#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
匿名编号批量删除功能静态代码测试
检查代码实现的完整性和正确性
"""

import os
import re


class StaticCodeTest:
    """静态代码测试类"""
    
    def __init__(self):
        self.base_path = "/mnt/d/code/newmachinecode/UniversalStaffEvaluation3"
        self.test_results = []
        
    def log_result(self, test_name, success, message):
        """记录测试结果"""
        status = "✅" if success else "❌"
        result = f"{status} {test_name}: {message}"
        print(result)
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })
    
    def test_views_implementation(self):
        """测试views_anonymous.py中的删除视图实现"""
        try:
            views_file = os.path.join(self.base_path, "organizations/views_anonymous.py")
            with open(views_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查必要的类是否存在
            required_classes = [
                'class AnonymousCodeDeleteView',
                'class AnonymousCodeBatchDeleteView'
            ]
            
            missing_classes = []
            for class_name in required_classes:
                if class_name not in content:
                    missing_classes.append(class_name)
            
            if missing_classes:
                self.log_result(
                    "后端视图类实现测试",
                    False,
                    f"缺少类定义: {missing_classes}"
                )
                return False
            
            # 检查必要的方法是否存在
            required_methods = [
                'def post(self, request, staff_id)',
                'def post(self, request)',
                'def _check_data_integrity(self, staff)'
            ]
            
            missing_methods = []
            for method in required_methods:
                if method not in content:
                    missing_methods.append(method)
            
            if missing_methods:
                self.log_result(
                    "后端视图类实现测试",
                    False,
                    f"缺少方法定义: {missing_methods}"
                )
                return False
            
            # 检查权限装饰器
            if '@method_decorator(require_permission(Permission.SYS_MANAGE_SETTINGS))' not in content:
                self.log_result(
                    "后端视图类实现测试",
                    False,
                    "缺少权限装饰器"
                )
                return False
            
            # 检查审计日志记录
            if 'AuditLog.objects.create' not in content:
                self.log_result(
                    "后端视图类实现测试",
                    False,
                    "缺少审计日志记录"
                )
                return False
            
            self.log_result(
                "后端视图类实现测试",
                True,
                "所有必要的视图类、方法和安全特性都已实现"
            )
            return True
            
        except Exception as e:
            self.log_result(
                "后端视图类实现测试",
                False,
                f"文件读取失败: {e}"
            )
            return False
    
    def test_urls_configuration(self):
        """测试URL配置"""
        try:
            urls_file = os.path.join(self.base_path, "organizations/urls.py")
            with open(urls_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查必要的URL路由是否存在
            required_urls = [
                "path('api/anonymous-codes/<int:staff_id>/delete/', views_anonymous.AnonymousCodeDeleteView.as_view(), name='anonymous_code_delete')",
                "path('api/anonymous-codes/batch-delete/', views_anonymous.AnonymousCodeBatchDeleteView.as_view(), name='anonymous_code_batch_delete')"
            ]
            
            missing_urls = []
            for url in required_urls:
                if url not in content:
                    missing_urls.append(url)
            
            if missing_urls:
                self.log_result(
                    "URL路由配置测试",
                    False,
                    f"缺少URL路由: {len(missing_urls)} 个"
                )
                return False
            
            self.log_result(
                "URL路由配置测试",
                True,
                "所有必要的URL路由都已正确配置"
            )
            return True
            
        except Exception as e:
            self.log_result(
                "URL路由配置测试",
                False,
                f"文件读取失败: {e}"
            )
            return False
    
    def test_template_implementation(self):
        """测试模板实现"""
        try:
            template_file = os.path.join(self.base_path, "templates/admin/anonymous/manage.html")
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查必要的HTML元素
            required_elements = [
                'id="batchDeleteAnonymousBtn"',
                'id="anonymousDeleteModal"',
                'id="batchAnonymousDeleteModal"',
                'id="selectedAnonymousCount"',
                'onclick="deleteAnonymousCode(',
                'onclick="showBatchDeleteAnonymousConfirm()"'
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                self.log_result(
                    "前端模板实现测试",
                    False,
                    f"缺少HTML元素: {len(missing_elements)} 个"
                )
                return False
            
            # 检查JavaScript函数
            required_js_functions = [
                'function deleteAnonymousCode',
                'function showBatchDeleteAnonymousConfirm',
                'function confirmAnonymousDelete',
                'function confirmBatchAnonymousDelete',
                'function updateSelectedAnonymousCount'
            ]
            
            missing_functions = []
            for func in required_js_functions:
                if func not in content:
                    missing_functions.append(func)
            
            if missing_functions:
                self.log_result(
                    "前端模板实现测试",
                    False,
                    f"缺少JavaScript函数: {len(missing_functions)} 个"
                )
                return False
            
            self.log_result(
                "前端模板实现测试",
                True,
                "所有必要的HTML元素和JavaScript函数都已实现"
            )
            return True
            
        except Exception as e:
            self.log_result(
                "前端模板实现测试",
                False,
                f"文件读取失败: {e}"
            )
            return False
    
    def test_code_quality(self):
        """测试代码质量"""
        try:
            views_file = os.path.join(self.base_path, "organizations/views_anonymous.py")
            with open(views_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            quality_checks = []
            
            # 检查错误处理
            if 'try:' in content and 'except Exception as e:' in content:
                quality_checks.append("✅ 包含异常处理")
            else:
                quality_checks.append("❌ 缺少异常处理")
            
            # 检查事务处理
            if 'with transaction.atomic():' in content:
                quality_checks.append("✅ 包含事务处理")
            else:
                quality_checks.append("❌ 缺少事务处理")
            
            # 检查日志记录
            if 'logger.error' in content:
                quality_checks.append("✅ 包含错误日志记录")
            else:
                quality_checks.append("❌ 缺少错误日志记录")
            
            # 检查参数验证
            if 'if not' in content and 'return JsonResponse' in content:
                quality_checks.append("✅ 包含参数验证")
            else:
                quality_checks.append("❌ 缺少参数验证")
            
            failed_checks = [check for check in quality_checks if check.startswith("❌")]
            
            if failed_checks:
                self.log_result(
                    "代码质量测试",
                    False,
                    f"质量检查失败项: {len(failed_checks)} 个"
                )
                for check in quality_checks:
                    print(f"  {check}")
                return False
            else:
                self.log_result(
                    "代码质量测试",
                    True,
                    "所有代码质量检查都通过"
                )
                for check in quality_checks:
                    print(f"  {check}")
                return True
            
        except Exception as e:
            self.log_result(
                "代码质量测试",
                False,
                f"文件读取失败: {e}"
            )
            return False
    
    def test_security_features(self):
        """测试安全特性"""
        try:
            views_file = os.path.join(self.base_path, "organizations/views_anonymous.py")
            with open(views_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            security_features = []
            
            # 检查权限验证
            if 'require_permission(Permission.SYS_MANAGE_SETTINGS)' in content:
                security_features.append("✅ 权限验证")
            else:
                security_features.append("❌ 缺少权限验证")
            
            # 检查数据完整性检查
            if '_check_data_integrity' in content:
                security_features.append("✅ 数据完整性检查")
            else:
                security_features.append("❌ 缺少数据完整性检查")
            
            # 检查强制删除选项
            if 'force_delete' in content:
                security_features.append("✅ 强制删除选项")
            else:
                security_features.append("❌ 缺少强制删除选项")
            
            # 检查审计日志
            if 'AuditLog.objects.create' in content:
                security_features.append("✅ 审计日志记录")
            else:
                security_features.append("❌ 缺少审计日志记录")
            
            failed_features = [feature for feature in security_features if feature.startswith("❌")]
            
            if failed_features:
                self.log_result(
                    "安全特性测试",
                    False,
                    f"安全特性缺失: {len(failed_features)} 个"
                )
                for feature in security_features:
                    print(f"  {feature}")
                return False
            else:
                self.log_result(
                    "安全特性测试",
                    True,
                    "所有安全特性都已实现"
                )
                for feature in security_features:
                    print(f"  {feature}")
                return True
            
        except Exception as e:
            self.log_result(
                "安全特性测试",
                False,
                f"文件读取失败: {e}"
            )
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始静态代码测试...\n")
        
        tests = [
            self.test_views_implementation,
            self.test_urls_configuration,
            self.test_template_implementation,
            self.test_code_quality,
            self.test_security_features
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
            print()  # 空行分隔
        
        print("="*60)
        print(f"📊 测试结果汇总:")
        print(f"总测试数: {total}")
        print(f"通过测试: {passed}")
        print(f"失败测试: {total - passed}")
        print(f"成功率: {(passed/total)*100:.1f}%")
        print("="*60)
        
        if passed == total:
            print("🎉 所有静态测试通过！代码实现正确完整。")
        else:
            print("⚠️  部分测试失败，请检查上述失败的测试项。")
        
        return passed == total


def main():
    """主函数"""
    print("匿名编号批量删除功能 - 静态代码测试")
    print("="*60)
    
    tester = StaticCodeTest()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ 静态代码测试完成，实现质量良好。")
        print("\n📋 下一步测试建议:")
        print("1. 检查数据库迁移是否需要执行")
        print("2. 验证模板文件路径是否正确")
        print("3. 测试API端点的HTTP响应")
        print("4. 进行手动功能测试")
    else:
        print("\n❌ 发现代码实现问题，请先修复失败的测试项。")
    
    return 0 if success else 1


if __name__ == '__main__':
    import sys
    sys.exit(main())