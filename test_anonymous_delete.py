#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
匿名编号删除功能测试
测试匿名编号删除的前后端集成
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.urls import reverse
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from organizations.models import Staff, Department
from organizations.views_anonymous import AnonymousCodeDeleteView
from common.models import AuditLog

class AnonymousDeleteTest:
    def __init__(self):
        self.client = Client()
        self.setup_test_data()
    
    def setup_test_data(self):
        """设置测试数据"""
        print("🔧 设置匿名编号删除测试数据...")
        
        # 创建测试部门
        self.test_dept = Department.objects.get_or_create(
            name="测试部门",
            defaults={
                'dept_code': 'TEST',
                'description': '用于匿名编号删除测试的部门'
            }
        )[0]
        
        # 创建有新匿名编号的测试员工
        self.test_staff_with_code = Staff.objects.get_or_create(
            employee_no="ANON001",
            defaults={
                'name': '匿名测试员工',
                'department': self.test_dept,
                'email': '<EMAIL>',
                'is_active': True,
                'new_anonymous_code': 'TEST-1234-5678-9ABC',  # 有新匿名编号
                'anonymous_code': 'old_code_123',  # 旧编号
                'anonymous_code_version': 'v2.0'
            }
        )[0]
        
        # 如果员工已存在但没有新匿名编号，添加一个
        if not self.test_staff_with_code.new_anonymous_code:
            self.test_staff_with_code.new_anonymous_code = 'TEST-1234-5678-9ABC'
            self.test_staff_with_code.anonymous_code_version = 'v2.0'
            self.test_staff_with_code.save()
        
        # 创建没有新匿名编号的测试员工
        self.test_staff_no_code = Staff.objects.get_or_create(
            employee_no="ANON002",
            defaults={
                'name': '无编号员工',
                'department': self.test_dept,
                'email': '<EMAIL>',
                'is_active': True,
                'new_anonymous_code': None,  # 没有新匿名编号
                'anonymous_code': 'old_code_456',  # 只有旧编号
                'anonymous_code_version': 'v1.0'
            }
        )[0]
        
        print(f"✅ 测试数据创建完成:")
        print(f"   - 有新编号员工: {self.test_staff_with_code.name} (编号: {self.test_staff_with_code.new_anonymous_code})")
        print(f"   - 无新编号员工: {self.test_staff_no_code.name} (编号: {self.test_staff_no_code.new_anonymous_code})")
    
    def test_anonymous_delete_url_resolution(self):
        """测试匿名编号删除URL解析"""
        print("\n📋 测试1: 匿名编号删除URL解析")
        
        try:
            delete_url = reverse('organizations:admin:anonymous_code_delete', kwargs={'staff_id': self.test_staff_with_code.id})
            expected_pattern = f'/organizations/admin/api/anonymous-codes/{self.test_staff_with_code.id}/delete/'
            
            print(f"   生成的URL: {delete_url}")
            print(f"   期望的模式: {expected_pattern}")
            
            if delete_url == expected_pattern:
                print("   ✅ URL解析正确")
                return True
            else:
                print("   ❌ URL解析错误")
                return False
                
        except Exception as e:
            print(f"   ❌ URL解析失败: {e}")
            return False
    
    def test_anonymous_delete_view_exists(self):
        """测试匿名编号删除视图是否存在"""
        print("\n📋 测试2: 匿名编号删除视图存在性")
        
        try:
            view = AnonymousCodeDeleteView()
            print("   ✅ AnonymousCodeDeleteView类存在")
            
            # 检查视图方法
            if hasattr(view, 'post'):
                print("   ✅ post方法存在")
            else:
                print("   ❌ post方法不存在")
                return False
            
            if hasattr(view, '_check_data_integrity'):
                print("   ✅ _check_data_integrity方法存在")
            else:
                print("   ❌ _check_data_integrity方法不存在")
                return False
                
            return True
                
        except Exception as e:
            print(f"   ❌ 视图检查失败: {e}")
            return False
    
    def test_staff_with_new_code(self):
        """测试有新匿名编号的员工数据"""
        print("\n📋 测试3: 员工新匿名编号数据")
        
        staff = Staff.objects.get(id=self.test_staff_with_code.id)
        
        print(f"   员工姓名: {staff.name}")
        print(f"   员工编号: {staff.employee_no}")
        print(f"   新匿名编号: {staff.new_anonymous_code}")
        print(f"   旧匿名编号: {staff.anonymous_code}")
        print(f"   编号版本: {staff.anonymous_code_version}")
        
        if staff.new_anonymous_code:
            print("   ✅ 该员工有新匿名编号，可以删除")
            return True
        else:
            print("   ❌ 该员工没有新匿名编号")
            return False
    
    def test_data_integrity_check(self):
        """测试数据完整性检查方法"""
        print("\n📋 测试4: 数据完整性检查")
        
        try:
            view = AnonymousCodeDeleteView()
            integrity_result = view._check_data_integrity(self.test_staff_with_code)
            
            print(f"   完整性检查结果: {integrity_result}")
            print(f"   是否有冲突: {integrity_result.get('has_conflicts', False)}")
            
            if 'conflicts' in integrity_result:
                print(f"   冲突列表: {integrity_result['conflicts']}")
            
            print("   ✅ 数据完整性检查方法工作正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 数据完整性检查失败: {e}")
            print(f"   错误详情: {type(e).__name__}: {str(e)}")
            return False
    
    def test_delete_api_call_simulation(self):
        """测试删除API调用模拟"""
        print("\n📋 测试5: 删除API调用模拟")
        
        try:
            delete_url = reverse('organizations:admin:anonymous_code_delete', kwargs={'staff_id': self.test_staff_with_code.id})
            
            # 模拟POST请求数据
            request_data = {
                'reason': '测试删除匿名编号',
                'force_delete': False
            }
            
            print(f"   请求URL: {delete_url}")
            print(f"   请求数据: {request_data}")
            
            # 发送模拟请求（注意：可能因为权限问题失败，但不应该404）
            response = self.client.post(
                delete_url,
                data=json.dumps(request_data),
                content_type='application/json'
            )
            
            print(f"   响应状态码: {response.status_code}")
            
            if response.status_code == 404:
                print("   ❌ URL不存在 (404错误)")
                return False
            elif response.status_code in [403, 401]:
                print("   ℹ️  权限问题 (预期的，因为没有登录)")
                return True
            elif response.status_code == 400:
                print("   ℹ️  请求格式问题 (可能是CSRF)")
                return True
            else:
                print(f"   ✅ API端点存在，状态码: {response.status_code}")
                try:
                    response_data = response.json()
                    print(f"   响应数据: {response_data}")
                except:
                    print(f"   响应内容: {response.content[:200]}")
                return True
                
        except Exception as e:
            print(f"   ❌ API调用测试失败: {e}")
            return False
    
    def test_frontend_dom_structure(self):
        """测试前端DOM结构模拟"""
        print("\n📋 测试6: 前端DOM结构验证")
        
        # 模拟前端DOM结构检查
        print("   模拟检查前端DOM选择器...")
        
        # 检查表格结构
        expected_columns = [
            '复选框',
            '员工信息', 
            '旧编号',
            '新安全编号',
            '状态',
            '生成时间',
            '操作'
        ]
        
        print(f"   期望的表格列: {expected_columns}")
        print("   DOM选择器说明:")
        print("   - td:nth-child(2): 员工信息列 (包含姓名和编号·部门)")
        print("   - td:nth-child(4): 新安全编号列")
        print("   - 员工信息格式: 'XX001 · 测试部门'")
        
        print("   ✅ DOM结构验证完成")
        return True
    
    def cleanup_test_data(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        try:
            # 删除测试员工（软删除）
            if hasattr(self.test_staff_with_code, 'id'):
                self.test_staff_with_code.soft_delete(deleted_by='test_cleanup')
                print(f"   ✅ 有编号测试员工已软删除: {self.test_staff_with_code.name}")
            
            if hasattr(self.test_staff_no_code, 'id'):
                self.test_staff_no_code.soft_delete(deleted_by='test_cleanup')
                print(f"   ✅ 无编号测试员工已软删除: {self.test_staff_no_code.name}")
                
        except Exception as e:
            print(f"   ⚠️  清理警告: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始匿名编号删除功能测试\n")
        
        tests = [
            self.test_anonymous_delete_url_resolution,
            self.test_anonymous_delete_view_exists,
            self.test_staff_with_new_code,
            self.test_data_integrity_check,
            self.test_delete_api_call_simulation,
            self.test_frontend_dom_structure
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        print(f"\n📊 测试结果统计:")
        print(f"   ✅ 通过: {passed}/{total}")
        print(f"   ❌ 失败: {total - passed}/{total}")
        print(f"   📈 成功率: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 所有测试通过！匿名编号删除功能基础组件正常！")
        else:
            print(f"\n⚠️  还有 {total - passed} 个问题需要解决")
        
        # 清理测试数据
        self.cleanup_test_data()
        
        return passed == total

if __name__ == '__main__':
    tester = AnonymousDeleteTest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)