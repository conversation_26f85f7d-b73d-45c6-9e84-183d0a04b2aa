#!/usr/bin/env python
"""
测试模型修复是否成功
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
sys.path.append('/mnt/d/code/newmachinecode/UniversalStaffEvaluation3')

django.setup()

# 导入模型进行测试
try:
    from evaluations.models import EvaluationBatch, EvaluationTemplate, BatchTemplate
    print("✅ 模型导入成功！")
    
    # 测试字段定义
    batch_fields = [field.name for field in EvaluationBatch._meta.get_fields()]
    print(f"✅ EvaluationBatch 字段: {batch_fields}")
    
    template_fields = [field.name for field in EvaluationTemplate._meta.get_fields()]
    print(f"✅ EvaluationTemplate 字段: {template_fields}")
    
    batch_template_fields = [field.name for field in BatchTemplate._meta.get_fields()]
    print(f"✅ BatchTemplate 字段: {batch_template_fields}")
    
    print("✅ 模型关系修复成功，可以进行迁移！")
    
except Exception as e:
    print(f"❌ 模型导入失败: {e}")
    sys.exit(1)