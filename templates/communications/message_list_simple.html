{% extends "admin/base_admin.html" %}

{% block page_title %}消息中心{% endblock %}
{% block page_description %}查看和管理您的消息通知{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <button type="button" class="btn btn-danger btn-md hidden" id="batchDeleteBtn" onclick="batchDeleteMessages()">
        <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
        <span>批量删除</span>
    </button>
    <button type="button" class="btn btn-primary btn-md" onclick="openComposeModal()">
        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
        撰写消息
    </button>
</div>
{% endblock %}

{% block admin_content %}

    <!-- 消息统计卡片 -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- 全部消息 -->
    <div class="card card-body">
        <div class="flex items-center">
            <div class="p-2 bg-gray-100 rounded-lg">
                <i data-lucide="mail" class="w-6 h-6 text-gray-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">全部消息</p>
                <p class="text-2xl font-bold text-gray-900" id="total-messages">{{ stats.total|default:0 }}</p>
            </div>
        </div>
    </div>

    <!-- 未读消息 -->
    <div class="card card-body">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="mail-open" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">未读消息</p>
                <p class="text-2xl font-bold text-gray-900" id="unread-messages">{{ stats.unread|default:0 }}</p>
            </div>
        </div>
    </div>

    <!-- 已读消息 -->
    <div class="card card-body">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">已读消息</p>
                <p class="text-2xl font-bold text-gray-900" id="read-messages">{{ stats.read|default:0 }}</p>
            </div>
        </div>
    </div>

    <!-- 星标消息 -->
    <div class="card card-body">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="star" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">星标消息</p>
                <p class="text-2xl font-bold text-gray-900" id="starred-messages">{{ stats.starred|default:0 }}</p>
            </div>
        </div>
    </div>
    </div>

    <!-- 消息列表 -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="inbox" class="w-5 h-5 mr-2 text-gray-500"></i>
                消息列表
            </h3>
            <div class="flex items-center space-x-4">
                <input type="text" id="searchInput" placeholder="搜索消息..." 
                       class="form-input w-64" onkeyup="searchMessages()">
                <button type="button" class="btn btn-success btn-sm" onclick="loadMessages()">
                    <i data-lucide="refresh-cw" class="w-4 h-4 mr-1"></i>
                    刷新
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" class="rounded border-gray-300">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发送者</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主题</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">优先级</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="messageTableBody" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                                <i data-lucide="loader" class="w-8 h-8 mx-auto mb-2 animate-spin"></i>
                                <p>正在加载消息...</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 模态框 -->
<div id="composeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">撰写消息</h3>
            </div>
            <div class="px-6 py-4">
                <p class="text-gray-600">消息撰写功能正在开发中...</p>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button type="button" class="btn btn-secondary btn-sm" onclick="closeModal('composeModal')">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
// 全局消息管理器
window.messageManager = {
    currentData: [],
    currentPage: 1,
    pageSize: 20,
    
    async loadMessages() {
        try {
            showLoading();
            
            // 使用统一的API路径
            const response = await fetch('/communications/api/messages/', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}`,
                    'X-CSRFToken': getCsrfToken()
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.renderMessages(data.messages || []);
                this.updateStats();
                showSuccess('消息加载成功');
            } else {
                throw new Error(data.error || '加载失败');
            }
        } catch (error) {
            console.error('加载消息失败:', error);
            this.renderEmptyState('加载失败：' + error.message);
            showError('加载失败', error.message);
        }
    },
    
    renderMessages(messages) {
        const tbody = document.getElementById('messageTableBody');
        
        if (!messages || messages.length === 0) {
            this.renderEmptyState();
            return;
        }
        
        tbody.innerHTML = messages.map(msg => `
            <tr class="hover:bg-gray-50 ${msg.is_read ? '' : 'bg-blue-50'}">
                <td class="px-6 py-4 whitespace-nowrap">
                    <input type="checkbox" class="message-checkbox rounded border-gray-300" value="${msg.id}" onchange="updateBatchDeleteButton()">
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                            <i data-lucide="user" class="w-4 h-4 text-gray-500"></i>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-900">${msg.sender.name}</div>
                            ${msg.sender.employee_no ? `<div class="text-xs text-gray-500">${msg.sender.employee_no}</div>` : ''}
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4">
                    <div class="text-sm font-medium text-gray-900 truncate max-w-xs">${msg.subject}</div>
                    <div class="text-xs text-gray-500 truncate max-w-xs">${msg.content}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="badge badge-${this.getPriorityColor(msg.priority)} badge-sm">
                        ${msg.priority_display}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${this.formatDate(msg.created_at)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="badge ${msg.is_read ? 'badge-success' : 'badge-warning'} badge-sm">
                        ${msg.is_read ? '已读' : '未读'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="viewMessage(${msg.id})" class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                    ${!msg.is_read ? `<button onclick="markAsRead(${msg.id})" class="text-green-600 hover:text-green-900 mr-3">标记已读</button>` : ''}
                    <button onclick="deleteMessage(${msg.id})" class="text-red-600 hover:text-red-900">删除</button>
                </td>
            </tr>
        `).join('');
        
        // 重新初始化图标
        if (window.lucide) {
            lucide.createIcons({ nameAttr: 'data-lucide' });
        }
    },
    
    renderEmptyState(message = '暂无消息') {
        const tbody = document.getElementById('messageTableBody');
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                    <i data-lucide="mail-x" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                    <p>${message}</p>
                </td>
            </tr>
        `;
        
        if (window.lucide) {
            lucide.createIcons({ nameAttr: 'data-lucide' });
        }
    },
    
    updateStats() {
        // 这里可以从API获取统计数据更新
    },
    
    getPriorityColor(priority) {
        const colors = {
            'low': 'secondary',
            'normal': 'primary', 
            'high': 'warning',
            'urgent': 'danger'
        };
        return colors[priority] || 'primary';
    },
    
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) {
            return '刚刚';
        } else if (diff < 3600000) {
            return `${Math.floor(diff / 60000)}分钟前`;
        } else if (diff < 86400000) {
            return `${Math.floor(diff / 3600000)}小时前`;
        } else if (diff < 86400000 * 7) {
            return `${Math.floor(diff / 86400000)}天前`;
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    }
};

// 全局函数
function loadMessages() {
    window.messageManager.loadMessages();
}

function searchMessages() {
    const query = document.getElementById('searchInput').value;
    // 实现搜索逻辑
    console.log('搜索:', query);
}

function viewMessage(messageId) {
    showInfo('查看消息', `正在查看消息 ${messageId}`);
}

function markAsRead(messageId) {
    showInfo('标记已读', `正在标记消息 ${messageId} 为已读`);
}

function deleteMessage(messageId) {
    window.confirmDelete({
        title: '删除消息',
        message: '确定要删除这条消息吗？删除后将无法恢复。',
        url: `/communications/api/messages/${messageId}/delete/`,
        method: 'DELETE',
        onSuccess: function(result) {
            // 成功后重新加载消息列表
            window.messageManager.loadMessages();
        },
        onError: function(error) {
            console.error('删除消息失败:', error);
        }
    });
}

function openComposeModal() {
    document.getElementById('composeModal').classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// 批量删除相关功能
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.message-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateBatchDeleteButton();
}

function updateBatchDeleteButton() {
    const checkboxes = document.querySelectorAll('.message-checkbox:checked');
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    
    if (checkboxes.length > 0) {
        batchDeleteBtn.classList.remove('hidden');
        batchDeleteBtn.querySelector('span').textContent = `批量删除 (${checkboxes.length})`;
    } else {
        batchDeleteBtn.classList.add('hidden');
    }
}

function batchDeleteMessages() {
    const checkboxes = document.querySelectorAll('.message-checkbox:checked');
    const selectedIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
    
    if (selectedIds.length === 0) {
        return;
    }
    
    window.confirmBatchDelete({
        title: '批量删除消息',
        message: `确定要删除选中的 ${selectedIds.length} 条消息吗？删除后将无法恢复。`,
        items: selectedIds,
        url: '/communications/api/messages/batch-delete/',
        onSuccess: function(result) {
            // 成功后重新加载消息列表
            window.messageManager.loadMessages();
            // 重置选择状态
            document.getElementById('selectAll').checked = false;
            updateBatchDeleteButton();
        },
        onError: function(error) {
            console.error('批量删除消息失败:', error);
        }
    });
}

// 工具函数
function getCsrfToken() {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
            return value;
        }
    }
    return '';
}

function showLoading() {
    const tbody = document.getElementById('messageTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                <i data-lucide="loader" class="w-8 h-8 mx-auto mb-2 animate-spin"></i>
                <p>正在加载消息...</p>
            </td>
        </tr>
    `;
    
    if (window.lucide) {
        lucide.createIcons({ nameAttr: 'data-lucide' });
    }
}

function showSuccess(message) {
    console.log('成功:', message);
    // 这里可以显示成功通知
}

function showError(title, message) {
    console.error('错误:', title, message);
    // 这里可以显示错误通知
}

function showInfo(title, message) {
    console.log('信息:', title, message);
    // 这里可以显示信息通知
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图标
    if (window.lucide) {
        lucide.createIcons({ nameAttr: 'data-lucide' });
    }
    
    // 加载消息数据
    window.messageManager.loadMessages();
});
</script>
{% endblock %}