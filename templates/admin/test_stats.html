{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}统计数据测试{% endblock %}
{% block page_description %}验证各页面统计数据的准确性{% endblock %}

{% block admin_content %}
<div class="space-y-6">
    <!-- 数据库统计 -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4">数据库统计（实时查询）</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600">{{ db_stats.total_staff }}</div>
                <div class="text-sm text-gray-600">总员工数</div>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600">{{ db_stats.active_staff }}</div>
                <div class="text-sm text-gray-600">在职员工</div>
            </div>
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                <div class="text-2xl font-bold text-yellow-600">{{ db_stats.admin_staff }}</div>
                <div class="text-sm text-gray-600">管理人员</div>
            </div>
            <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600">{{ db_stats.total_departments }}</div>
                <div class="text-sm text-gray-600">部门数量</div>
            </div>
        </div>
    </div>

    <!-- 职位统计 -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4">职位统计（实时查询）</h3>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600">{{ position_stats.total_positions }}</div>
                <div class="text-sm text-gray-600">总职位数</div>
            </div>
            <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600">{{ position_stats.management_count }}</div>
                <div class="text-sm text-gray-600">管理岗位</div>
            </div>
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                <div class="text-2xl font-bold text-yellow-600">{{ position_stats.department_manager_count }}</div>
                <div class="text-sm text-gray-600">部门经理</div>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600">{{ position_stats.max_level }}</div>
                <div class="text-sm text-gray-600">最高级别</div>
            </div>
            <div class="text-center p-4 bg-indigo-50 rounded-lg">
                <div class="text-2xl font-bold text-indigo-600">{{ position_stats.departments_covered }}</div>
                <div class="text-sm text-gray-600">部门覆盖</div>
            </div>
        </div>
    </div>

    <!-- 部门统计 -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4">部门统计（实时查询）</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600">{{ department_stats.total_departments }}</div>
                <div class="text-sm text-gray-600">总部门数</div>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600">{{ department_stats.active_departments }}</div>
                <div class="text-sm text-gray-600">活跃部门</div>
            </div>
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                <div class="text-2xl font-bold text-yellow-600">{{ department_stats.departments_with_staff }}</div>
                <div class="text-sm text-gray-600">有员工部门</div>
            </div>
            <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600">{{ department_stats.departments_with_manager }}</div>
                <div class="text-sm text-gray-600">有经理部门</div>
            </div>
        </div>
    </div>

    <!-- 详细数据 -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4">详细数据验证</h3>
        <div class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 员工角色分布 -->
                <div>
                    <h4 class="font-medium mb-2">员工角色分布</h4>
                    <div class="space-y-2">
                        {% for role_code, role_name in role_choices %}
                        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span class="text-sm">{{ role_name }}</span>
                            <span class="font-medium">{{ role_distribution|default_if_none:role_code|default:0 }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- 部门员工分布 -->
                <div>
                    <h4 class="font-medium mb-2">部门员工分布（前10）</h4>
                    <div class="space-y-2">
                        {% for dept in department_distribution %}
                        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span class="text-sm">{{ dept.name }}</span>
                            <span class="font-medium">{{ dept.staff_count }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 刷新按钮 -->
    <div class="text-center">
        <button onclick="location.reload()" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            <i data-lucide="refresh-cw" class="w-4 h-4 mr-2 inline"></i>
            刷新数据
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    console.log('统计数据测试页面已加载');
    console.log('数据库统计:', {
        total_staff: {{ db_stats.total_staff }},
        active_staff: {{ db_stats.active_staff }},
        admin_staff: {{ db_stats.admin_staff }},
        total_departments: {{ db_stats.total_departments }}
    });
});
</script>
{% endblock %}
