{% extends "admin/base_admin.html" %}

{% block page_title %}评分项管理{% endblock %}
{% block page_description %}{{ template.name }} - 管理模板的评分项目{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'evaluations:admin:template_detail' template.pk %}" 
       class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700">
        <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>返回模板详情
    </a>
    <a href="{% url 'evaluations:admin:item_create' template.pk %}" 
       class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
        <i data-lucide="plus" class="w-4 h-4 mr-2 inline"></i>添加评分项
    </a>
</div>
{% endblock %}

{% block admin_content %}
<!-- 模板信息 -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">模板信息</h3>
    </div>
    <div class="px-6 py-4">
        <div class="grid grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700">模板名称</label>
                <p class="mt-1 text-sm text-gray-900">{{ template.name }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">模板类型</label>
                <p class="mt-1 text-sm text-gray-900">{{ template.get_template_type_display }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">评分项数量</label>
                <p class="mt-1 text-sm text-gray-900">{{ items|length }} 个</p>
            </div>
        </div>
    </div>
</div>

<!-- 评分项列表 -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">评分项列表</h3>
            <div class="text-sm text-gray-500">
                共 {{ items|length }} 个评分项
            </div>
        </div>
    </div>
    
    {% if items %}
    <div class="overflow-x-auto">
        <table class="min-w-full">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">评分项名称</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">评分方式</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分值范围</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">权重</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">必填</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for item in items %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center justify-center w-6 h-6 bg-gray-100 text-gray-700 rounded-full text-sm font-medium">
                            {{ item.sort_order|default:forloop.counter }}
                        </span>
                    </td>
                    <td class="px-6 py-4">
                        <div>
                            <p class="font-medium text-gray-900">{{ item.name }}</p>
                            {% if item.description %}
                            <p class="text-sm text-gray-500 mt-1">{{ item.description|truncatechars:50 }}</p>
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {% if item.scoring_mode == 'score' %}bg-blue-100 text-blue-800
                            {% elif item.scoring_mode == 'level' %}bg-green-100 text-green-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ item.get_scoring_mode_display }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {% if item.scoring_mode == 'score' %}
                            0 - {{ item.max_score }}
                        {% elif item.scoring_mode == 'level' %}
                            {{ item.scoringtier_set.count }} 个等级
                        {% else %}
                            文本评价
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ item.weight }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if item.is_required %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                必填
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                选填
                            </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if item.is_active %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                启用
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                禁用
                            </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex items-center space-x-2">
                            <a href="{% url 'evaluations:admin:item_update' template.pk item.pk %}" 
                               class="text-blue-600 hover:text-blue-900">编辑</a>
                            <button onclick="deleteItem({{ item.pk }}, '{{ item.name }}')" 
                                    class="text-red-600 hover:text-red-900">删除</button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <!-- 空状态 -->
    <div class="text-center py-12">
        <i data-lucide="list-x" class="w-16 h-16 text-gray-300 mx-auto mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无评分项</h3>
        <p class="text-gray-500 mb-4">该模板还没有配置任何评分项目。</p>
        <a href="{% url 'evaluations:admin:item_create' template.pk %}" 
           class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 inline-flex items-center">
            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>添加评分项
        </a>
    </div>
    {% endif %}
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="mt-8 flex items-center justify-between">
    <div class="text-sm text-gray-700">
        显示第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 项，共 {{ paginator.count }} 项
    </div>
    <nav class="flex items-center space-x-2">
        {% if page_obj.has_previous %}
            <a href="?page=1" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">首页</a>
            <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</a>
        {% endif %}
        
        <span class="px-3 py-2 text-sm bg-blue-50 border border-blue-200 rounded-md text-blue-600">
            {{ page_obj.number }}
        </span>
        
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</a>
            <a href="?page={{ paginator.num_pages }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">末页</a>
        {% endif %}
    </nav>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // 删除评分项
    function deleteItem(itemId, itemName) {
        if (confirm(`确定要删除评分项"${itemName}"吗？此操作不可恢复。`)) {
            fetch(`{% url 'evaluations:admin:item_delete' template.pk 0 %}`.replace('0', itemId), {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => {
                if (response.ok) {
                    if (typeof showNotification === 'function') {
                        showNotification('评分项删除成功', 'success');
                    }
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    if (typeof showNotification === 'function') {
                        showNotification('删除失败，请重试', 'error');
                    } else {
                        alert('删除失败，请重试');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (typeof showNotification === 'function') {
                    showNotification('删除失败，请重试', 'error');
                } else {
                    alert('删除失败，请重试');
                }
            });
        }
    }
</script>
{% endblock %}
