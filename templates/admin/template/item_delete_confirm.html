{% extends "admin/base_admin.html" %}

{% block page_title %}删除评分项{% endblock %}
{% block page_description %}确认删除评分项 "{{ object.name }}"{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'evaluations:admin:item_list' template.pk %}" 
       class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700">
        <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>返回评分项列表
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-2xl mx-auto">
    <!-- 警告提示 -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i data-lucide="alert-triangle" class="w-6 h-6 text-red-600"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-medium text-red-800">确认删除操作</h3>
                <div class="mt-2 text-sm text-red-700">
                    <p>您即将删除评分项 <strong>"{{ object.name }}"</strong>。</p>
                    <p class="mt-2">此操作将会：</p>
                    <ul class="list-disc list-inside mt-2 space-y-1">
                        <li>永久删除该评分项及其配置</li>
                        {% if object.scoring_mode == 'level' %}
                        <li>删除所有相关的评分等级设置</li>
                        {% endif %}
                        <li>删除所有相关的评分记录</li>
                        <li>此操作不可恢复</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 评分项信息 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">评分项信息</h3>
        </div>
        <div class="px-6 py-4 space-y-4">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">评分项名称</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.name }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">评分方式</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.get_scoring_mode_display }}</p>
                </div>
            </div>
            
            {% if object.description %}
            <div>
                <label class="block text-sm font-medium text-gray-700">评分说明</label>
                <p class="mt-1 text-sm text-gray-900">{{ object.description }}</p>
            </div>
            {% endif %}
            
            <div class="grid grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">最高分值</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.max_score }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">权重</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.weight }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">必填</label>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {% if object.is_required %}bg-red-100 text-red-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                        {% if object.is_required %}是{% else %}否{% endif %}
                    </span>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">状态</label>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {% if object.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {% if object.is_active %}启用{% else %}禁用{% endif %}
                    </span>
                </div>
            </div>

            {% if object.scoring_mode == 'level' and object.scoringtier_set.exists %}
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">评分等级</label>
                <div class="space-y-2">
                    {% for tier in object.scoringtier_set.all %}
                    <div class="flex items-center justify-between bg-gray-50 px-3 py-2 rounded">
                        <span class="text-sm text-gray-900">{{ tier.name }}</span>
                        <span class="text-sm text-gray-600">{{ tier.score }} 分</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 所属模板信息 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">所属模板</h3>
        </div>
        <div class="px-6 py-4">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">模板名称</label>
                    <p class="mt-1 text-sm text-gray-900">{{ template.name }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">模板类型</label>
                    <p class="mt-1 text-sm text-gray-900">{{ template.get_template_type_display }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认表单 -->
    <form method="post" class="bg-white rounded-lg shadow-sm border border-gray-200">
        {% csrf_token %}
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">确认删除</h3>
        </div>
        <div class="px-6 py-4">
            <div class="flex items-center space-x-2 mb-4">
                <input type="checkbox" id="confirmDelete" name="confirm_delete" required
                       class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
                <label for="confirmDelete" class="text-sm text-gray-700">
                    我确认要删除此评分项，并了解此操作不可恢复
                </label>
            </div>
            
            <div class="flex items-center justify-end space-x-3">
                <a href="{% url 'evaluations:admin:item_list' template.pk %}" 
                   class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                    取消
                </a>
                <button type="submit" id="deleteBtn" disabled
                        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed">
                    <i data-lucide="trash-2" class="w-4 h-4 mr-2 inline"></i>
                    确认删除
                </button>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 确认复选框控制删除按钮
    document.getElementById('confirmDelete').addEventListener('change', function() {
        const deleteBtn = document.getElementById('deleteBtn');
        deleteBtn.disabled = !this.checked;
    });
</script>
{% endblock %}
