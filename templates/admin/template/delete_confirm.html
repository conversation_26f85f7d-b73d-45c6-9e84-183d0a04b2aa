{% extends "admin/base_admin.html" %}

{% block page_title %}删除考评模板{% endblock %}
{% block page_description %}确认删除模板 "{{ object.name }}"{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'evaluations:admin:template_detail' object.pk %}" 
       class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700">
        <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>返回详情
    </a>
    <a href="{% url 'evaluations:admin:template_list' %}" 
       class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
        <i data-lucide="list" class="w-4 h-4 mr-2 inline"></i>返回列表
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-2xl mx-auto">
    <!-- 警告提示 -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i data-lucide="alert-triangle" class="w-6 h-6 text-red-600"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-medium text-red-800">确认删除操作</h3>
                <div class="mt-2 text-sm text-red-700">
                    <p>您即将删除考评模板 <strong>"{{ object.name }}"</strong>。</p>
                    <p class="mt-2">此操作将会：</p>
                    <ul class="list-disc list-inside mt-2 space-y-1">
                        <li>永久删除该模板及其所有评分项配置</li>
                        <li>删除所有相关的评分等级设置</li>
                        <li>此操作不可恢复</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 模板信息 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">模板信息</h3>
        </div>
        <div class="px-6 py-4 space-y-4">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">模板名称</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.name }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">模板类型</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.get_template_type_display }}</p>
                </div>
            </div>
            
            {% if object.description %}
            <div>
                <label class="block text-sm font-medium text-gray-700">模板描述</label>
                <p class="mt-1 text-sm text-gray-900">{{ object.description }}</p>
            </div>
            {% endif %}
            
            <div class="grid grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">评分项数量</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.evaluationitem_set.count }} 个</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">创建时间</label>
                    <p class="mt-1 text-sm text-gray-900">{{ object.created_at|date:"Y-m-d H:i" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">状态</label>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {% if object.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {% if object.is_active %}启用{% else %}禁用{% endif %}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认表单 -->
    <form method="post" class="bg-white rounded-lg shadow-sm border border-gray-200">
        {% csrf_token %}
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">确认删除</h3>
        </div>
        <div class="px-6 py-4">
            <div class="flex items-center space-x-2 mb-4">
                <input type="checkbox" id="confirmDelete" name="confirm_delete" required
                       class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
                <label for="confirmDelete" class="text-sm text-gray-700">
                    我确认要删除此模板，并了解此操作不可恢复
                </label>
            </div>
            
            <div class="flex items-center justify-end space-x-3">
                <a href="{% url 'evaluations:admin:template_detail' object.pk %}" 
                   class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                    取消
                </a>
                <button type="submit" id="deleteBtn" disabled
                        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed">
                    <i data-lucide="trash-2" class="w-4 h-4 mr-2 inline"></i>
                    确认删除
                </button>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 确认复选框控制删除按钮
    document.getElementById('confirmDelete').addEventListener('change', function() {
        const deleteBtn = document.getElementById('deleteBtn');
        deleteBtn.disabled = !this.checked;
    });
</script>
{% endblock %}
