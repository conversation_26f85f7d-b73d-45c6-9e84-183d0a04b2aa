{% extends "admin/base_admin.html" %}

{% block page_title %}编辑考评模板{% endblock %}
{% block page_description %}修改考评表单的基本信息和配置{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'evaluations:admin:template_detail' object.pk %}" 
       class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="eye" class="w-4 h-4"></i>
        <span>查看详情</span>
    </a>
    <a href="{% url 'evaluations:admin:template_list' %}" 
       class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <i data-lucide="file-text" class="w-6 h-6 text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">编辑考评模板</h3>
                    <p class="mt-1 text-sm text-gray-600">
                        修改 "{{ object.name }}" 的基本信息和配置设置
                    </p>
                </div>
            </div>
        </div>

        <form method="post" class="p-6 space-y-6" id="templateForm">
            {% csrf_token %}
            
            <!-- 基本信息 -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                    <i data-lucide="info" class="w-5 h-5 mr-2 text-gray-500"></i>
                    基本信息
                </h4>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 模板名称 -->
                    <div>
                        <label for="id_name" class="block text-sm font-medium text-gray-700 mb-2">
                            模板名称 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="id_name" required 
                               value="{{ object.name }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="请输入模板名称">
                        <p class="mt-1 text-xs text-gray-500">例如：月度绩效考评、年终综合评价等</p>
                    </div>

                    <!-- 模板类型 -->
                    <div>
                        <label for="id_template_type" class="block text-sm font-medium text-gray-700 mb-2">
                            模板类型 <span class="text-red-500">*</span>
                        </label>
                        <select name="template_type" id="id_template_type" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">请选择模板类型</option>
                            <option value="structured" {% if object.template_type == 'structured' %}selected{% endif %}>结构化评分 - 预设评分项和标准</option>
                            <option value="open" {% if object.template_type == 'open' %}selected{% endif %}>开放式评分 - 自由文本评价</option>
                            <option value="mixed" {% if object.template_type == 'mixed' %}selected{% endif %}>混合式评分 - 结构化+开放式</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">选择不同类型将影响评分方式和界面</p>
                    </div>

                    <!-- 模板描述 -->
                    <div class="lg:col-span-2">
                        <label for="id_description" class="block text-sm font-medium text-gray-700 mb-2">
                            模板描述
                        </label>
                        <textarea name="description" id="id_description" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="请输入模板的详细描述和使用说明">{{ object.description }}</textarea>
                        <p class="mt-1 text-xs text-gray-500">描述模板的用途、适用范围和注意事项</p>
                    </div>
                </div>
            </div>

            <!-- 模板设置 -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                    <i data-lucide="settings" class="w-5 h-5 mr-2 text-gray-500"></i>
                    模板设置
                </h4>
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 是否启用 -->
                    <div class="flex items-center">
                        <input type="checkbox" name="is_active" id="id_is_active" 
                               {% if object.is_active %}checked{% endif %}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="id_is_active" class="ml-2 block text-sm text-gray-700">
                            启用模板
                        </label>
                        <div class="ml-2 group relative">
                            <i data-lucide="help-circle" class="w-4 h-4 text-gray-400 cursor-help"></i>
                            <div class="hidden group-hover:block absolute z-10 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg -top-2 left-6">
                                启用后的模板可以在创建考评批次时选择使用
                            </div>
                        </div>
                    </div>

                    <!-- 是否默认 -->
                    <div class="flex items-center">
                        <input type="checkbox" name="is_default" id="id_is_default"
                               {% if object.is_default %}checked{% endif %}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="id_is_default" class="ml-2 block text-sm text-gray-700">
                            设为默认模板
                        </label>
                        <div class="ml-2 group relative">
                            <i data-lucide="help-circle" class="w-4 h-4 text-gray-400 cursor-help"></i>
                            <div class="hidden group-hover:block absolute z-10 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg -top-2 left-6">
                                默认模板会在创建批次时自动选中
                            </div>
                        </div>
                    </div>

                    <!-- 排序顺序 -->
                    <div>
                        <label for="id_sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                            排序顺序
                        </label>
                        <input type="number" name="sort_order" id="id_sort_order" 
                               value="{{ object.sort_order }}" min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        <p class="mt-1 text-xs text-gray-500">数字越小排序越靠前</p>
                    </div>
                </div>
            </div>

            <!-- 模板统计信息 -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                    <i data-lucide="bar-chart" class="w-5 h-5 mr-2 text-gray-500"></i>
                    模板统计
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div class="bg-white rounded p-3 border">
                        <div class="text-gray-500">评分项数量</div>
                        <div class="text-lg font-semibold text-blue-600">{{ object.get_items_count }} 项</div>
                    </div>
                    <div class="bg-white rounded p-3 border">
                        <div class="text-gray-500">总分</div>
                        <div class="text-lg font-semibold text-green-600">{{ object.calculate_total_score }} 分</div>
                    </div>
                    <div class="bg-white rounded p-3 border">
                        <div class="text-gray-500">创建时间</div>
                        <div class="text-lg font-semibold text-gray-600">{{ object.created_at|date:"Y-m-d" }}</div>
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="flex items-center justify-between pt-6">
                <div class="text-sm text-gray-500">
                    <i data-lucide="info" class="w-4 h-4 inline mr-1"></i>
                    注意：修改模板类型可能影响现有的评分项配置
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{% url 'evaluations:admin:template_list' %}" 
                       class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        取消
                    </a>
                    <button type="submit" id="submitBtn"
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center">
                        <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                        保存修改
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 表单提交处理
    document.getElementById('templateForm').addEventListener('submit', function(e) {
        const templateName = document.getElementById('id_name').value.trim();
        const templateType = document.getElementById('id_template_type').value;
        
        if (!templateName) {
            e.preventDefault();
            showNotification('请输入模板名称', 'error');
            document.getElementById('id_name').focus();
            return;
        }
        
        if (!templateType) {
            e.preventDefault();
            showNotification('请选择模板类型', 'error');
            document.getElementById('id_template_type').focus();
            return;
        }
        
        // 显示加载状态
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 animate-spin"></i>保存中...';
    });

    // 初始化 Lucide 图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

// 通知函数
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
        type === 'error' ? 'bg-red-100 border border-red-400 text-red-700' :
        type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' :
        type === 'warning' ? 'bg-yellow-100 border border-yellow-400 text-yellow-700' :
        'bg-blue-100 border border-blue-400 text-blue-700'
    }`;
    
    notification.innerHTML = `
        <div class="flex items-center">
            <i data-lucide="${
                type === 'error' ? 'x-circle' :
                type === 'success' ? 'check-circle' :
                type === 'warning' ? 'alert-triangle' :
                'info'
            }" class="w-5 h-5 mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-gray-500 hover:text-gray-700">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
