{% extends "admin/base_admin.html" %}

{% block page_title %}创建考评模板{% endblock %}
{% block page_description %}设计考评表单，配置评分项和标准{% endblock %}

{% block header_actions %}
<a href="{% url 'evaluations:admin:template_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
    <i data-lucide="arrow-left" class="w-4 h-4"></i>
    <span>返回列表</span>
</a>
{% endblock %}

{% block admin_content %}
<form method="post" id="templateForm" class="space-y-8">
    {% csrf_token %}
    
    <!-- 基础信息 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="file-text" class="w-5 h-5 mr-2 text-gray-500"></i>
                基础信息
            </h3>
            <p class="mt-1 text-sm text-gray-500">设置模板的基本信息和类型</p>
        </div>
        <div class="px-6 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 模板名称 -->
                <div>
                    <label for="id_name" class="block text-sm font-medium text-gray-700 mb-2">
                        模板名称 <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="name" id="id_name" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="请输入模板名称">
                    <p class="mt-1 text-xs text-gray-500">例如：月度绩效考评、年终综合评价等</p>
                </div>

                <!-- 模板类型 -->
                <div>
                    <label for="id_template_type" class="block text-sm font-medium text-gray-700 mb-2">
                        模板类型 <span class="text-red-500">*</span>
                    </label>
                    <select name="template_type" id="id_template_type" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">请选择模板类型</option>
                        <option value="structured">结构化评分 - 预设评分项和标准</option>
                        <option value="open">开放式评分 - 自由文本评价</option>
                        <option value="mixed">混合式评分 - 结构化+开放式</option>
                    </select>
                    <p class="mt-1 text-xs text-gray-500">选择不同类型将影响评分方式和界面</p>
                </div>

                <!-- 模板描述 -->
                <div class="lg:col-span-2">
                    <label for="id_description" class="block text-sm font-medium text-gray-700 mb-2">
                        模板描述
                    </label>
                    <textarea name="description" id="id_description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="请输入模板的详细描述和使用说明"></textarea>
                    <p class="mt-1 text-xs text-gray-500">描述模板的用途、适用范围和注意事项</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 模板设置 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="settings" class="w-5 h-5 mr-2 text-gray-500"></i>
                模板设置
            </h3>
            <p class="mt-1 text-sm text-gray-500">配置模板的状态和优先级</p>
        </div>
        <div class="px-6 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 是否启用 -->
                <div class="flex items-center">
                    <input type="checkbox" name="is_active" id="id_is_active" checked
                           class="checkbox">
                    <label for="id_is_active" class="ml-2 block text-sm text-gray-700">
                        启用模板
                    </label>
                    <div class="ml-2 group relative">
                        <i data-lucide="help-circle" class="w-4 h-4 text-gray-400 cursor-help"></i>
                        <div class="hidden group-hover:block absolute z-10 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg -top-2 left-6">
                            启用后的模板可以在创建考评批次时选择使用
                        </div>
                    </div>
                </div>

                <!-- 是否默认 -->
                <div class="flex items-center">
                    <input type="checkbox" name="is_default" id="id_is_default"
                           class="checkbox">
                    <label for="id_is_default" class="ml-2 block text-sm text-gray-700">
                        设为默认模板
                    </label>
                    <div class="ml-2 group relative">
                        <i data-lucide="help-circle" class="w-4 h-4 text-gray-400 cursor-help"></i>
                        <div class="hidden group-hover:block absolute z-10 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg -top-2 left-6">
                            默认模板会在创建批次时自动选中
                        </div>
                    </div>
                </div>

                <!-- 排序顺序 -->
                <div>
                    <label for="id_sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                        排序顺序
                    </label>
                    <input type="number" name="sort_order" id="id_sort_order" value="0" min="0"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <p class="mt-1 text-xs text-gray-500">数字越小排序越靠前</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 评分项管理 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200" id="evaluationItems">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 flex items-center">
                        <i data-lucide="list" class="w-5 h-5 mr-2 text-gray-500"></i>
                        评分项设置
                    </h3>
                    <p class="mt-1 text-sm text-gray-500">添加和配置具体的评分项目</p>
                </div>
                <button type="button" id="addItemBtn" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
                    <i data-lucide="plus" class="w-4 h-4"></i>
                    <span>添加评分项</span>
                </button>
            </div>
        </div>
        <div class="px-6 py-6">
            <div id="itemsList" class="space-y-4">
                <!-- 评分项将在这里动态添加 -->
            </div>
            <div id="emptyState" class="text-center py-8 text-gray-500">
                <i data-lucide="plus-circle" class="mx-auto h-12 w-12 text-gray-300 mb-4"></i>
                <p>点击"添加评分项"开始配置评分内容</p>
            </div>
        </div>
    </div>

    <!-- 提交按钮 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    <i data-lucide="info" class="w-4 h-4 inline mr-1"></i>
                    保存后可以继续编辑评分项的详细设置
                </div>
                <div class="flex items-center space-x-4">
                    <button type="button" id="saveDraftBtn"
                            class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                        保存草稿
                    </button>
                    <button type="submit" id="submitBtn"
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
                        <i data-lucide="save" class="w-4 h-4"></i>
                        <span>创建模板</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
    let itemCounter = 0;

    // 添加评分项
    document.getElementById('addItemBtn').addEventListener('click', function() {
        addEvaluationItem();
    });

    // 模板类型变化处理
    document.getElementById('id_template_type').addEventListener('change', function() {
        const type = this.value;
        const itemsList = document.getElementById('itemsList');
        
        // 清空现有评分项
        itemsList.innerHTML = '';
        itemCounter = 0;
        
        // 根据类型添加默认项
        if (type === 'structured') {
            addDefaultStructuredItems();
        } else if (type === 'mixed') {
            addDefaultMixedItems();
        }
        
        updateEmptyState();
    });

    function addEvaluationItem() {
        itemCounter++;
        const itemHtml = `
            <div class="evaluation-item border border-gray-200 rounded-lg p-4" data-item="${itemCounter}">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="font-medium text-gray-900">评分项 ${itemCounter}</h4>
                    <button type="button" onclick="removeItem(${itemCounter})" 
                            class="text-red-600 hover:text-red-800">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                        <input type="text" name="items[${itemCounter}][name]" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="请输入评分项名称">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">评分方式</label>
                        <select name="items[${itemCounter}][scoring_mode]" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                onchange="updateScoringMode(${itemCounter}, this.value)">
                            <option value="numeric">数值评分</option>
                            <option value="tier">等级评分</option>
                            <option value="text">文本评价</option>
                        </select>
                    </div>
                    
                    <div class="score-config">
                        <label class="block text-sm font-medium text-gray-700 mb-1">满分</label>
                        <input type="number" name="items[${itemCounter}][max_score]" value="10" min="1" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目描述</label>
                    <textarea name="items[${itemCounter}][description]" rows="2"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="请输入评分项的详细说明和评分标准"></textarea>
                </div>
                
                <div class="tier-config mt-4" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 mb-2">等级设置</label>
                    <div class="tier-list space-y-2">
                        <!-- 等级项将在这里添加 -->
                    </div>
                    <button type="button" onclick="addTier(${itemCounter})" 
                            class="mt-2 text-sm text-blue-600 hover:text-blue-800">
                        + 添加等级
                    </button>
                </div>
            </div>
        `;
        
        document.getElementById('itemsList').insertAdjacentHTML('beforeend', itemHtml);
        
        // 初始化Lucide图标
        lucide.createIcons();
        updateEmptyState();
    }

    function removeItem(itemId) {
        if (confirm('确定要删除这个评分项吗？')) {
            document.querySelector(`[data-item="${itemId}"]`).remove();
            updateEmptyState();
        }
    }

    function updateScoringMode(itemId, mode) {
        const item = document.querySelector(`[data-item="${itemId}"]`);
        const scoreConfig = item.querySelector('.score-config');
        const tierConfig = item.querySelector('.tier-config');
        
        if (mode === 'tier') {
            scoreConfig.style.display = 'none';
            tierConfig.style.display = 'block';
            // 添加默认等级
            if (tierConfig.querySelector('.tier-list').children.length === 0) {
                addTier(itemId, '优秀', 10);
                addTier(itemId, '良好', 8);
                addTier(itemId, '一般', 6);
                addTier(itemId, '较差', 4);
            }
        } else if (mode === 'text') {
            scoreConfig.style.display = 'none';
            tierConfig.style.display = 'none';
        } else {
            scoreConfig.style.display = 'block';
            tierConfig.style.display = 'none';
        }
    }

    function addTier(itemId, name = '', score = 10) {
        const tierList = document.querySelector(`[data-item="${itemId}"] .tier-list`);
        const tierIndex = tierList.children.length;
        
        const tierHtml = `
            <div class="flex items-center space-x-2 tier-item">
                <input type="text" name="items[${itemId}][tiers][${tierIndex}][name]" 
                       value="${name}" placeholder="等级名称"
                       class="flex-1 px-3 py-1 border border-gray-300 rounded text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                <input type="number" name="items[${itemId}][tiers][${tierIndex}][score]" 
                       value="${score}" min="0" max="100"
                       class="w-20 px-3 py-1 border border-gray-300 rounded text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                <button type="button" onclick="this.parentElement.remove()" 
                        class="text-red-600 hover:text-red-800">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>
        `;
        
        tierList.insertAdjacentHTML('beforeend', tierHtml);
        lucide.createIcons();
    }

    function addDefaultStructuredItems() {
        // 添加默认的结构化评分项
        const defaultItems = [
            { name: '工作质量', description: '工作完成的质量和准确度' },
            { name: '工作效率', description: '工作完成的速度和时效性' },
            { name: '团队协作', description: '与同事的协作配合能力' },
            { name: '学习能力', description: '接受新知识和技能的能力' },
            { name: '责任心', description: '对工作的责任感和主动性' }
        ];
        
        defaultItems.forEach(item => {
            addEvaluationItem();
            const lastItem = document.querySelector('#itemsList .evaluation-item:last-child');
            lastItem.querySelector('input[name*="[name]"]').value = item.name;
            lastItem.querySelector('textarea[name*="[description]"]').value = item.description;
        });
    }

    function addDefaultMixedItems() {
        // 添加混合式评分的默认项
        addEvaluationItem();
        const item1 = document.querySelector('#itemsList .evaluation-item:last-child');
        item1.querySelector('input[name*="[name]"]').value = '综合评分';
        item1.querySelector('textarea[name*="[description]"]').value = '基于各项指标的综合评价';
        
        addEvaluationItem();
        const item2 = document.querySelector('#itemsList .evaluation-item:last-child');
        item2.querySelector('input[name*="[name]"]').value = '文字评价';
        item2.querySelector('select[name*="[scoring_mode]"]').value = 'text';
        item2.querySelector('textarea[name*="[description]"]').value = '详细的文字评价和建议';
        updateScoringMode(itemCounter, 'text');
    }

    function updateEmptyState() {
        const itemsList = document.getElementById('itemsList');
        const emptyState = document.getElementById('emptyState');
        
        if (itemsList.children.length === 0) {
            emptyState.style.display = 'block';
        } else {
            emptyState.style.display = 'none';
        }
    }

    // 表单提交处理
    document.getElementById('templateForm').addEventListener('submit', function(e) {
        const templateType = document.getElementById('id_template_type').value;
        const itemsCount = document.querySelectorAll('.evaluation-item').length;
        
        if (!templateType) {
            e.preventDefault();
            showNotification('请选择模板类型', 'error');
            return;
        }
        
        if (templateType !== 'open' && itemsCount === 0) {
            e.preventDefault();
            showNotification('请至少添加一个评分项', 'error');
            return;
        }
        
        // 显示加载状态
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 animate-spin"></i>创建中...';
    });

    // 保存草稿
    document.getElementById('saveDraftBtn').addEventListener('click', function() {
        const form = document.getElementById('templateForm');
        const formData = new FormData(form);
        formData.append('save_draft', 'true');
        
        fetch(form.action, {
            method: 'POST',
            body: formData,
        })
        .then(response => {
            if (response.ok) {
                showNotification('草稿保存成功', 'success');
            } else {
                showNotification('保存失败，请重试', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('保存失败，请重试', 'error');
        });
    });

    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        updateEmptyState();
    });
</script>
{% endblock %}