{% extends "admin/base_admin.html" %}

{% block page_title %}添加评分项{% endblock %}
{% block page_description %}为模板 "{{ template.name }}" 添加新的评分项目{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'evaluations:admin:item_list' template.pk %}" 
       class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700">
        <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>返回评分项列表
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto">
    <!-- 模板信息 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">所属模板</h3>
        </div>
        <div class="px-6 py-4">
            <div class="grid grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">模板名称</label>
                    <p class="mt-1 text-sm text-gray-900">{{ template.name }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">模板类型</label>
                    <p class="mt-1 text-sm text-gray-900">{{ template.get_template_type_display }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">现有评分项</label>
                    <p class="mt-1 text-sm text-gray-900">{{ template.evaluationitem_set.count }} 个</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 评分项表单 -->
    <form method="post" class="bg-white rounded-lg shadow-sm border border-gray-200">
        {% csrf_token %}
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">评分项信息</h3>
        </div>
        <div class="px-6 py-6 space-y-6">
            <!-- 基本信息 -->
            <div class="grid grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        评分项名称 <span class="text-red-500">*</span>
                    </label>
                    {{ form.name }}
                    {% if form.name.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">请输入清晰的评分项名称</p>
                </div>
                
                <div>
                    <label for="{{ form.scoring_mode.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        评分方式 <span class="text-red-500">*</span>
                    </label>
                    {{ form.scoring_mode }}
                    {% if form.scoring_mode.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.scoring_mode.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">选择该项目的评分方式</p>
                </div>
            </div>

            <!-- 描述 -->
            <div>
                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    评分说明
                </label>
                {{ form.description }}
                {% if form.description.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
                {% endif %}
                <p class="mt-1 text-sm text-gray-500">详细描述评分标准和要求</p>
            </div>

            <!-- 评分配置 -->
            <div class="grid grid-cols-3 gap-6">
                <div>
                    <label for="{{ form.max_score.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        最高分值 <span class="text-red-500">*</span>
                    </label>
                    {{ form.max_score }}
                    {% if form.max_score.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.max_score.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">该项目的最高得分</p>
                </div>
                
                <div>
                    <label for="{{ form.weight.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        权重 <span class="text-red-500">*</span>
                    </label>
                    {{ form.weight }}
                    {% if form.weight.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.weight.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">该项目在总分中的权重</p>
                </div>
                
                <div>
                    <label for="{{ form.sort_order.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        排序序号
                    </label>
                    {{ form.sort_order }}
                    {% if form.sort_order.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.sort_order.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">控制显示顺序，数字越小越靠前</p>
                </div>
            </div>

            <!-- 选项设置 -->
            <div class="grid grid-cols-2 gap-6">
                <div class="flex items-center">
                    {{ form.is_required }}
                    <label for="{{ form.is_required.id_for_label }}" class="ml-2 text-sm text-gray-700">
                        必填项目
                    </label>
                    {% if form.is_required.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.is_required.errors.0 }}</p>
                    {% endif %}
                    <p class="ml-2 text-sm text-gray-500">勾选后该项目为必填</p>
                </div>
            </div>
        </div>
        
        <!-- 表单按钮 -->
        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div class="flex items-center justify-end space-x-3">
                <a href="{% url 'evaluations:admin:item_list' template.pk %}" 
                   class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                    取消
                </a>
                <button type="submit" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i data-lucide="save" class="w-4 h-4 mr-2 inline"></i>
                    保存评分项
                </button>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 为表单字段添加样式
    document.addEventListener('DOMContentLoaded', function() {
        // 为所有输入字段添加样式
        const inputs = document.querySelectorAll('input[type="text"], input[type="number"], textarea, select');
        inputs.forEach(input => {
            input.classList.add(
                'w-full', 'border', 'border-gray-300', 'rounded-lg', 'px-4', 'py-3',
                'focus:outline-none', 'focus:ring-2', 'focus:ring-blue-500', 
                'focus:border-transparent', 'transition-all', 'duration-200'
            );
        });

        // 为复选框添加样式
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.classList.add(
                'h-4', 'w-4', 'text-blue-600', 'focus:ring-blue-500', 
                'border-gray-300', 'rounded'
            );
        });

        // 为textarea设置行数
        const textareas = document.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            textarea.rows = 4;
            textarea.classList.add('resize-none');
        });
    });
</script>
{% endblock %}
