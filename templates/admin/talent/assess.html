{% extends "admin/base_admin.html" %}

{% block page_title %}人才盘点评估 - {{ batch.name }}{% endblock %}
{% block page_description %}基于考评数据进行人才盘点和九宫格分类{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'reports:admin:talent_matrix' batch.id %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="grid-3x3" class="w-4 h-4"></i>
        <span>九宫格视图</span>
    </a>
    <button onclick="batchAssess()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="wand-2" class="w-4 h-4"></i>
        <span>智能盘点</span>
    </button>
    <a href="{% url 'reports:admin:talent_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-7xl mx-auto space-y-6">
    <!-- 批次信息卡片 -->
    <div class="bg-white rounded-lg shadow">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center text-white">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i data-lucide="clipboard-list" class="w-6 h-6"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-xl font-bold">{{ batch.name }}</h2>
                        <p class="text-blue-100">{{ batch.get_status_display }} • {{ batch.description|default:"考评批次人才盘点" }}</p>
                    </div>
                </div>
                <div class="text-right text-white">
                    <div class="text-2xl font-bold">{{ evaluation_data|length }}</div>
                    <div class="text-sm text-blue-100">待评估人员</div>
                </div>
            </div>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-green-50 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-green-600" id="assessedCount">0</div>
                    <div class="text-sm text-green-700">已评估</div>
                </div>
                <div class="bg-yellow-50 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-yellow-600" id="pendingCount">{{ evaluation_data|length }}</div>
                    <div class="text-sm text-yellow-700">待评估</div>
                </div>
                <div class="bg-blue-50 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-blue-600" id="avgScore">0.0</div>
                    <div class="text-sm text-blue-700">平均分</div>
                </div>
                <div class="bg-purple-50 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-purple-600" id="progressRate">0%</div>
                    <div class="text-sm text-purple-700">完成度</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 评估标准说明 -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="info" class="w-5 h-5 mr-2 text-gray-500"></i>
                评估标准与九宫格分类
            </h3>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 评分标准 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-3">评分标准</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between p-2 bg-green-50 rounded">
                            <span class="font-medium text-green-700">优秀 (8.0-10.0分)</span>
                            <span class="text-green-600">高绩效/高潜力</span>
                        </div>
                        <div class="flex justify-between p-2 bg-blue-50 rounded">
                            <span class="font-medium text-blue-700">良好 (6.0-7.9分)</span>
                            <span class="text-blue-600">中等绩效/中等潜力</span>
                        </div>
                        <div class="flex justify-between p-2 bg-yellow-50 rounded">
                            <span class="font-medium text-yellow-700">一般 (4.0-5.9分)</span>
                            <span class="text-yellow-600">待提升</span>
                        </div>
                        <div class="flex justify-between p-2 bg-red-50 rounded">
                            <span class="font-medium text-red-700">较差 (0-3.9分)</span>
                            <span class="text-red-600">需重点关注</span>
                        </div>
                    </div>
                </div>
                
                <!-- 九宫格分类 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-3">九宫格分类</h4>
                    <div class="grid grid-cols-3 gap-2 text-xs">
                        <div class="bg-red-100 border border-red-200 rounded p-2 text-center">
                            <div class="font-medium text-red-700">问题员工</div>
                            <div class="text-red-500">低绩效/低潜力</div>
                        </div>
                        <div class="bg-yellow-100 border border-yellow-200 rounded p-2 text-center">
                            <div class="font-medium text-yellow-700">绩效明星</div>
                            <div class="text-yellow-500">高绩效/低潜力</div>
                        </div>
                        <div class="bg-green-100 border border-green-200 rounded p-2 text-center">
                            <div class="font-medium text-green-700">超级明星</div>
                            <div class="text-green-500">高绩效/高潜力</div>
                        </div>
                        <div class="bg-orange-100 border border-orange-200 rounded p-2 text-center">
                            <div class="font-medium text-orange-700">待观察</div>
                            <div class="text-orange-500">中绩效/低潜力</div>
                        </div>
                        <div class="bg-blue-100 border border-blue-200 rounded p-2 text-center">
                            <div class="font-medium text-blue-700">核心员工</div>
                            <div class="text-blue-500">中绩效/中潜力</div>
                        </div>
                        <div class="bg-cyan-100 border border-cyan-200 rounded p-2 text-center">
                            <div class="font-medium text-cyan-700">潜力明星</div>
                            <div class="text-cyan-500">中绩效/高潜力</div>
                        </div>
                        <div class="bg-gray-100 border border-gray-200 rounded p-2 text-center">
                            <div class="font-medium text-gray-700">潜力员工</div>
                            <div class="text-gray-500">低绩效/中潜力</div>
                        </div>
                        <div class="bg-indigo-100 border border-indigo-200 rounded p-2 text-center">
                            <div class="font-medium text-indigo-700">新星</div>
                            <div class="text-indigo-500">低绩效/高潜力</div>
                        </div>
                        <div class="bg-pink-100 border border-pink-200 rounded p-2 text-center">
                            <div class="font-medium text-pink-700">待定义</div>
                            <div class="text-pink-500">待评估</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 人才评估列表 -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="users" class="w-5 h-5 mr-2 text-gray-500"></i>
                    人才评估列表
                </h3>
                <div class="flex items-center space-x-3">
                    <div class="flex items-center">
                        <label class="text-sm text-gray-500 mr-2">批量操作:</label>
                        <select id="batchAction" class="text-sm border border-gray-300 rounded px-2 py-1">
                            <option value="">选择操作</option>
                            <option value="auto_assess">自动评估</option>
                            <option value="mark_high_potential">标记高潜力</option>
                            <option value="mark_core">标记核心员工</option>
                        </select>
                        <button onclick="executeBatchAction()" class="ml-2 px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                            执行
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="selectAll" class="checkbox" onchange="toggleSelectAll()">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工信息</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">考评得分</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">绩效评估</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">潜力评估</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">九宫格分类</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发展建议</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="assessmentTableBody">
                    {% for item in evaluation_data %}
                    <tr class="assessment-row" data-staff-id="{{ item.staff.id }}">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="staff-checkbox checkbox" value="{{ item.staff.id }}">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium text-gray-600">{{ item.staff.name|first }}</span>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ item.staff.name }}</div>
                                    <div class="text-sm text-gray-500">{{ item.staff.employee_no }}</div>
                                    <div class="text-sm text-gray-500">{{ item.staff.department.name }}</div>
                                    <div class="text-xs text-gray-400">{{ item.staff.position.name|default:"未设置职位" }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-lg font-bold text-gray-900">{{ item.score|floatformat:1 }}</div>
                            <div class="text-xs text-gray-500">评价权重: {{ item.relation.weight_factor }}×</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center space-x-2">
                                <select class="performance-score text-sm border border-gray-300 rounded px-2 py-1 w-20" 
                                        data-staff="{{ item.staff.id }}" onchange="updateAssessment(this)">
                                    <option value="">-</option>
                                    <option value="10">10</option>
                                    <option value="9">9</option>
                                    <option value="8">8</option>
                                    <option value="7">7</option>
                                    <option value="6">6</option>
                                    <option value="5">5</option>
                                    <option value="4">4</option>
                                    <option value="3">3</option>
                                    <option value="2">2</option>
                                    <option value="1">1</option>
                                </select>
                                <div class="text-xs text-gray-500">分</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center space-x-2">
                                <select class="potential-score text-sm border border-gray-300 rounded px-2 py-1 w-20" 
                                        data-staff="{{ item.staff.id }}" onchange="updateAssessment(this)">
                                    <option value="">-</option>
                                    <option value="10">10</option>
                                    <option value="9">9</option>
                                    <option value="8">8</option>
                                    <option value="7">7</option>
                                    <option value="6">6</option>
                                    <option value="5">5</option>
                                    <option value="4">4</option>
                                    <option value="3">3</option>
                                    <option value="2">2</option>
                                    <option value="1">1</option>
                                </select>
                                <div class="text-xs text-gray-500">分</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <select class="category-select text-sm border border-gray-300 rounded px-3 py-1" 
                                    data-staff="{{ item.staff.id }}" onchange="updateAssessment(this)">
                                <option value="">待分类</option>
                                <option value="super_star">超级明星</option>
                                <option value="performance_star">绩效明星</option>
                                <option value="potential_star">潜力明星</option>
                                <option value="core_employee">核心员工</option>
                                <option value="rising_star">新星</option>
                                <option value="potential_employee">潜力员工</option>
                                <option value="watch_list">待观察</option>
                                <option value="problem_employee">问题员工</option>
                            </select>
                        </td>
                        <td class="px-6 py-4">
                            <textarea class="development-suggestions text-sm border border-gray-300 rounded px-2 py-1 w-48 h-16 resize-none" 
                                      data-staff="{{ item.staff.id }}"
                                      placeholder="输入发展建议..."
                                      onchange="updateAssessment(this)"></textarea>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                            <button onclick="autoAssess('{{ item.staff.id }}')" 
                                    class="text-blue-600 hover:text-blue-900 font-medium">
                                自动评估
                            </button>
                            <button onclick="saveAssessment('{{ item.staff.id }}')" 
                                    class="text-green-600 hover:text-green-900 font-medium">
                                保存
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="px-6 py-12 text-center">
                            <i data-lucide="clipboard-x" class="mx-auto h-12 w-12 text-gray-400"></i>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无评估数据</h3>
                            <p class="mt-1 text-sm text-gray-500">该批次暂无已完成的考评记录。</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 批量操作工具栏 -->
        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    已选择 <span id="selectedCount">0</span> 个员工
                </div>
                <div class="flex items-center space-x-3">
                    <button onclick="saveAllAssessments()" 
                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
                        <i data-lucide="save" class="w-4 h-4"></i>
                        <span>保存全部</span>
                    </button>
                    <button onclick="generateMatrix()" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
                        <i data-lucide="grid-3x3" class="w-4 h-4"></i>
                        <span>生成矩阵</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    updateStatistics();
    bindCheckboxEvents();
});

// 更新统计数据
function updateStatistics() {
    const rows = document.querySelectorAll('.assessment-row');
    let assessedCount = 0;
    let totalScore = 0;
    let scoreCount = 0;
    
    rows.forEach(row => {
        const performanceSelect = row.querySelector('.performance-score');
        const potentialSelect = row.querySelector('.potential-score');
        
        if (performanceSelect.value && potentialSelect.value) {
            assessedCount++;
            totalScore += parseFloat(performanceSelect.value) + parseFloat(potentialSelect.value);
            scoreCount += 2;
        }
    });
    
    const pendingCount = rows.length - assessedCount;
    const avgScore = scoreCount > 0 ? (totalScore / scoreCount).toFixed(1) : '0.0';
    const progressRate = rows.length > 0 ? Math.round((assessedCount / rows.length) * 100) : 0;
    
    document.getElementById('assessedCount').textContent = assessedCount;
    document.getElementById('pendingCount').textContent = pendingCount;
    document.getElementById('avgScore').textContent = avgScore;
    document.getElementById('progressRate').textContent = progressRate + '%';
}

// 绑定复选框事件
function bindCheckboxEvents() {
    const checkboxes = document.querySelectorAll('.staff-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
}

// 切换全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.staff-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedCount();
}

// 更新选中数量
function updateSelectedCount() {
    const selectedBoxes = document.querySelectorAll('.staff-checkbox:checked');
    document.getElementById('selectedCount').textContent = selectedBoxes.length;
}

// 自动评估单个员工
function autoAssess(staffId) {
    const row = document.querySelector(`[data-staff-id="${staffId}"]`);
    const performanceSelect = row.querySelector('.performance-score');
    const potentialSelect = row.querySelector('.potential-score');
    const categorySelect = row.querySelector('.category-select');
    const suggestionsTextarea = row.querySelector('.development-suggestions');
    
    // 基于考评分数自动评估
    const scoreCell = row.querySelector('.text-lg.font-bold');
    const score = parseFloat(scoreCell.textContent);
    
    // 简单的自动评估逻辑
    let performanceScore, potentialScore, category, suggestions;
    
    if (score >= 8.5) {
        performanceScore = 9;
        potentialScore = 8;
        category = 'super_star';
        suggestions = '继续发挥领导作用，可考虑承担更多挑战性项目。';
    } else if (score >= 7.0) {
        performanceScore = 7;
        potentialScore = 7;
        category = 'core_employee';
        suggestions = '表现稳定，建议提供更多成长机会和专业培训。';
    } else if (score >= 5.5) {
        performanceScore = 6;
        potentialScore = 5;
        category = 'watch_list';
        suggestions = '需要关注绩效改进，建议制定具体的提升计划。';
    } else {
        performanceScore = 4;
        potentialScore = 4;
        category = 'problem_employee';
        suggestions = '绩效需要显著改进，建议加强指导和支持。';
    }
    
    // 设置评估结果
    performanceSelect.value = performanceScore;
    potentialSelect.value = potentialScore;
    categorySelect.value = category;
    suggestionsTextarea.value = suggestions;
    
    // 更新统计数据
    updateStatistics();
    
    UI.showMessage('自动评估完成', 'success');
}

// 更新评估数据
function updateAssessment(element) {
    updateStatistics();
}

// 保存单个评估
function saveAssessment(staffId) {
    const row = document.querySelector(`[data-staff-id="${staffId}"]`);
    const data = {
        staff_id: staffId,
        batch_id: {{ batch.id }},
        performance_score: row.querySelector('.performance-score').value,
        potential_score: row.querySelector('.potential-score').value,
        talent_category: row.querySelector('.category-select').value,
        development_suggestions: row.querySelector('.development-suggestions').value
    };
    
    // 验证数据
    if (!data.performance_score || !data.potential_score || !data.talent_category) {
        UI.showMessage('请完成绩效评估、潜力评估和分类选择', 'error');
        return;
    }
    
    // 发送保存请求
    fetch('/reports/admin/talent/assess/save/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            UI.showMessage('保存成功', 'success');
            row.classList.add('bg-green-50');
            setTimeout(() => row.classList.remove('bg-green-50'), 2000);
        } else {
            UI.showMessage(result.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        UI.showMessage('保存失败，请重试', 'error');
    });
}

// 保存全部评估
function saveAllAssessments() {
    const rows = document.querySelectorAll('.assessment-row');
    const assessments = [];
    
    rows.forEach(row => {
        const staffId = row.getAttribute('data-staff-id');
        const performanceScore = row.querySelector('.performance-score').value;
        const potentialScore = row.querySelector('.potential-score').value;
        const category = row.querySelector('.category-select').value;
        const suggestions = row.querySelector('.development-suggestions').value;
        
        if (performanceScore && potentialScore && category) {
            assessments.push({
                staff_id: staffId,
                batch_id: {{ batch.id }},
                performance_score: performanceScore,
                potential_score: potentialScore,
                talent_category: category,
                development_suggestions: suggestions
            });
        }
    });
    
    if (assessments.length === 0) {
        UI.showMessage('没有可保存的评估数据', 'warning');
        return;
    }
    
    // 发送批量保存请求
    fetch('/reports/admin/talent/assess/batch-save/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({assessments: assessments})
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            UI.showMessage(`成功保存 ${result.saved_count} 个评估数据`, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            UI.showMessage(result.message || '批量保存失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        UI.showMessage('批量保存失败，请重试', 'error');
    });
}

// 批量智能评估
function batchAssess() {
    const rows = document.querySelectorAll('.assessment-row');
    
    if (confirm(`确定要对 ${rows.length} 个员工进行智能评估吗？此操作将覆盖现有的手动评估。`)) {
        rows.forEach(row => {
            const staffId = row.getAttribute('data-staff-id');
            autoAssess(staffId);
        });
        
        UI.showMessage('批量智能评估完成', 'success');
    }
}

// 执行批量操作
function executeBatchAction() {
    const action = document.getElementById('batchAction').value;
    const selectedBoxes = document.querySelectorAll('.staff-checkbox:checked');
    
    if (!action) {
        UI.showMessage('请选择操作类型', 'warning');
        return;
    }
    
    if (selectedBoxes.length === 0) {
        UI.showMessage('请选择要操作的员工', 'warning');
        return;
    }
    
    selectedBoxes.forEach(checkbox => {
        const staffId = checkbox.value;
        const row = document.querySelector(`[data-staff-id="${staffId}"]`);
        
        switch (action) {
            case 'auto_assess':
                autoAssess(staffId);
                break;
            case 'mark_high_potential':
                row.querySelector('.potential-score').value = 9;
                row.querySelector('.category-select').value = 'potential_star';
                break;
            case 'mark_core':
                row.querySelector('.performance-score').value = 7;
                row.querySelector('.potential-score').value = 7;
                row.querySelector('.category-select').value = 'core_employee';
                break;
        }
    });
    
    updateStatistics();
    UI.showMessage(`批量操作完成，影响 ${selectedBoxes.length} 个员工`, 'success');
}

// 生成人才矩阵
function generateMatrix() {
    if (confirm('确定要生成人才九宫格矩阵吗？')) {
        window.location.href = `{% url 'reports:admin:talent_matrix' batch.id %}`;
    }
}
</script>
{% endblock %}