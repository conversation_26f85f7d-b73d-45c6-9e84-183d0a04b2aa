{% extends "admin/base_admin.html" %}

{% block page_title %}消息详情{% endblock %}
{% block page_description %}查看消息详细内容{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <!-- 返回按钮 -->
    <a href="{% url 'communications:admin:message_center' %}" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回消息中心</span>
    </a>
    
    {% if not message_recipient.is_starred %}
    <!-- 收藏按钮 -->
    <button onclick="toggleStar({{ message.id }}, true)" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="star" class="w-4 h-4"></i>
        <span>收藏</span>
    </button>
    {% else %}
    <!-- 取消收藏按钮 -->
    <button onclick="toggleStar({{ message.id }}, false)" class="px-4 py-2 border border-yellow-300 text-yellow-700 bg-yellow-50 rounded-md hover:bg-yellow-100 flex items-center space-x-2">
        <i data-lucide="star" class="w-4 h-4 fill-current"></i>
        <span>已收藏</span>
    </button>
    {% endif %}
    
    <!-- 删除按钮 -->
    <button onclick="deleteMessage({{ message.id }})" class="px-4 py-2 border border-red-300 text-red-700 rounded-md hover:bg-red-50 flex items-center space-x-2">
        <i data-lucide="trash-2" class="w-4 h-4"></i>
        <span>删除</span>
    </button>
    
    {% if user.role.name in 'SUPER_ADMIN,SYSTEM_ADMIN,HR_ADMIN' %}
    <!-- 回复按钮（仅管理员） -->
    <button onclick="showReplyModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="reply" class="w-4 h-4"></i>
        <span>回复</span>
    </button>
    {% endif %}
</div>
{% endblock %}

{% block admin_content %}
<!-- 消息头部信息 -->
<div class="bg-white rounded-lg shadow mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <!-- 消息类型图标 -->
                <div class="p-3 rounded-lg
                    {% if message.message_type == 'SYSTEM' %}bg-blue-100{% endif %}
                    {% if message.message_type == 'PERSONAL' %}bg-green-100{% endif %}
                    {% if message.message_type == 'EVALUATION' %}bg-purple-100{% endif %}
                    {% if message.message_type == 'ANNOUNCEMENT' %}bg-yellow-100{% endif %}">
                    {% if message.message_type == 'SYSTEM' %}
                        <i data-lucide="settings" class="w-6 h-6 text-blue-600"></i>
                    {% elif message.message_type == 'PERSONAL' %}
                        <i data-lucide="user" class="w-6 h-6 text-green-600"></i>
                    {% elif message.message_type == 'EVALUATION' %}
                        <i data-lucide="clipboard-check" class="w-6 h-6 text-purple-600"></i>
                    {% elif message.message_type == 'ANNOUNCEMENT' %}
                        <i data-lucide="megaphone" class="w-6 h-6 text-yellow-600"></i>
                    {% endif %}
                </div>
                
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">{{ message.subject }}</h1>
                    <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                        <span>发送者：{% if message.sender %}{{ message.sender.name }}{% else %}系统{% endif %}</span>
                        <span>•</span>
                        <span>{{ message.created_at|date:"Y年m月d日 H:i" }}</span>
                        <span>•</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {% if message.priority == 'HIGH' %}bg-red-100 text-red-800{% endif %}
                            {% if message.priority == 'MEDIUM' %}bg-blue-100 text-blue-800{% endif %}
                            {% if message.priority == 'LOW' %}bg-gray-100 text-gray-800{% endif %}">
                            {% if message.priority == 'HIGH' %}高优先级{% endif %}
                            {% if message.priority == 'MEDIUM' %}中优先级{% endif %}
                            {% if message.priority == 'LOW' %}低优先级{% endif %}
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- 消息状态 -->
            <div class="flex items-center space-x-3">
                {% if message_recipient.is_starred %}
                <div class="flex items-center text-yellow-600">
                    <i data-lucide="star" class="w-4 h-4 fill-current mr-1"></i>
                    <span class="text-sm">已收藏</span>
                </div>
                {% endif %}
                
                {% if message.expires_at and message.is_expired %}
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                    已过期
                </span>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="px-6 py-6">
        <!-- 消息内容 -->
        <div class="prose max-w-none">
            <div class="text-gray-900 leading-relaxed whitespace-pre-wrap">{{ message.content }}</div>
        </div>
        
        <!-- 业务关联信息 -->
        {% if message.related_model and message.related_id %}
        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 class="text-sm font-medium text-gray-900 mb-2">相关业务</h4>
            <p class="text-sm text-gray-600">
                关联对象：{{ message.related_model }} (ID: {{ message.related_id }})
            </p>
        </div>
        {% endif %}
        
        <!-- 过期时间提醒 -->
        {% if message.expires_at %}
        <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div class="flex items-center">
                <i data-lucide="clock" class="w-5 h-5 text-yellow-600 mr-2"></i>
                <div>
                    <h4 class="text-sm font-medium text-yellow-800">消息有效期</h4>
                    <p class="text-sm text-yellow-700">
                        此消息将于 {{ message.expires_at|date:"Y年m月d日 H:i" }} 过期
                        {% if message.is_expired %}
                        <span class="text-red-600 font-medium">（已过期）</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 消息统计信息 -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总接收人数</p>
                <p class="text-2xl font-bold text-gray-900">{{ message.get_total_recipients }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="mail-open" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">已读人数</p>
                <p class="text-2xl font-bold text-gray-900">{{ read_count }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="mail" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">未读人数</p>
                <p class="text-2xl font-bold text-gray-900">{{ message.get_unread_count }}</p>
            </div>
        </div>
    </div>
</div>

<!-- 阅读记录 -->
{% if user.role.name in 'SUPER_ADMIN,SYSTEM_ADMIN,HR_ADMIN' %}
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">阅读记录</h3>
    </div>
    <div class="px-6 py-4">
        {% if read_logs %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">阅读者</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">阅读时间</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for log in read_logs %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8">
                                    <div class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-600">{{ log.reader.name|first }}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ log.reader.name }}</div>
                                    <div class="text-sm text-gray-500">{{ log.reader.position.name }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ log.reader.department.name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ log.read_at|date:"m-d H:i" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ log.ip_address|default:"--" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                已读
                            </span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-8">
            <i data-lucide="mail" class="mx-auto h-12 w-12 text-gray-400"></i>
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无阅读记录</h3>
            <p class="mt-1 text-sm text-gray-500">还没有人阅读此消息。</p>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}

<!-- 回复弹窗 -->
<div id="replyModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto hidden z-50">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <form id="replyForm">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                            <i data-lucide="reply" class="h-6 w-6 text-blue-600"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">回复消息</h3>
                            <div class="mt-4">
                                <label for="replySubject" class="block text-sm font-medium text-gray-700">主题</label>
                                <input type="text" id="replySubject" name="subject" value="回复: {{ message.subject }}" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                            <div class="mt-4">
                                <label for="replyContent" class="block text-sm font-medium text-gray-700">内容</label>
                                <textarea id="replyContent" name="content" rows="4" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="输入回复内容..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                        发送回复
                    </button>
                    <button type="button" onclick="hideReplyModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        取消
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时标记消息为已读
document.addEventListener('DOMContentLoaded', function() {
    markAsRead({{ message.id }});
    
    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

// 标记消息为已读
function markAsRead(messageId) {
    fetch(`/communications/api/messages/${messageId}/read/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/json',
        },
    }).catch(error => {
        console.error('标记已读失败:', error);
    });
}

// 收藏/取消收藏消息
function toggleStar(messageId, starred) {
    const url = starred ? 
        `/communications/api/messages/${messageId}/star/` : 
        `/communications/api/messages/${messageId}/unstar/`;
    
    fetch(url, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload(); // 重新加载页面更新状态
        } else {
            alert(data.message || '操作失败');
        }
    })
    .catch(error => {
        console.error('操作失败:', error);
        alert('操作失败，请稍后重试');
    });
}

// 删除消息
function deleteMessage(messageId) {
    if (!confirm('确定要删除这条消息吗？删除后将无法恢复。')) {
        return;
    }
    
    fetch(`/communications/api/messages/${messageId}/delete/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = '/communications/admin/message-center/';
        } else {
            alert(data.message || '删除失败');
        }
    })
    .catch(error => {
        console.error('删除失败:', error);
        alert('删除失败，请稍后重试');
    });
}

// 显示回复弹窗
function showReplyModal() {
    document.getElementById('replyModal').classList.remove('hidden');
}

// 隐藏回复弹窗
function hideReplyModal() {
    document.getElementById('replyModal').classList.add('hidden');
    document.getElementById('replyForm').reset();
}

// 提交回复
document.getElementById('replyForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {
        subject: formData.get('subject'),
        content: formData.get('content'),
        message_type: 'PERSONAL',
        priority: 'MEDIUM',
        recipients: [{{ message.sender.id }}] // 回复给原发送者
    };
    
    fetch('/communications/api/messages/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCsrfToken(),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            hideReplyModal();
            alert('回复发送成功！');
        } else {
            alert(data.message || '发送失败');
        }
    })
    .catch(error => {
        console.error('发送失败:', error);
        alert('发送失败，请稍后重试');
    });
});

// 获取CSRF Token
function getCsrfToken() {
    const cookieValue = document.cookie
        .split('; ')
        .find(row => row.startsWith('csrftoken='))
        ?.split('=')[1];
    return cookieValue || '';
}

// ESC键关闭弹窗
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        hideReplyModal();
    }
});

// 点击背景关闭弹窗
document.getElementById('replyModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideReplyModal();
    }
});
</script>
{% endblock %}