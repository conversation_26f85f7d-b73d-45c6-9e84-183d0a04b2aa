{% extends "admin/base_admin.html" %}

{% block page_title %}写消息{% endblock %}
{% block page_description %}发送新消息{% endblock %}

{% block extra_css %}
<style>
    .recipient-tag {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        background-color: #e0e7ff;
        color: #3730a3;
        border-radius: 9999px;
        font-size: 0.875rem;
        margin: 0.125rem;
    }
    .recipient-tag .remove-btn {
        margin-left: 0.5rem;
        cursor: pointer;
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(55, 48, 163, 0.2);
    }
    .recipient-tag .remove-btn:hover {
        background-color: rgba(55, 48, 163, 0.3);
    }
    .department-group {
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        margin-bottom: 0.5rem;
    }
    .department-header {
        background-color: #f9fafb;
        padding: 0.75rem;
        font-weight: 600;
        border-bottom: 1px solid #e5e7eb;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: between;
    }
    .department-staff {
        padding: 0.5rem;
        display: none;
    }
    .department-staff.show {
        display: block;
    }
    .staff-checkbox {
        display: flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        margin: 0.125rem 0;
        border-radius: 0.25rem;
        cursor: pointer;
    }
    .staff-checkbox:hover {
        background-color: #f3f4f6;
    }
    .priority-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    .priority-high {
        background-color: #fecaca;
        color: #991b1b;
    }
    .priority-medium {
        background-color: #fed7aa;
        color: #9a3412;
    }
    .priority-low {
        background-color: #bbf7d0;
        color: #14532d;
    }
</style>
{% endblock %}

{% block admin_content %}
<div class="max-w-4xl">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h1 class="text-xl font-semibold text-gray-900 flex items-center">
                    <i data-lucide="edit" class="w-5 h-5 mr-2 text-blue-600"></i>
                    写消息
                </h1>
                <a href="{% url 'communications:admin:message_center' %}" 
                   class="text-gray-500 hover:text-gray-700 transition-colors">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </a>
            </div>
        </div>
        
        <form id="messageForm" class="p-6 space-y-6">
            {% csrf_token %}
            
            <!-- 消息类型和优先级 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="messageType" class="block text-sm font-medium text-gray-700 mb-2">消息类型</label>
                    <select id="messageType" name="message_type" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">请选择消息类型</option>
                        <option value="PERSONAL">个人消息</option>
                        <option value="SYSTEM">系统通知</option>
                        <option value="EVALUATION">考评相关</option>
                        <option value="ANNOUNCEMENT">公告通知</option>
                    </select>
                </div>
                
                <div>
                    <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                    <select id="priority" name="priority" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="MEDIUM">中等优先级</option>
                        <option value="HIGH">高优先级</option>
                        <option value="LOW">低优先级</option>
                    </select>
                </div>
            </div>
            
            <!-- 收件人选择 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">收件人</label>
                
                <!-- 已选择的收件人 -->
                <div class="mb-3 p-3 border border-gray-200 rounded-md min-h-[2.5rem] bg-gray-50" id="selectedRecipients">
                    <div class="text-gray-500 text-sm" id="recipientPlaceholder">点击下方选择收件人...</div>
                </div>
                
                <!-- 快速选择按钮 -->
                <div class="flex flex-wrap gap-2 mb-3">
                    <button type="button" onclick="selectAll()" 
                            class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors">
                        全选
                    </button>
                    <button type="button" onclick="clearSelection()" 
                            class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors">
                        清空
                    </button>
                    <button type="button" onclick="selectByDepartment('all')" 
                            class="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors">
                        所有部门
                    </button>
                </div>
                
                <!-- 按部门选择收件人 -->
                <div class="border border-gray-200 rounded-md max-h-64 overflow-y-auto">
                    <div id="departmentList">
                        <!-- 这里会通过JavaScript动态加载部门和员工列表 -->
                        <div class="p-4 text-center text-gray-500">
                            <i data-lucide="loader" class="w-5 h-5 animate-spin mx-auto mb-2"></i>
                            加载员工列表中...
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 消息主题 -->
            <div>
                <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">主题</label>
                <input type="text" id="subject" name="subject" required maxlength="200"
                       placeholder="请输入消息主题"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            
            <!-- 消息内容 -->
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">消息内容</label>
                <textarea id="content" name="content" required rows="8" maxlength="2000"
                          placeholder="请输入消息内容..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"></textarea>
                <div class="flex justify-between items-center mt-1">
                    <div class="text-sm text-gray-500">
                        支持简单的文本格式，最多2000字符
                    </div>
                    <div class="text-sm text-gray-500">
                        <span id="contentLength">0</span>/2000
                    </div>
                </div>
            </div>
            
            <!-- 消息选项 -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-700 mb-3">消息选项</h4>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="checkbox" id="requireRead" name="require_read" 
                               class="checkbox">
                        <span class="ml-2 text-sm text-gray-700">要求确认已读</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="allowReply" name="allow_reply" checked
                               class="checkbox">
                        <span class="ml-2 text-sm text-gray-700">允许回复</span>
                    </label>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex justify-between items-center pt-4 border-t border-gray-200">
                <a href="{% url 'communications:admin:message_center' %}" 
                   class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors">
                    取消
                </a>
                
                <div class="flex space-x-3">
                    <button type="button" id="saveDraftBtn" 
                            class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                        保存草稿
                    </button>
                    <button type="submit" id="sendBtn" 
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center">
                        <i data-lucide="send" class="w-4 h-4 mr-2"></i>
                        发送消息
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 发送确认模态框 -->
<div id="confirmModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                    <i data-lucide="send" class="w-5 h-5 text-blue-600"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900">确认发送</h3>
            </div>
            <div id="confirmContent" class="text-gray-600 mb-6">
                <!-- 动态内容 -->
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="closeConfirmModal()" 
                        class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                    取消
                </button>
                <button type="button" id="confirmSend" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    确认发送
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedRecipients = new Set();
let departmentData = {};

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadDepartmentData();
    setupEventListeners();
});

function setupEventListeners() {
    // 内容长度计数
    document.getElementById('content').addEventListener('input', function() {
        const length = this.value.length;
        document.getElementById('contentLength').textContent = length;
        
        if (length > 2000) {
            this.value = this.value.substring(0, 2000);
            document.getElementById('contentLength').textContent = 2000;
        }
    });
    
    // 表单提交
    document.getElementById('messageForm').addEventListener('submit', function(e) {
        e.preventDefault();
        showConfirmModal();
    });
    
    // 保存草稿
    document.getElementById('saveDraftBtn').addEventListener('click', function() {
        saveDraft();
    });
}

// 加载部门和员工数据
function loadDepartmentData() {
    // 从API获取真实的员工数据
    fetch('/communications/api/staff-data/', {
        method: 'GET',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            departmentData = data.departments;
            renderDepartmentList();
        } else {
            console.error('获取员工数据失败:', data.error);
            // 如果API失败，使用默认数据
            departmentData = {
                '总经理室': [
                    {id: 1, name: '张总', position: '总经理'},
                    {id: 2, name: '李总', position: '副总经理'}
                ],
                '人力资源部': [
                    {id: 3, name: '王经理', position: '人力资源经理'},
                    {id: 4, name: '赵主管', position: '人力资源主管'},
                    {id: 5, name: '刘专员', position: '人力资源专员'}
                ]
            };
            renderDepartmentList();
        }
    })
    .catch(error => {
        console.error('加载员工数据错误:', error);
        // 错误时使用默认数据
        departmentData = {
            '总经理室': [
                {id: 1, name: '张总', position: '总经理'}
            ]
        };
        renderDepartmentList();
    });
}

function renderDepartmentList() {
    const container = document.getElementById('departmentList');
    let html = '';
    
    for (const [deptName, staff] of Object.entries(departmentData)) {
        html += `
            <div class="department-group">
                <div class="department-header" onclick="toggleDepartment('${deptName}')">
                    <div class="flex items-center flex-1">
                        <i data-lucide="chevron-right" class="w-4 h-4 mr-2 chevron"></i>
                        <span>${deptName}</span>
                        <span class="ml-2 text-sm text-gray-500">(${staff.length}人)</span>
                    </div>
                    <button type="button" onclick="event.stopPropagation(); selectDepartment('${deptName}')" 
                            class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                        全选
                    </button>
                </div>
                <div class="department-staff" id="dept-${deptName}">
        `;
        
        staff.forEach(person => {
            html += `
                <label class="staff-checkbox">
                    <input type="checkbox" class="checkbox mr-2" 
                           value="${person.id}" onchange="toggleRecipient(${person.id}, '${person.name}', '${deptName}')">
                    <div class="flex-1">
                        <div class="font-medium text-sm">${person.name}</div>
                        <div class="text-xs text-gray-500">${person.position}</div>
                    </div>
                </label>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
    }
    
    container.innerHTML = html;
    
    // 初始化Lucide图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

function toggleDepartment(deptName) {
    const staffDiv = document.getElementById(`dept-${deptName}`);
    const chevron = event.currentTarget.querySelector('.chevron');
    
    if (staffDiv.classList.contains('show')) {
        staffDiv.classList.remove('show');
        chevron.style.transform = 'rotate(0deg)';
    } else {
        staffDiv.classList.add('show');
        chevron.style.transform = 'rotate(90deg)';
    }
}

function selectDepartment(deptName) {
    const staff = departmentData[deptName];
    staff.forEach(person => {
        if (!selectedRecipients.has(person.id)) {
            selectedRecipients.add(person.id);
            const checkbox = document.querySelector(`input[value="${person.id}"]`);
            if (checkbox) checkbox.checked = true;
        }
    });
    updateRecipientDisplay();
}

function toggleRecipient(id, name, department) {
    if (selectedRecipients.has(id)) {
        selectedRecipients.delete(id);
    } else {
        selectedRecipients.add(id);
    }
    updateRecipientDisplay();
}

function updateRecipientDisplay() {
    const container = document.getElementById('selectedRecipients');
    const placeholder = document.getElementById('recipientPlaceholder');
    
    if (selectedRecipients.size === 0) {
        placeholder.style.display = 'block';
        return;
    }
    
    placeholder.style.display = 'none';
    
    let html = '';
    selectedRecipients.forEach(id => {
        // 查找对应的员工信息
        let person = null;
        let deptName = '';
        for (const [dept, staff] of Object.entries(departmentData)) {
            const found = staff.find(p => p.id === id);
            if (found) {
                person = found;
                deptName = dept;
                break;
            }
        }
        
        if (person) {
            html += `
                <span class="recipient-tag">
                    ${person.name} (${deptName})
                    <span class="remove-btn" onclick="removeRecipient(${id})">
                        <i data-lucide="x" class="w-3 h-3"></i>
                    </span>
                </span>
            `;
        }
    });
    
    // 更新HTML（保留placeholder元素）
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    
    // 清除除了placeholder之外的所有内容
    Array.from(container.children).forEach(child => {
        if (child.id !== 'recipientPlaceholder') {
            container.removeChild(child);
        }
    });
    
    // 添加新的recipient tags
    Array.from(tempDiv.children).forEach(child => {
        container.appendChild(child);
    });
    
    // 重新初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

function removeRecipient(id) {
    selectedRecipients.delete(id);
    const checkbox = document.querySelector(`input[value="${id}"]`);
    if (checkbox) checkbox.checked = false;
    updateRecipientDisplay();
}

function selectAll() {
    Object.values(departmentData).flat().forEach(person => {
        selectedRecipients.add(person.id);
        const checkbox = document.querySelector(`input[value="${person.id}"]`);
        if (checkbox) checkbox.checked = true;
    });
    updateRecipientDisplay();
}

function clearSelection() {
    selectedRecipients.clear();
    document.querySelectorAll('input[type="checkbox"]').forEach(cb => {
        cb.checked = false;
    });
    updateRecipientDisplay();
}

function showConfirmModal() {
    if (selectedRecipients.size === 0) {
        alert('请选择至少一个收件人');
        return;
    }
    
    const subject = document.getElementById('subject').value;
    const messageType = document.getElementById('messageType').value;
    const priority = document.getElementById('priority').value;
    
    if (!subject || !messageType || !priority) {
        alert('请填写完整的消息信息');
        return;
    }
    
    const content = `
        <p><strong>主题：</strong>${subject}</p>
        <p><strong>收件人：</strong>${selectedRecipients.size}人</p>
        <p><strong>类型：</strong>${getMessageTypeText(messageType)}</p>
        <p><strong>优先级：</strong>${getPriorityText(priority)}</p>
        <p class="mt-3 text-sm">确定要发送这条消息吗？</p>
    `;
    
    document.getElementById('confirmContent').innerHTML = content;
    document.getElementById('confirmModal').classList.remove('hidden');
    document.getElementById('confirmModal').classList.add('flex');
}

function closeConfirmModal() {
    document.getElementById('confirmModal').classList.add('hidden');
    document.getElementById('confirmModal').classList.remove('flex');
}

function getMessageTypeText(type) {
    const types = {
        'PERSONAL': '个人消息',
        'SYSTEM': '系统通知',
        'EVALUATION': '考评相关',
        'ANNOUNCEMENT': '公告通知'
    };
    return types[type] || type;
}

function getPriorityText(priority) {
    const priorities = {
        'HIGH': '高优先级',
        'MEDIUM': '中等优先级',
        'LOW': '低优先级'
    };
    return priorities[priority] || priority;
}

// 确认发送
document.getElementById('confirmSend').addEventListener('click', function() {
    sendMessage();
});

function sendMessage() {
    const formData = {
        message_type: document.getElementById('messageType').value,
        priority: document.getElementById('priority').value,
        subject: document.getElementById('subject').value,
        content: document.getElementById('content').value,
        recipients: Array.from(selectedRecipients),
        require_read: document.getElementById('requireRead').checked,
        allow_reply: document.getElementById('allowReply').checked
    };
    
    // 发送按钮loading状态
    const sendBtn = document.getElementById('confirmSend');
    const originalText = sendBtn.innerHTML;
    sendBtn.innerHTML = '<i data-lucide="loader" class="w-4 h-4 mr-2 animate-spin"></i>发送中...';
    sendBtn.disabled = true;
    
    fetch('/communications/api/messages/send/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('消息发送成功！');
            window.location.href = '/communications/admin/messages/';
        } else {
            alert('发送失败：' + (data.error || '未知错误'));
            sendBtn.innerHTML = originalText;
            sendBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('发送错误:', error);
        alert('发送失败，请稍后重试');
        sendBtn.innerHTML = originalText;
        sendBtn.disabled = false;
    });
    
    closeConfirmModal();
}

function saveDraft() {
    // 保存草稿功能
    alert('草稿保存功能待实现');
}

// 点击模态框外部关闭
document.getElementById('confirmModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeConfirmModal();
    }
});
</script>
{% endblock %}