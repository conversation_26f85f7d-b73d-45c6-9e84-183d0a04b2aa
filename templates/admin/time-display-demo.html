{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}时间显示效果演示{% endblock %}
{% block page_description %}展示考评模板和考评批次页面的时间显示美化效果{% endblock %}

{% block admin_content %}
<div class="space-y-8">
    <!-- 页面标题 -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <h1 class="text-2xl font-bold mb-2">时间显示美化效果演示</h1>
        <p class="text-blue-100">展示考评模板和考评批次页面中时间显示的各种美化效果</p>
    </div>

    <!-- 时间卡片展示 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">时间卡片样式</h2>
            <p class="text-sm text-gray-600 mt-1">用于详情页面的时间信息展示</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- 创建时间卡片 -->
                <div class="time-card blue">
                    <div class="flex items-center mb-2">
                        <i data-lucide="calendar-plus" class="time-icon text-blue-600"></i>
                        <label class="block font-medium text-blue-700">创建时间</label>
                    </div>
                    <div class="space-y-1">
                        <p class="time-primary" data-relative-time="2024-01-15T09:30:00">2024年1月15日</p>
                        <p class="time-secondary">09:30:00</p>
                    </div>
                    <div class="time-status-indicator"></div>
                </div>

                <!-- 开始时间卡片 -->
                <div class="time-card green">
                    <div class="flex items-center mb-2">
                        <i data-lucide="play-circle" class="time-icon text-green-600"></i>
                        <label class="block font-medium text-green-700">开始时间</label>
                    </div>
                    <div class="space-y-1">
                        <p class="time-primary" data-relative-time="2024-02-01T08:00:00">2024年2月1日</p>
                        <p class="time-secondary">08:00:00</p>
                    </div>
                    <div class="time-status-indicator success"></div>
                </div>

                <!-- 结束时间卡片 -->
                <div class="time-card red">
                    <div class="flex items-center mb-2">
                        <i data-lucide="stop-circle" class="time-icon text-red-600"></i>
                        <label class="block font-medium text-red-700">结束时间</label>
                    </div>
                    <div class="space-y-1">
                        <p class="time-primary" data-relative-time="2024-02-28T18:00:00">2024年2月28日</p>
                        <p class="time-secondary">18:00:00</p>
                    </div>
                    <div class="time-status-indicator pending"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 时间范围展示 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">时间范围样式</h2>
            <p class="text-sm text-gray-600 mt-1">用于列表页面的时间范围展示</p>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="time-range">
                    <i data-lucide="calendar-range" class="time-icon text-blue-500"></i>
                    <div class="flex flex-col">
                        <span class="time-label">2月1日 ~ 2月28日</span>
                        <span class="text-xs text-gray-500 mt-1">2024年</span>
                    </div>
                </div>
                
                <div class="time-range">
                    <i data-lucide="calendar-range" class="time-icon text-purple-500"></i>
                    <div class="flex flex-col">
                        <span class="time-label success">3月1日 ~ 3月31日</span>
                        <span class="text-xs text-gray-500 mt-1">2024年</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 时间标签展示 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">时间标签样式</h2>
            <p class="text-sm text-gray-600 mt-1">用于各种时间状态的标签展示</p>
        </div>
        <div class="p-6">
            <div class="flex flex-wrap gap-4">
                <span class="time-label">
                    <i data-lucide="calendar" class="time-icon"></i>
                    默认时间标签
                </span>
                
                <span class="time-label success">
                    <i data-lucide="check-circle" class="time-icon"></i>
                    成功状态
                </span>
                
                <span class="time-label danger">
                    <i data-lucide="x-circle" class="time-icon"></i>
                    危险状态
                </span>
            </div>
        </div>
    </div>

    <!-- 时间徽章展示 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">时间徽章样式</h2>
            <p class="text-sm text-gray-600 mt-1">用于表格和列表中的时间信息展示</p>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="time-list-item">
                    <i data-lucide="calendar" class="time-icon text-blue-500"></i>
                    <span class="text-gray-600 mr-2">创建：</span>
                    <div class="time-badge">
                        <i data-lucide="calendar-plus" class="time-icon"></i>
                        <div class="flex flex-col">
                            <span class="font-medium text-gray-900">2024年1月15日</span>
                            <span class="text-xs text-gray-500">09:30</span>
                        </div>
                    </div>
                </div>

                <div class="time-list-item">
                    <i data-lucide="play-circle" class="time-icon text-green-500"></i>
                    <div class="time-badge success">
                        <i data-lucide="calendar-plus" class="time-icon"></i>
                        <div class="flex flex-col">
                            <span class="font-medium">2024年2月1日</span>
                            <span class="text-xs opacity-75">08:00</span>
                        </div>
                    </div>
                </div>

                <div class="time-list-item">
                    <i data-lucide="stop-circle" class="time-icon text-red-500"></i>
                    <div class="time-badge danger">
                        <i data-lucide="calendar-x" class="time-icon"></i>
                        <div class="flex flex-col">
                            <span class="font-medium">2024年2月28日</span>
                            <span class="text-xs opacity-75">18:00</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 时间进度条展示 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">时间进度条</h2>
            <p class="text-sm text-gray-600 mt-1">显示时间进度的可视化组件</p>
        </div>
        <div class="p-6">
            <div class="space-y-6">
                <div>
                    <div class="flex justify-between text-sm mb-2">
                        <span>考评进度</span>
                        <span>65%</span>
                    </div>
                    <div class="time-progress">
                        <div class="time-progress-bar" data-progress="65"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between text-sm mb-2">
                        <span>时间进度</span>
                        <span>80%</span>
                    </div>
                    <div class="time-progress">
                        <div class="time-progress-bar" data-progress="80"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用说明 -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-start">
            <i data-lucide="info" class="w-6 h-6 text-blue-600 mt-1 flex-shrink-0"></i>
            <div class="ml-4">
                <h3 class="text-lg font-semibold text-blue-900 mb-2">使用说明</h3>
                <div class="text-sm text-blue-800 space-y-2">
                    <p><strong>时间卡片：</strong>用于详情页面展示重要的时间信息，支持悬停动画效果。</p>
                    <p><strong>时间范围：</strong>用于列表页面简洁地展示时间范围信息。</p>
                    <p><strong>时间标签：</strong>用于标识不同状态的时间信息，支持多种颜色主题。</p>
                    <p><strong>时间徽章：</strong>用于表格和列表中的时间信息展示，提供良好的可读性。</p>
                    <p><strong>相对时间：</strong>自动显示相对于当前时间的描述，如"2小时前"、"3天前"等。</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
