{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}多选框测试页面{% endblock %}
{% block page_description %}测试新的多选框样式是否生效{% endblock %}

{% block admin_content %}
<div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">多选框样式测试</h3>
    
    <div class="space-y-6">
        <!-- 原有样式 -->
        <div>
            <h4 class="font-medium mb-2">原有样式 (18px)</h4>
            <input type="checkbox" class="checkbox" style="width: 18px; height: 18px;">
            <span class="ml-2">原有多选框</span>
        </div>
        
        <!-- 新样式 -->
        <div>
            <h4 class="font-medium mb-2">新样式 (20px)</h4>
            <input type="checkbox" class="checkbox">
            <span class="ml-2">默认多选框</span>
        </div>
        
        <!-- 表格专用样式 -->
        <div>
            <h4 class="font-medium mb-2">表格专用样式 (22px)</h4>
            <input type="checkbox" class="checkbox-table">
            <span class="ml-2">表格多选框</span>
        </div>
        
        <!-- 大尺寸样式 -->
        <div>
            <h4 class="font-medium mb-2">大尺寸样式 (24px)</h4>
            <input type="checkbox" class="checkbox-lg">
            <span class="ml-2">大尺寸多选框</span>
        </div>
        
        <!-- 超大尺寸样式 -->
        <div>
            <h4 class="font-medium mb-2">超大尺寸样式 (28px)</h4>
            <input type="checkbox" class="checkbox-xl">
            <span class="ml-2">超大尺寸多选框</span>
        </div>
    </div>
    
    <!-- 表格测试 -->
    <div class="mt-8">
        <h4 class="font-medium mb-4">表格中的多选框测试</h4>
        <table class="min-w-full border border-gray-200">
            <thead>
                <tr class="bg-gray-50">
                    <th class="border border-gray-200 px-4 py-2">
                        <input type="checkbox" class="checkbox-table" id="testSelectAll">
                    </th>
                    <th class="border border-gray-200 px-4 py-2 text-left">项目名称</th>
                    <th class="border border-gray-200 px-4 py-2 text-left">状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="border border-gray-200 px-4 py-2 checkbox-cell text-center">
                        <input type="checkbox" class="checkbox-table test-checkbox" value="1">
                    </td>
                    <td class="border border-gray-200 px-4 py-2">测试项目 1</td>
                    <td class="border border-gray-200 px-4 py-2 text-green-600">活跃</td>
                </tr>
                <tr>
                    <td class="border border-gray-200 px-4 py-2 checkbox-cell text-center">
                        <input type="checkbox" class="checkbox-table test-checkbox" value="2">
                    </td>
                    <td class="border border-gray-200 px-4 py-2">测试项目 2</td>
                    <td class="border border-gray-200 px-4 py-2 text-yellow-600">待审核</td>
                </tr>
                <tr>
                    <td class="border border-gray-200 px-4 py-2 checkbox-cell text-center">
                        <input type="checkbox" class="checkbox-table test-checkbox" value="3">
                    </td>
                    <td class="border border-gray-200 px-4 py-2">测试项目 3</td>
                    <td class="border border-gray-200 px-4 py-2 text-red-600">已禁用</td>
                </tr>
            </tbody>
        </table>
        
        <div class="mt-4 p-4 bg-gray-50 rounded">
            <p id="testCounter" class="text-sm text-gray-600">已选择 0 项</p>
            <div class="mt-2 space-x-2">
                <button onclick="testSelectAll()" class="px-3 py-1 bg-blue-600 text-white rounded text-sm">全选</button>
                <button onclick="testClearAll()" class="px-3 py-1 bg-gray-600 text-white rounded text-sm">清除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/enhanced-checkbox.js' %}"></script>
<script>
// 测试页面脚本
document.addEventListener('DOMContentLoaded', function() {
    const testCheckboxes = document.querySelectorAll('.test-checkbox');
    const testSelectAllCheckbox = document.getElementById('testSelectAll');
    const testCounter = document.getElementById('testCounter');
    
    function updateTestCounter() {
        const checkedCount = document.querySelectorAll('.test-checkbox:checked').length;
        testCounter.textContent = `已选择 ${checkedCount} 项`;
        
        if (checkedCount === 0) {
            testSelectAllCheckbox.checked = false;
            testSelectAllCheckbox.indeterminate = false;
        } else if (checkedCount === testCheckboxes.length) {
            testSelectAllCheckbox.checked = true;
            testSelectAllCheckbox.indeterminate = false;
        } else {
            testSelectAllCheckbox.checked = false;
            testSelectAllCheckbox.indeterminate = true;
        }
    }
    
    testCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateTestCounter);
    });
    
    testSelectAllCheckbox.addEventListener('change', function() {
        testCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateTestCounter();
    });
    
    window.testSelectAll = function() {
        testCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
        updateTestCounter();
    };
    
    window.testClearAll = function() {
        testCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        updateTestCounter();
    };
    
    console.log('多选框测试页面已加载');
    console.log('CSS样式是否加载:', !!document.querySelector('style, link[href*="design-system.css"]'));
    console.log('增强脚本是否加载:', typeof window.EnhancedCheckbox !== 'undefined');
});
</script>
{% endblock %}
