{% extends "admin/base_admin.html" %}

{% block page_title %}权限管理{% endblock %}
{% block page_description %}管理用户角色和权限分配{% endblock %}

{% block header_actions %}
<button id="testPermissionsBtn" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700">
    <i data-lucide="zap" class="w-4 h-4 mr-2 inline"></i>测试权限
</button>
<button id="batchRoleBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
    <i data-lucide="users" class="w-4 h-4 mr-2 inline"></i>批量设置
</button>
{% endblock %}

{% block admin_content %}
<!-- Permission Overview Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-lg">
                <i data-lucide="shield-check" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总角色数</p>
                <p class="text-2xl font-bold text-gray-900">7</p>
                <p class="text-xs text-blue-600 mt-1">完整层级体系</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-lg">
                <i data-lucide="key" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总权限数</p>
                <p class="text-2xl font-bold text-gray-900">{{ total_permissions }}</p>
                <p class="text-xs text-green-600 mt-1">6个权限类别</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="p-3 bg-yellow-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">管理员数量</p>
                <p class="text-2xl font-bold text-gray-900">{{ admin_count }}</p>
                <p class="text-xs text-yellow-600 mt-1">含各级管理员</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="p-3 bg-purple-100 rounded-lg">
                <i data-lucide="building" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">活跃部门</p>
                <p class="text-2xl font-bold text-gray-900">{{ active_departments }}</p>
                <p class="text-xs text-purple-600 mt-1">数据隔离控制</p>
            </div>
        </div>
    </div>
</div>

<!-- Role Management Section -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="users-cog" class="w-5 h-5 mr-2 text-gray-500"></i>
            角色权限映射
        </h3>
    </div>
    <div class="p-6">
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left py-3 px-4 font-medium text-gray-700">角色</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">权限数量</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">主要权限</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">用户数量</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for role_info in role_mappings %}
                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                        <td class="py-4 px-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 {{ role_info.color_class }} rounded-lg flex items-center justify-center">
                                    <i data-lucide="{{ role_info.icon }}" class="w-4 h-4 {{ role_info.text_color }}"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">{{ role_info.display_name }}</p>
                                    <p class="text-sm text-gray-500">{{ role_info.code }}</p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4 px-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ role_info.permission_count }} 个权限
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <div class="flex flex-wrap gap-1">
                                {% for perm in role_info.main_permissions %}
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700">
                                    {{ perm }}
                                </span>
                                {% endfor %}
                            </div>
                        </td>
                        <td class="py-4 px-4">
                            <span class="text-sm font-medium text-gray-900">{{ role_info.user_count }}</span>
                        </td>
                        <td class="py-4 px-4">
                            <div class="flex items-center space-x-2">
                                <button onclick="viewRoleDetails('{{ role_info.code }}')" 
                                    class="p-1 text-blue-600 hover:text-blue-800">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </button>
                                <button onclick="editRolePermissions('{{ role_info.code }}')" 
                                    class="p-1 text-green-600 hover:text-green-800">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Staff Role Assignment -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="user-check" class="w-5 h-5 mr-2 text-gray-500"></i>
                员工角色分配
            </h3>
            <div class="flex items-center space-x-3">
                <div class="relative">
                    <input type="text" id="staffSearch" placeholder="搜索员工..." value="{{ search }}"
                        class="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-64">
                    <i data-lucide="search" class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <select id="roleFilter" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">所有角色</option>
                    {% for role_code, role_name in role_choices %}
                    <option value="{{ role_code }}" {% if role_filter == role_code %}selected{% endif %}>{{ role_name }}</option>
                    {% endfor %}
                </select>
                <select id="departmentFilter" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">所有部门</option>
                    {% for dept in departments %}
                    <option value="{{ dept.id }}" {% if department_filter == dept.id|stringformat:"s" %}selected{% endif %}>{{ dept.name }}</option>
                    {% endfor %}
                </select>
                <button onclick="performServerSideFilter()" class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                    <i data-lucide="search" class="w-4 h-4 inline mr-1"></i>搜索
                </button>
                <button onclick="clearFilters()" class="px-4 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 text-gray-700">
                    <i data-lucide="x" class="w-4 h-4 inline mr-1"></i>清空
                </button>
            </div>
        </div>
    </div>
    <!-- 批量操作工具栏 -->
    <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <span class="text-sm text-gray-600">批量操作：</span>
                <button id="batchDeleteBtn" onclick="showBatchDeleteConfirm()" class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    <i data-lucide="trash-2" class="w-4 h-4 inline mr-1"></i>批量删除
                </button>
            </div>
            <span id="selectedCount" class="text-sm text-gray-600">已选择 0 个员工</span>
        </div>
    </div>
    <div class="p-6">
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left py-3 px-4 font-medium text-gray-700">
                            <input type="checkbox" id="selectAll" class="checkbox">
                        </th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">员工</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">部门</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">当前角色</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">最后登录</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">操作</th>
                    </tr>
                </thead>
                <tbody id="staffTableBody">
                    {% for staff in staff_list %}
                    <tr class="border-b border-gray-100 hover:bg-gray-50" data-staff-id="{{ staff.id }}">
                        <td class="py-4 px-4">
                            <input type="checkbox" class="staff-checkbox checkbox" value="{{ staff.id }}">
                        </td>
                        <td class="py-4 px-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium text-gray-700">{{ staff.name|first }}</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">{{ staff.name }}</p>
                                    <p class="text-sm text-gray-500">{{ staff.employee_no }}</p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4 px-4">
                            <span class="text-sm text-gray-900">{{ staff.department.name }}</span>
                        </td>
                        <td class="py-4 px-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                {% if staff.role == 'super_admin' %}bg-red-100 text-red-800
                                {% elif staff.role == 'system_admin' %}bg-orange-100 text-orange-800
                                {% elif staff.role == 'hr_admin' %}bg-purple-100 text-purple-800
                                {% elif staff.role == 'eval_admin' %}bg-blue-100 text-blue-800
                                {% elif staff.role == 'dept_manager' %}bg-green-100 text-green-800
                                {% elif staff.role == 'admin' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ staff.get_role_display }}
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <span class="text-sm text-gray-500">
                                {% if staff.last_login %}
                                    {{ staff.last_login|date:"Y-m-d H:i" }}
                                {% else %}
                                    从未登录
                                {% endif %}
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <div class="flex items-center space-x-2">
                                <button onclick="editStaffRole({{ staff.id }}, '{{ staff.role }}')" 
                                    class="p-1 text-blue-600 hover:text-blue-800" title="编辑角色">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </button>
                                <button onclick="viewStaffPermissions({{ staff.id }})" 
                                    class="p-1 text-green-600 hover:text-green-800" title="查看权限">
                                    <i data-lucide="shield-check" class="w-4 h-4"></i>
                                </button>
                                <button onclick="viewStaffHistory({{ staff.id }})" 
                                    class="p-1 text-gray-600 hover:text-gray-800" title="操作历史">
                                    <i data-lucide="history" class="w-4 h-4"></i>
                                </button>
                                <button onclick="deleteStaff({{ staff.id }}, '{{ staff.name }}')" 
                                    class="p-1 text-red-600 hover:text-red-800" title="删除员工">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if is_paginated %}
        <div class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-700">
                显示第 {{ page_obj.start_index }}-{{ page_obj.end_index }} 项，共 {{ paginator.count }} 项
            </div>
            <div class="flex items-center space-x-2">
                <!-- Previous Page -->
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}" 
                       class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 text-gray-700">
                        <i data-lucide="chevron-left" class="w-4 h-4 inline mr-1"></i>上一页
                    </a>
                {% else %}
                    <span class="px-3 py-2 border border-gray-200 rounded-md text-sm text-gray-400 cursor-not-allowed">
                        <i data-lucide="chevron-left" class="w-4 h-4 inline mr-1"></i>上一页
                    </span>
                {% endif %}

                <!-- Page Numbers -->
                {% for num in paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="px-3 py-2 bg-blue-600 text-white rounded-md text-sm font-medium">
                            {{ num }}
                        </span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}"
                           class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 text-gray-700">
                            {{ num }}
                        </a>
                    {% elif num == 1 %}
                        <a href="?page=1{% if search %}&search={{ search }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}"
                           class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 text-gray-700">
                            1
                        </a>
                        {% if page_obj.number > 4 %}
                            <span class="px-2 py-2 text-gray-500">...</span>
                        {% endif %}
                    {% elif num == paginator.num_pages %}
                        {% if page_obj.number < paginator.num_pages|add:'-3' %}
                            <span class="px-2 py-2 text-gray-500">...</span>
                        {% endif %}
                        <a href="?page={{ paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}"
                           class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 text-gray-700">
                            {{ paginator.num_pages }}
                        </a>
                    {% endif %}
                {% endfor %}

                <!-- Next Page -->
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}{% if department_filter %}&department={{ department_filter }}{% endif %}" 
                       class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50 text-gray-700">
                        下一页<i data-lucide="chevron-right" class="w-4 h-4 inline ml-1"></i>
                    </a>
                {% else %}
                    <span class="px-3 py-2 border border-gray-200 rounded-md text-sm text-gray-400 cursor-not-allowed">
                        下一页<i data-lucide="chevron-right" class="w-4 h-4 inline ml-1"></i>
                    </span>
                {% endif %}
            </div>
        </div>
        {% else %}
        <div class="flex items-center justify-center mt-6">
            <div class="text-sm text-gray-500">
                共 {{ paginator.count }} 个员工（当前页显示全部）
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Role Edit Modal -->
<div id="roleEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">编辑员工角色</h3>
            </div>
            <div class="p-6">
                <form id="roleEditForm">
                    <input type="hidden" id="editStaffId">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">员工信息</label>
                        <div id="editStaffInfo" class="text-sm text-gray-600"></div>
                    </div>
                    <div class="mb-4">
                        <label for="newRole" class="block text-sm font-medium text-gray-700 mb-2">新角色</label>
                        <select id="newRole" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            {% for role_code, role_name in role_choices %}
                            <option value="{{ role_code }}">{{ role_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-4">
                        <label for="changeReason" class="block text-sm font-medium text-gray-700 mb-2">变更原因</label>
                        <textarea id="changeReason" rows="3" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="请输入角色变更原因..."></textarea>
                    </div>
                </form>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeRoleEditModal()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">取消</button>
                <button onclick="saveRoleChange()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- Batch Role Assignment Modal -->
<div id="batchRoleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">批量角色设置</h3>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">已选择 <span id="selectedCount">0</span> 个员工</p>
                    <div id="selectedStaffList" class="max-h-32 overflow-y-auto border border-gray-200 rounded p-2 text-sm"></div>
                </div>
                <div class="mb-4">
                    <label for="batchNewRole" class="block text-sm font-medium text-gray-700 mb-2">设置角色</label>
                    <select id="batchNewRole" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        {% for role_code, role_name in role_choices %}
                        <option value="{{ role_code }}">{{ role_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-4">
                    <label for="batchReason" class="block text-sm font-medium text-gray-700 mb-2">变更原因</label>
                    <textarea id="batchReason" rows="3" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="请输入批量变更原因..."></textarea>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeBatchRoleModal()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">取消</button>
                <button onclick="saveBatchRoleChange()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">批量保存</button>
            </div>
        </div>
    </div>
</div>

<!-- Single Staff Delete Modal ---->
<div id="staffDeleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="alert-triangle" class="w-5 h-5 mr-2 text-red-600"></i>
                    删除员工确认
                </h3>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">您确定要删除以下员工吗？</p>
                    <div id="deleteStaffInfo" class="bg-red-50 p-3 rounded border border-red-200 text-sm"></div>
                </div>
                <div id="deleteConflicts" class="mb-4 hidden">
                    <p class="text-sm font-medium text-red-700 mb-2">检测到数据冲突：</p>
                    <div id="conflictsList" class="bg-red-50 p-3 rounded border border-red-200 text-sm"></div>
                    <label class="flex items-center mt-3">
                        <input type="checkbox" id="forceDelete" class="checkbox">
                        <span class="ml-2 text-sm text-gray-700">我了解风险，强制删除</span>
                    </label>
                </div>
                <div class="mb-4">
                    <label for="deleteReason" class="block text-sm font-medium text-gray-700 mb-2">删除原因 <span class="text-red-500">*</span></label>
                    <textarea id="deleteReason" rows="3" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                        placeholder="请输入删除原因..."></textarea>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeStaffDeleteModal()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">取消</button>
                <button id="confirmDeleteBtn" onclick="confirmStaffDelete()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- Batch Delete Modal -->
<div id="batchDeleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="alert-triangle" class="w-5 h-5 mr-2 text-red-600"></i>
                    批量删除员工确认
                </h3>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">您确定要删除以下 <span id="batchDeleteCount">0</span> 个员工吗？</p>
                    <div id="batchDeleteList" class="max-h-32 overflow-y-auto border border-gray-200 rounded p-2 text-sm bg-red-50"></div>
                </div>
                <div id="batchDeleteConflicts" class="mb-4 hidden">
                    <p class="text-sm font-medium text-red-700 mb-2">检测到以下员工存在数据冲突：</p>
                    <div id="batchConflictsList" class="max-h-32 overflow-y-auto bg-red-50 p-3 rounded border border-red-200 text-sm"></div>
                    <label class="flex items-center mt-3">
                        <input type="checkbox" id="batchForceDelete" class="checkbox">
                        <span class="ml-2 text-sm text-gray-700">我了解风险，强制删除所有员工</span>
                    </label>
                </div>
                <div class="mb-4">
                    <label for="batchDeleteReason" class="block text-sm font-medium text-gray-700 mb-2">删除原因 <span class="text-red-500">*</span></label>
                    <textarea id="batchDeleteReason" rows="3" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                        placeholder="请输入批量删除原因..."></textarea>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeBatchDeleteModal()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">取消</button>
                <button id="confirmBatchDeleteBtn" onclick="confirmBatchDelete()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">确认批量删除</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Search and filter functionality - 使用服务器端筛选
    document.getElementById('staffSearch').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performServerSideFilter();
        }
    });
    
    document.getElementById('roleFilter').addEventListener('change', performServerSideFilter);
    document.getElementById('departmentFilter').addEventListener('change', performServerSideFilter);

    function performServerSideFilter() {
        const searchTerm = document.getElementById('staffSearch').value.trim();
        const roleFilter = document.getElementById('roleFilter').value;
        const departmentFilter = document.getElementById('departmentFilter').value;
        
        // 构建URL参数
        const params = new URLSearchParams();
        if (searchTerm) {
            params.append('search', searchTerm);
        }
        if (roleFilter) {
            params.append('role', roleFilter);
        }
        if (departmentFilter) {
            params.append('department', departmentFilter);
        }
        
        // 重新加载页面并保持筛选条件
        const url = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
        window.location.href = url;
    }

    function clearFilters() {
        document.getElementById('staffSearch').value = '';
        document.getElementById('roleFilter').value = '';
        document.getElementById('departmentFilter').value = '';
        window.location.href = window.location.pathname;
    }

    // 设置筛选器的初始值（从URL参数读取）
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const search = urlParams.get('search');
        const role = urlParams.get('role');
        const department = urlParams.get('department');
        
        if (search) {
            document.getElementById('staffSearch').value = search;
        }
        if (role) {
            document.getElementById('roleFilter').value = role;
        }
        if (department) {
            document.getElementById('departmentFilter').value = department;
        }
    });

    // Role editing functions
    function editStaffRole(staffId, currentRole) {
        document.getElementById('editStaffId').value = staffId;
        document.getElementById('newRole').value = currentRole;
        
        // Get staff info from the table row
        const row = document.querySelector(`tr[data-staff-id="${staffId}"]`);
        const staffName = row.querySelector('td:nth-child(2) p').textContent;
        const staffNo = row.querySelector('td:nth-child(2) p:nth-child(2)').textContent;
        const department = row.querySelector('td:nth-child(3)').textContent;
        
        document.getElementById('editStaffInfo').innerHTML = `
            <strong>${staffName}</strong> (${staffNo})<br>
            部门：${department}
        `;
        
        document.getElementById('roleEditModal').classList.remove('hidden');
    }

    function closeRoleEditModal() {
        document.getElementById('roleEditModal').classList.add('hidden');
        document.getElementById('roleEditForm').reset();
    }

    function saveRoleChange() {
        const staffId = document.getElementById('editStaffId').value;
        const newRole = document.getElementById('newRole').value;
        const reason = document.getElementById('changeReason').value;

        if (!reason.trim()) {
            alert('请输入变更原因');
            return;
        }

        // AJAX call to save role change
        fetch('{% url "organizations:admin:edit_staff_role" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || getCookie('csrftoken')
            },
            body: JSON.stringify({
                staff_id: staffId,
                new_role: newRole,
                reason: reason
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('角色更新成功', 'success');
                closeRoleEditModal();
                location.reload(); // Refresh page to show updated data
            } else {
                showNotification(data.message || '更新失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('网络错误', 'error');
        });
    }

    // Batch role assignment
    document.getElementById('batchRoleBtn').addEventListener('click', function() {
        const selectedCheckboxes = document.querySelectorAll('.staff-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            alert('请先选择要批量设置的员工');
            return;
        }

        const selectedStaff = [];
        selectedCheckboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            const staffName = row.querySelector('td:nth-child(2) p').textContent;
            selectedStaff.push(staffName);
        });

        document.getElementById('selectedCount').textContent = selectedStaff.length;
        document.getElementById('selectedStaffList').innerHTML = selectedStaff.join(', ');
        document.getElementById('batchRoleModal').classList.remove('hidden');
    });

    function closeBatchRoleModal() {
        document.getElementById('batchRoleModal').classList.add('hidden');
    }

    function saveBatchRoleChange() {
        const selectedIds = Array.from(document.querySelectorAll('.staff-checkbox:checked')).map(cb => cb.value);
        const newRole = document.getElementById('batchNewRole').value;
        const reason = document.getElementById('batchReason').value;

        if (!reason.trim()) {
            alert('请输入变更原因');
            return;
        }

        // AJAX call for batch role change
        fetch('{% url "organizations:admin:batch_role_assignment" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                staff_ids: selectedIds,
                new_role: newRole,
                reason: reason
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`成功更新 ${data.updated_count} 个员工的角色`, 'success');
                closeBatchRoleModal();
                location.reload();
            } else {
                showNotification(data.message || '批量更新失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('网络错误', 'error');
        });
    }

    // Select all functionality
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.staff-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Test permissions function
    document.getElementById('testPermissionsBtn').addEventListener('click', function() {
        if (confirm('确定要运行权限系统测试吗？这可能需要几分钟时间。')) {
            window.open('{% url "organizations:admin:test_permissions" %}', '_blank');
        }
    });

    // Other utility functions
    function viewRoleDetails(roleCode) {
        // 在当前页面查看角色详情
        const url = "{% url 'organizations:admin:permissions_manage' %}" + "role/" + roleCode + "/details/";
        window.location.href = url;
    }

    function editRolePermissions(roleCode) {
        // 角色权限编辑功能 - 显示说明信息
        const roleNames = {
            'super_admin': '超级管理员',
            'system_admin': '系统管理员', 
            'hr_admin': 'HR管理员',
            'eval_admin': '考评管理员',
            'dept_manager': '部门经理',
            'admin': '管理员',
            'employee': '员工'
        };
        const roleName = roleNames[roleCode] || roleCode;
        showNotification(`当前系统采用基于角色的权限控制(RBAC)，${roleName}角色的权限已预定义。\n\n要查看详细权限信息，请点击查看按钮👁️。\n\n如需调整员工权限，请修改员工角色来实现。`, 'info');
    }

    function viewStaffPermissions(staffId) {
        // 在当前页面查看员工权限页面
        const url = "{% url 'organizations:admin:permissions_manage' %}" + "../api/permissions/staff/" + staffId + "/permissions/";
        window.location.href = url;
    }

    function viewStaffHistory(staffId) {
        // 在当前页面查看员工历史页面
        const url = "{% url 'organizations:admin:permissions_manage' %}" + "../api/permissions/staff/" + staffId + "/history/";
        window.location.href = url;
    }

    // Utility functions
    // 获取CSRF Token（统一方法）
    function getCSRFToken() {
        // 优先从表单中获取token（最可靠）
        const formToken = document.querySelector('[name=csrfmiddlewaretoken]');
        if (formToken && formToken.value) {
            return formToken.value;
        }

        // 备用：从meta标签获取
        const metaToken = document.querySelector('meta[name=csrf-token]');
        if (metaToken && metaToken.content) {
            return metaToken.content;
        }

        // 最后：从cookie获取
        return getCookie('csrftoken');
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    // 通知函数
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.style.maxWidth = '400px';
        notification.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="flex-1">
                    <p class="text-sm font-medium">${message.replace(/\n/g, '<br>')}</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 自动消失
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
        
        // 初始化图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // 删除功能相关变量
    let currentDeleteStaffId = null;
    let selectedStaffIds = [];

    // 复选框状态管理
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.staff-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedCount();
    });

    // 监听单个复选框变化
    document.addEventListener('change', function(event) {
        if (event.target.classList.contains('staff-checkbox')) {
            updateSelectedCount();
        }
    });

    // 更新选中数量和批量删除按钮状态
    function updateSelectedCount() {
        const selectedCheckboxes = document.querySelectorAll('.staff-checkbox:checked');
        const count = selectedCheckboxes.length;
        
        // 更新批量操作工具栏中的计数
        document.getElementById('selectedCount').textContent = `已选择 ${count} 个员工`;
        
        // 启用/禁用批量删除按钮
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');
        if (count > 0) {
            batchDeleteBtn.disabled = false;
            batchDeleteBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        } else {
            batchDeleteBtn.disabled = true;
            batchDeleteBtn.classList.add('opacity-50', 'cursor-not-allowed');
        }
    }

    // 单个员工删除
    function deleteStaff(staffId, staffName) {
        currentDeleteStaffId = staffId;
        
        // 获取员工详细信息
        const row = document.querySelector(`tr[data-staff-id="${staffId}"]`);
        const staffNo = row.querySelector('td:nth-child(2) p:nth-child(2)').textContent;
        const department = row.querySelector('td:nth-child(3)').textContent;
        
        // 设置员工信息
        document.getElementById('deleteStaffInfo').innerHTML = `
            <strong>${staffName}</strong> (${staffNo})<br>
            部门：${department}
        `;
        
        // 清空之前的数据
        document.getElementById('deleteConflicts').classList.add('hidden');
        document.getElementById('deleteReason').value = '';
        document.getElementById('forceDelete').checked = false;
        
        // 显示删除确认模态框
        document.getElementById('staffDeleteModal').classList.remove('hidden');
    }

    // 关闭删除确认模态框
    function closeStaffDeleteModal() {
        document.getElementById('staffDeleteModal').classList.add('hidden');
        currentDeleteStaffId = null;
    }

    // 确认删除单个员工
    function confirmStaffDelete() {
        const reason = document.getElementById('deleteReason').value.trim();
        const forceDelete = document.getElementById('forceDelete').checked;
        
        if (!reason) {
            showNotification('请输入删除原因', 'error');
            return;
        }
        
        // 禁用确认按钮防止重复提交
        const confirmBtn = document.getElementById('confirmDeleteBtn');
        confirmBtn.disabled = true;
        confirmBtn.textContent = '删除中...';
        
        // 发送删除请求
        fetch(`{% url 'organizations:admin:staff_delete_permission' 0 %}`.replace('0', currentDeleteStaffId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                reason: reason,
                force_delete: forceDelete
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message || '删除成功', 'success');
                closeStaffDeleteModal();
                location.reload();
            } else if (data.has_conflicts) {
                // 显示数据冲突信息
                displayDeleteConflicts(data.conflicts);
            } else {
                showNotification(data.message || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('网络错误', 'error');
        })
        .finally(() => {
            confirmBtn.disabled = false;
            confirmBtn.textContent = '确认删除';
        });
    }

    // 显示删除冲突信息
    function displayDeleteConflicts(conflicts) {
        const conflictsDiv = document.getElementById('deleteConflicts');
        const conflictsList = document.getElementById('conflictsList');
        
        conflictsList.innerHTML = conflicts.map(conflict => `<div>• ${conflict}</div>`).join('');
        conflictsDiv.classList.remove('hidden');
    }

    // 批量删除确认
    function showBatchDeleteConfirm() {
        const selectedCheckboxes = document.querySelectorAll('.staff-checkbox:checked');
        
        if (selectedCheckboxes.length === 0) {
            showNotification('请先选择要删除的员工', 'error');
            return;
        }
        
        // 收集选中的员工信息
        const selectedStaff = [];
        selectedStaffIds = [];
        
        selectedCheckboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            const staffName = row.querySelector('td:nth-child(2) p').textContent;
            const staffNo = row.querySelector('td:nth-child(2) p:nth-child(2)').textContent;
            const department = row.querySelector('td:nth-child(3)').textContent;
            
            selectedStaff.push(`${staffName} (${staffNo}) - ${department}`);
            selectedStaffIds.push(checkbox.value);
        });
        
        // 设置批量删除信息
        document.getElementById('batchDeleteCount').textContent = selectedStaff.length;
        document.getElementById('batchDeleteList').innerHTML = selectedStaff.join('<br>');
        
        // 清空之前的数据
        document.getElementById('batchDeleteConflicts').classList.add('hidden');
        document.getElementById('batchDeleteReason').value = '';
        document.getElementById('batchForceDelete').checked = false;
        
        // 显示批量删除确认模态框
        document.getElementById('batchDeleteModal').classList.remove('hidden');
    }

    // 关闭批量删除模态框
    function closeBatchDeleteModal() {
        document.getElementById('batchDeleteModal').classList.add('hidden');
        selectedStaffIds = [];
    }

    // 确认批量删除
    function confirmBatchDelete() {
        const reason = document.getElementById('batchDeleteReason').value.trim();
        const forceDelete = document.getElementById('batchForceDelete').checked;
        
        if (!reason) {
            showNotification('请输入删除原因', 'error');
            return;
        }
        
        // 禁用确认按钮防止重复提交
        const confirmBtn = document.getElementById('confirmBatchDeleteBtn');
        confirmBtn.disabled = true;
        confirmBtn.textContent = '删除中...';
        
        // 发送批量删除请求
        fetch(`{% url 'organizations:admin:staff_batch_delete_permission' %}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({
                staff_ids: selectedStaffIds,
                reason: reason,
                force_delete: forceDelete
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`批量删除完成：成功 ${data.success_count} 个，失败 ${data.failed_count} 个`, 'success');
                closeBatchDeleteModal();
                location.reload();
            } else if (data.has_conflicts) {
                // 显示批量删除冲突信息
                displayBatchDeleteConflicts(data.conflict_items);
            } else {
                showNotification(data.message || '批量删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('网络错误', 'error');
        })
        .finally(() => {
            confirmBtn.disabled = false;
            confirmBtn.textContent = '确认批量删除';
        });
    }

    // 显示批量删除冲突信息
    function displayBatchDeleteConflicts(conflictItems) {
        const conflictsDiv = document.getElementById('batchDeleteConflicts');
        const conflictsList = document.getElementById('batchConflictsList');
        
        const conflictsHtml = conflictItems.map(item => `
            <div class="mb-2">
                <strong>${item.name} (${item.employee_no}) - ${item.department}</strong>
                <div class="ml-4 text-xs">
                    ${item.conflicts.map(conflict => `• ${conflict}`).join('<br>')}
                </div>
            </div>
        `).join('');
        
        conflictsList.innerHTML = conflictsHtml;
        conflictsDiv.classList.remove('hidden');
    }
</script>
{% endblock %}