{% extends "admin/base_admin.html" %}

{% block page_title %}角色权限详情{% endblock %}
{% block page_description %}{{ role_info.display_name }} ({{ role_info.role }}) 的权限详情{% endblock %}

{% block admin_content %}
{% if error %}
<div class="bg-red-50 border border-red-200 rounded-lg p-6">
    <div class="flex items-center">
        <div class="p-2 bg-red-100 rounded-lg">
            <i data-lucide="alert-triangle" class="w-5 h-5 text-red-600"></i>
        </div>
        <div class="ml-4">
            <h3 class="text-lg font-medium text-red-800">错误</h3>
            <p class="text-red-600">{{ error }}</p>
        </div>
    </div>
</div>
{% else %}
<!-- Role Overview -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="shield" class="w-5 h-5 mr-2 text-gray-500"></i>
                角色信息
            </h3>
            <a href="{% url 'organizations:admin:permissions_manage' %}" 
               class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700">
                <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>返回权限管理
            </a>
        </div>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-blue-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <i data-lucide="user-check" class="w-5 h-5 text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">角色名称</p>
                        <p class="text-lg font-bold text-gray-900">{{ role_info.display_name }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-green-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <i data-lucide="key" class="w-5 h-5 text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">权限数量</p>
                        <p class="text-lg font-bold text-gray-900">{{ role_info.permissions|length }} 个权限</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-yellow-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <i data-lucide="users" class="w-5 h-5 text-yellow-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">用户数量</p>
                        <p class="text-lg font-bold text-gray-900">{{ role_info.staff_count }} 个用户</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Permissions by Category -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="settings" class="w-5 h-5 mr-2 text-gray-500"></i>
            权限详情
        </h3>
    </div>
    <div class="p-6">
        {% for category, permissions in permission_groups.items %}
        {% if permissions %}
        <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3 flex items-center">
                <i data-lucide="folder" class="w-4 h-4 mr-2 text-gray-500"></i>
                {{ category }} ({{ permissions|length }} 个权限)
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {% for permission in permissions %}
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
                    <div class="flex items-center space-x-2">
                        <i data-lucide="check-circle" class="w-4 h-4 text-green-500"></i>
                        <span class="text-sm font-medium text-gray-700">{{ permission }}</span>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        {% endfor %}
    </div>
</div>

<!-- Staff with this Role -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="users" class="w-5 h-5 mr-2 text-gray-500"></i>
            拥有此角色的员工 ({{ role_info.staff_count }} 个)
        </h3>
    </div>
    <div class="p-6">
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left py-3 px-4 font-medium text-gray-700">员工</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">部门</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">最后登录</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for staff in staff_list %}
                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                        <td class="py-4 px-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium text-gray-700">{{ staff.name|first }}</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">{{ staff.name }}</p>
                                    <p class="text-sm text-gray-500">{{ staff.employee_no }}</p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4 px-4">
                            <span class="text-sm text-gray-900">{{ staff.department.name|default:"未分配" }}</span>
                        </td>
                        <td class="py-4 px-4">
                            <span class="text-sm text-gray-500">
                                {% if staff.last_login %}
                                    {{ staff.last_login|date:"Y-m-d H:i" }}
                                {% else %}
                                    从未登录
                                {% endif %}
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <div class="flex items-center space-x-2">
                                <a href="{% url 'organizations:admin:staff_detail' staff.id %}" 
                                   class="p-1 text-blue-600 hover:text-blue-800" title="查看详情">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                                <button onclick="editStaffRole({{ staff.id }}, '{{ staff.role }}')" 
                                    class="p-1 text-green-600 hover:text-green-800" title="修改角色">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" class="py-8 px-4 text-center text-gray-500">
                            <div class="flex flex-col items-center">
                                <i data-lucide="users" class="w-12 h-12 text-gray-300 mb-2"></i>
                                <p>暂无员工拥有此角色</p>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<script>
    function editStaffRole(staffId, currentRole) {
        if (confirm('确定要修改此员工的角色吗？')) {
            // 跳转到权限管理页面并定位到该员工
            window.location.href = '{% url "organizations:admin:permissions_manage" %}?highlight=' + staffId;
        }
    }
</script>
{% endblock %}