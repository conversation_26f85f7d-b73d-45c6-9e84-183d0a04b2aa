{% extends "admin/base_admin.html" %}

{% block page_title %}员工操作历史{% endblock %}
{% block page_description %}{{ staff.name }} ({{ staff.employee_no }}) 的操作历史记录{% endblock %}

{% block admin_content %}
<!-- Staff Overview -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="user" class="w-5 h-5 mr-2 text-gray-500"></i>
                员工信息
            </h3>
            <a href="{% url 'organizations:admin:permissions_manage' %}" 
               class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700">
                <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>返回权限管理
            </a>
        </div>
    </div>
    <div class="p-6">
        <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                <span class="text-xl font-medium text-gray-700">{{ staff.name|first }}</span>
            </div>
            <div>
                <h4 class="text-xl font-bold text-gray-900">{{ staff.name }}</h4>
                <p class="text-gray-600">{{ staff.employee_no }} | {{ staff.department.name|default:"未分配部门" }}</p>
                <p class="text-sm text-gray-500">{{ staff.get_role_display }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Operation History -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="history" class="w-5 h-5 mr-2 text-gray-500"></i>
            操作历史 (最近50条记录)
        </h3>
    </div>
    <div class="p-6">
        {% if logs %}
        <div class="space-y-4">
            {% for log in logs %}
            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            {% if log.action == 'create' %}
                            <i data-lucide="plus" class="w-4 h-4 text-blue-600"></i>
                            {% elif log.action == 'update' %}
                            <i data-lucide="edit" class="w-4 h-4 text-green-600"></i>
                            {% elif log.action == 'delete' %}
                            <i data-lucide="trash" class="w-4 h-4 text-red-600"></i>
                            {% elif log.action == 'login' %}
                            <i data-lucide="log-in" class="w-4 h-4 text-purple-600"></i>
                            {% elif log.action == 'logout' %}
                            <i data-lucide="log-out" class="w-4 h-4 text-orange-600"></i>
                            {% else %}
                            <i data-lucide="activity" class="w-4 h-4 text-gray-600"></i>
                            {% endif %}
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between">
                            <p class="text-sm font-medium text-gray-900">
                                {{ log.get_action_display }}
                            </p>
                            <p class="text-sm text-gray-500">
                                {{ log.created_at|date:"Y-m-d H:i:s" }}
                            </p>
                        </div>
                        {% if log.description %}
                        <p class="text-sm text-gray-600 mt-1">{{ log.description }}</p>
                        {% endif %}
                        {% if log.changes %}
                        <div class="mt-2">
                            <details class="text-sm">
                                <summary class="cursor-pointer text-blue-600 hover:text-blue-800">查看详细变更</summary>
                                <div class="mt-2 bg-gray-50 p-3 rounded border">
                                    <pre class="text-xs text-gray-700 whitespace-pre-wrap">{{ log.changes }}</pre>
                                </div>
                            </details>
                        </div>
                        {% endif %}
                        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            {% if log.user %}
                            <span class="flex items-center">
                                <i data-lucide="user" class="w-3 h-3 mr-1"></i>
                                操作人：{{ log.user.name }}
                            </span>
                            {% endif %}
                            {% if log.ip_address %}
                            <span class="flex items-center">
                                <i data-lucide="globe" class="w-3 h-3 mr-1"></i>
                                IP：{{ log.ip_address }}
                            </span>
                            {% endif %}
                            {% if log.user_agent %}
                            <span class="flex items-center">
                                <i data-lucide="monitor" class="w-3 h-3 mr-1"></i>
                                浏览器：{{ log.user_agent|truncatechars:50 }}
                            </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-12">
            <i data-lucide="clock" class="w-16 h-16 text-gray-300 mx-auto mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无操作记录</h3>
            <p class="text-gray-500">该员工暂时没有任何操作历史记录。</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}