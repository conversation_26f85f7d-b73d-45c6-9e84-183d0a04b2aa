{% extends "admin/base_admin.html" %}

{% block page_title %}员工权限详情{% endblock %}
{% block page_description %}{{ staff.name }} ({{ staff.employee_no }}) 的权限详情{% endblock %}

{% block admin_content %}
<!-- Staff Overview -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="user-check" class="w-5 h-5 mr-2 text-gray-500"></i>
                员工信息
            </h3>
            <a href="{% url 'organizations:admin:permissions_manage' %}" 
               class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700">
                <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>返回权限管理
            </a>
        </div>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-blue-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <i data-lucide="user" class="w-5 h-5 text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">员工姓名</p>
                        <p class="text-lg font-bold text-gray-900">{{ staff.name }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-green-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <i data-lucide="id-card" class="w-5 h-5 text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">员工编号</p>
                        <p class="text-lg font-bold text-gray-900">{{ staff.employee_no }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-yellow-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <i data-lucide="shield-check" class="w-5 h-5 text-yellow-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">当前角色</p>
                        <p class="text-lg font-bold text-gray-900">{{ role_name }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-purple-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <i data-lucide="key" class="w-5 h-5 text-purple-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">权限数量</p>
                        <p class="text-lg font-bold text-gray-900">{{ permission_count }} 个权限</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Permissions by Category -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="settings" class="w-5 h-5 mr-2 text-gray-500"></i>
            权限详情
        </h3>
    </div>
    <div class="p-6">
        {% for category, permissions in permission_groups.items %}
        {% if permissions %}
        <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-3 flex items-center">
                <i data-lucide="folder" class="w-4 h-4 mr-2 text-gray-500"></i>
                {{ category }} ({{ permissions|length }} 个权限)
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {% for permission in permissions %}
                <div class="{% if permission.has_permission %}bg-green-50 border-green-200{% else %}bg-gray-50 border-gray-200{% endif %} border rounded-lg p-3">
                    <div class="flex items-center space-x-2">
                        {% if permission.has_permission %}
                        <i data-lucide="check-circle" class="w-4 h-4 text-green-500"></i>
                        <span class="text-sm font-medium text-gray-700">{{ permission.description }}</span>
                        {% else %}
                        <i data-lucide="x-circle" class="w-4 h-4 text-gray-400"></i>
                        <span class="text-sm text-gray-500">{{ permission.description }}</span>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        {% endfor %}
    </div>
</div>

<!-- Accessible Departments -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="building" class="w-5 h-5 mr-2 text-gray-500"></i>
            可访问部门
        </h3>
    </div>
    <div class="p-6">
        {% if accessible_departments %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for dept in accessible_departments %}
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center space-x-3">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <i data-lucide="building" class="w-4 h-4 text-blue-600"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">{{ dept.name }}</p>
                        <p class="text-sm text-gray-500">{{ dept.dept_code }}</p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-8">
            <i data-lucide="building" class="w-12 h-12 text-gray-300 mx-auto mb-4"></i>
            <p class="text-gray-500">该员工可访问所有部门或无特定部门限制</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}