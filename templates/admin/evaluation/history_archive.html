{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}数据归档管理{% endblock %}
{% block page_description %}管理历史考评数据的归档、恢复和清理操作{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <!-- 返回历史列表 -->
    <a href="{% url 'evaluations:admin:history:list' %}" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
    
    <!-- 归档设置 -->
    <button onclick="openArchiveSettings()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="settings" class="w-4 h-4"></i>
        <span>归档设置</span>
    </button>
    
    <!-- 批量操作 -->
    <button onclick="batchArchiveSelected()" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center space-x-2">
        <i data-lucide="archive" class="w-4 h-4"></i>
        <span>批量归档</span>
    </button>
</div>
{% endblock %}

{% block admin_content %}
<!-- 归档统计概览 -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="archive" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">已归档批次</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.archived_batches|default:0 }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="hard-drive" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">节省空间</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.saved_space|default:"0 MB" }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="clock" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">待归档</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.pending_archive|default:0 }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-red-100 rounded-lg">
                <i data-lucide="trash-2" class="w-6 h-6 text-red-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">待清理</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.pending_cleanup|default:0 }}</p>
            </div>
        </div>
    </div>
</div>

<!-- 归档操作标签页 -->
<div class="bg-white shadow rounded-lg">
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8 px-6">
            <button onclick="switchTab('eligible')" id="eligibleTab" 
                    class="py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 whitespace-nowrap">
                待归档批次
            </button>
            <button onclick="switchTab('archived')" id="archivedTab" 
                    class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">
                已归档批次
            </button>
            <button onclick="switchTab('cleanup')" id="cleanupTab" 
                    class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">
                数据清理
            </button>
            <button onclick="switchTab('schedule')" id="scheduleTab" 
                    class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">
                定时任务
            </button>
        </nav>
    </div>
    
    <!-- 待归档批次 -->
    <div id="eligibleContent" class="p-6">
        <div class="mb-4 flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <label class="flex items-center">
                    <input type="checkbox" id="selectAllEligible" class="checkbox">
                    <span class="ml-2 text-sm text-gray-700">全选</span>
                </label>
                <span id="selectedEligibleCount" class="text-sm text-gray-500">已选择 0 项</span>
            </div>
            <div class="text-sm text-gray-500">
                符合归档条件：完成时间超过 {{ archive_settings.archive_after_days|default:90 }} 天
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">选择</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">批次信息</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">完成时间</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">数据量</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">估计节省</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for batch in eligible_batches %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-3 py-4">
                            <input type="checkbox" class="eligible-checkbox checkbox" value="{{ batch.id }}">
                        </td>
                        <td class="px-3 py-4">
                            <div class="flex items-center">
                                <div class="p-2 bg-blue-100 rounded-lg mr-3">
                                    <i data-lucide="calendar" class="w-4 h-4 text-blue-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ batch.name }}</div>
                                    <div class="text-sm text-gray-500">{{ batch.year }}年</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-3 py-4 text-sm text-gray-900">
                            {{ batch.end_date|date:"Y-m-d" }}
                            <div class="text-xs text-gray-500">{{ batch.days_since_completion }} 天前</div>
                        </td>
                        <td class="px-3 py-4 text-sm text-gray-900">
                            <div>记录: {{ batch.get_completed_count }}</div>
                            <div class="text-xs text-gray-500">关系: {{ batch.get_relations_count }}</div>
                        </td>
                        <td class="px-3 py-4 text-sm text-gray-900">{{ batch.estimated_space|default:"--" }}</td>
                        <td class="px-3 py-4 text-right text-sm font-medium">
                            <button onclick="archiveSingleBatch({{ batch.id }})" 
                                    class="text-purple-600 hover:text-purple-900 p-1 rounded hover:bg-purple-50" title="归档">
                                <i data-lucide="archive" class="w-4 h-4"></i>
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-3 py-8 text-center text-gray-500">暂无符合归档条件的批次</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 已归档批次 -->
    <div id="archivedContent" class="p-6 hidden">
        <div class="mb-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="selectAllArchived" class="checkbox">
                        <span class="ml-2 text-sm text-gray-700">全选</span>
                    </label>
                    <span id="selectedArchivedCount" class="text-sm text-gray-500">已选择 0 项</span>
                </div>
                <button onclick="batchRestoreSelected()" class="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700">
                    批量恢复
                </button>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">选择</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">批次信息</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">归档时间</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">存储位置</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">文件大小</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for archive in archived_batches %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-3 py-4">
                            <input type="checkbox" class="archived-checkbox checkbox" value="{{ archive.id }}">
                        </td>
                        <td class="px-3 py-4">
                            <div class="flex items-center">
                                <div class="p-2 bg-purple-100 rounded-lg mr-3">
                                    <i data-lucide="archive" class="w-4 h-4 text-purple-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ archive.batch_name }}</div>
                                    <div class="text-sm text-gray-500">{{ archive.batch_year }}年</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-3 py-4 text-sm text-gray-900">{{ archive.archived_at|date:"Y-m-d H:i" }}</td>
                        <td class="px-3 py-4 text-sm text-gray-900">{{ archive.storage_path|truncatechars:30 }}</td>
                        <td class="px-3 py-4 text-sm text-gray-900">{{ archive.file_size }}</td>
                        <td class="px-3 py-4 text-right text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                <button onclick="downloadArchive({{ archive.id }})" 
                                        class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="下载">
                                    <i data-lucide="download" class="w-4 h-4"></i>
                                </button>
                                <button onclick="restoreArchive({{ archive.id }})" 
                                        class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="恢复">
                                    <i data-lucide="rotate-ccw" class="w-4 h-4"></i>
                                </button>
                                <button onclick="deleteArchive({{ archive.id }})" 
                                        class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" title="删除">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-3 py-8 text-center text-gray-500">暂无已归档的批次</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 数据清理 -->
    <div id="cleanupContent" class="p-6 hidden">
        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
            <div class="flex">
                <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-400 mr-2"></i>
                <div>
                    <h3 class="text-sm font-medium text-yellow-800">注意</h3>
                    <p class="text-sm text-yellow-700 mt-1">数据清理操作不可逆，请确保已做好备份。建议只清理超过保留期限的数据。</p>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">临时数据清理</h3>
                <div class="space-y-3 mb-4">
                    <div class="flex justify-between">
                        <span class="text-gray-600">临时文件</span>
                        <span class="font-medium">{{ cleanup_stats.temp_files|default:0 }} 个</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">日志文件</span>
                        <span class="font-medium">{{ cleanup_stats.log_files|default:0 }} 个</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">预计释放空间</span>
                        <span class="font-medium">{{ cleanup_stats.temp_space|default:"0 MB" }}</span>
                    </div>
                </div>
                <button onclick="cleanupTempData()" class="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700">
                    清理临时数据
                </button>
            </div>
            
            <div class="border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">过期数据清理</h3>
                <div class="space-y-3 mb-4">
                    <div class="flex justify-between">
                        <span class="text-gray-600">过期批次</span>
                        <span class="font-medium">{{ cleanup_stats.expired_batches|default:0 }} 个</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">孤立记录</span>
                        <span class="font-medium">{{ cleanup_stats.orphan_records|default:0 }} 个</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">预计释放空间</span>
                        <span class="font-medium">{{ cleanup_stats.expired_space|default:"0 MB" }}</span>
                    </div>
                </div>
                <button onclick="cleanupExpiredData()" class="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                    清理过期数据
                </button>
            </div>
        </div>
    </div>
    
    <!-- 定时任务 -->
    <div id="scheduleContent" class="p-6 hidden">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">自动归档</h3>
                <form class="space-y-4">
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" class="checkbox" 
                                   {% if schedule_settings.auto_archive_enabled %}checked{% endif %}>
                            <span class="ml-2 text-sm text-gray-700">启用自动归档</span>
                        </label>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">归档条件</label>
                        <select class="w-full border border-gray-300 rounded-md py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
                            <option value="30">完成后30天</option>
                            <option value="60" {% if schedule_settings.archive_after_days == 60 %}selected{% endif %}>完成后60天</option>
                            <option value="90" {% if schedule_settings.archive_after_days == 90 %}selected{% endif %}>完成后90天</option>
                            <option value="180">完成后180天</option>
                            <option value="365">完成后1年</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">执行时间</label>
                        <input type="time" value="{{ schedule_settings.archive_time|default:'02:00' }}" 
                               class="w-full border border-gray-300 rounded-md py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <button type="submit" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        保存设置
                    </button>
                </form>
            </div>
            
            <div class="border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">自动清理</h3>
                <form class="space-y-4">
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" class="checkbox" 
                                   {% if schedule_settings.auto_cleanup_enabled %}checked{% endif %}>
                            <span class="ml-2 text-sm text-gray-700">启用自动清理</span>
                        </label>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">清理频率</label>
                        <select class="w-full border border-gray-300 rounded-md py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
                            <option value="daily">每日</option>
                            <option value="weekly" {% if schedule_settings.cleanup_frequency == 'weekly' %}selected{% endif %}>每周</option>
                            <option value="monthly">每月</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">保留时间</label>
                        <select class="w-full border border-gray-300 rounded-md py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
                            <option value="30">30天</option>
                            <option value="90">90天</option>
                            <option value="180" {% if schedule_settings.cleanup_retention_days == 180 %}selected{% endif %}>180天</option>
                            <option value="365">1年</option>
                        </select>
                    </div>
                    <button type="submit" class="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                        保存设置
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeArchiveFeatures();
    
    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

function switchTab(tabName) {
    // 隐藏所有内容
    document.querySelectorAll('[id$="Content"]').forEach(content => {
        content.classList.add('hidden');
    });
    
    // 重置所有标签样式
    document.querySelectorAll('[id$="Tab"]').forEach(tab => {
        tab.className = 'py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap';
    });
    
    // 显示选中的内容
    document.getElementById(tabName + 'Content').classList.remove('hidden');
    
    // 激活选中的标签
    document.getElementById(tabName + 'Tab').className = 'py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 whitespace-nowrap';
}

function initializeArchiveFeatures() {
    // 初始化全选功能
    const selectAllEligible = document.getElementById('selectAllEligible');
    const selectAllArchived = document.getElementById('selectAllArchived');
    
    if (selectAllEligible) {
        selectAllEligible.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.eligible-checkbox');
            checkboxes.forEach(cb => cb.checked = this.checked);
            updateSelectedCount('eligible');
        });
    }
    
    if (selectAllArchived) {
        selectAllArchived.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.archived-checkbox');
            checkboxes.forEach(cb => cb.checked = this.checked);
            updateSelectedCount('archived');
        });
    }
    
    // 绑定行选择事件
    document.querySelectorAll('.eligible-checkbox').forEach(cb => {
        cb.addEventListener('change', () => updateSelectedCount('eligible'));
    });
    
    document.querySelectorAll('.archived-checkbox').forEach(cb => {
        cb.addEventListener('change', () => updateSelectedCount('archived'));
    });
}

function updateSelectedCount(type) {
    const checkboxes = document.querySelectorAll(`.${type}-checkbox:checked`);
    const countElement = document.getElementById(`selected${type.charAt(0).toUpperCase() + type.slice(1)}Count`);
    if (countElement) {
        countElement.textContent = `已选择 ${checkboxes.length} 项`;
    }
}

function batchArchiveSelected() {
    const selected = document.querySelectorAll('.eligible-checkbox:checked');
    if (selected.length === 0) {
        showMessage('请选择要归档的批次', 'warning');
        return;
    }
    
    if (confirm(`确定要归档选中的 ${selected.length} 个批次吗？归档后数据将被压缩存储。`)) {
        // 实现批量归档逻辑
        showMessage('批量归档功能开发中', 'info');
    }
}

function batchRestoreSelected() {
    const selected = document.querySelectorAll('.archived-checkbox:checked');
    if (selected.length === 0) {
        showMessage('请选择要恢复的批次', 'warning');
        return;
    }
    
    if (confirm(`确定要恢复选中的 ${selected.length} 个批次吗？`)) {
        // 实现批量恢复逻辑
        showMessage('批量恢复功能开发中', 'info');
    }
}

function archiveSingleBatch(batchId) {
    if (confirm('确定要归档这个批次吗？归档后数据将被压缩存储。')) {
        // 实现单个归档逻辑
        showMessage('归档功能开发中', 'info');
    }
}

function downloadArchive(archiveId) {
    // 实现下载归档文件
    showMessage('下载功能开发中', 'info');
}

function restoreArchive(archiveId) {
    if (confirm('确定要恢复这个归档批次吗？')) {
        // 实现恢复归档
        showMessage('恢复功能开发中', 'info');
    }
}

function deleteArchive(archiveId) {
    if (confirm('确定要删除这个归档文件吗？此操作不可撤销！')) {
        // 实现删除归档
        showMessage('删除功能开发中', 'info');
    }
}

function cleanupTempData() {
    if (confirm('确定要清理临时数据吗？这将删除所有临时文件和日志。')) {
        // 实现临时数据清理
        showMessage('清理功能开发中', 'info');
    }
}

function cleanupExpiredData() {
    if (confirm('确定要清理过期数据吗？此操作不可撤销，请确保已做好备份！')) {
        // 实现过期数据清理
        showMessage('清理功能开发中', 'info');
    }
}

function openArchiveSettings() {
    // 打开归档设置模态框
    showMessage('设置功能开发中', 'info');
}

// 消息提示函数
function showMessage(message, type) {
    const alertClass = {
        'success': 'bg-green-100 text-green-800 border-green-200',
        'warning': 'bg-yellow-100 text-yellow-800 border-yellow-200',
        'error': 'bg-red-100 text-red-800 border-red-200',
        'info': 'bg-blue-100 text-blue-800 border-blue-200'
    }[type] || 'bg-gray-100 text-gray-800 border-gray-200';
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 px-4 py-2 rounded-md border ${alertClass} z-50`;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}
</script>
{% endblock %}