{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}考评批次详情 - {{ batch.name }}{% endblock %}
{% block page_description %}查看 {{ batch.name }} 的详细信息和评价数据{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <!-- 返回列表 -->
    <a href="{% url 'evaluations:admin:history:list' %}" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
    
    <!-- 导出详情 -->
    <button onclick="exportBatchDetail({{ batch.id }})" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>导出详情</span>
    </button>
    
    <!-- 添加到对比 -->
    <button onclick="addToComparison({{ batch.id }})" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="bar-chart-3" class="w-4 h-4"></i>
        <span>添加到对比</span>
    </button>
</div>
{% endblock %}

{% block admin_content %}
<!-- 批次基本信息 -->
<div class="bg-white shadow rounded-lg p-6 mb-6">
    <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-lg mr-4">
                <i data-lucide="calendar" class="w-8 h-8 text-blue-600"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900">{{ batch.name }}</h2>
                <p class="text-gray-600">{{ batch.description|default:"暂无描述" }}</p>
            </div>
        </div>
        <div class="text-right">
            {% if batch.status == 'completed' %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    <i data-lucide="check-circle" class="w-4 h-4 mr-1"></i>已完成
                </span>
            {% elif batch.status == 'active' %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    <i data-lucide="clock" class="w-4 h-4 mr-1"></i>进行中
                </span>
            {% elif batch.status == 'draft' %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                    <i data-lucide="file-text" class="w-4 h-4 mr-1"></i>草稿
                </span>
            {% else %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                    <i data-lucide="x-circle" class="w-4 h-4 mr-1"></i>已取消
                </span>
            {% endif %}
        </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
            <p class="text-gray-900">{{ batch.start_date|date:"Y-m-d H:i"|default:"未设置" }}</p>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
            <p class="text-gray-900">{{ batch.end_date|date:"Y-m-d H:i"|default:"未设置" }}</p>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">默认模板</label>
            <p class="text-gray-900">{{ batch.default_template.name }}</p>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">创建时间</label>
            <p class="text-gray-900">{{ batch.created_at|date:"Y-m-d H:i" }}</p>
        </div>
    </div>
</div>

<!-- 统计概览 -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">考评关系数</p>
                <p class="text-2xl font-bold text-gray-900">{{ batch.get_relations_count }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">已完成</p>
                <p class="text-2xl font-bold text-gray-900">{{ batch.get_completed_count }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="percent" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">完成率</p>
                <p class="text-2xl font-bold text-gray-900">{{ batch.get_completion_rate|floatformat:1 }}%</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-orange-100 rounded-lg">
                <i data-lucide="clock" class="w-6 h-6 text-orange-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">平均用时</p>
                <p class="text-2xl font-bold text-gray-900">{{ avg_time|default:"--" }}min</p>
            </div>
        </div>
    </div>
</div>

<!-- 详细数据标签页 -->
<div class="bg-white shadow rounded-lg">
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8 px-6">
            <button onclick="switchTab('participants')" id="participantsTab" 
                    class="py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 whitespace-nowrap">
                参与统计
            </button>
            <button onclick="switchTab('evaluations')" id="evaluationsTab" 
                    class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">
                评价记录
            </button>
            <button onclick="switchTab('scores')" id="scoresTab" 
                    class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">
                分数分析
            </button>
            <button onclick="switchTab('templates')" id="templatesTab" 
                    class="py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">
                模板使用
            </button>
        </nav>
    </div>
    
    <!-- 参与统计标签页 -->
    <div id="participantsContent" class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 参与人员统计 -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">参与人员统计</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">评价者人数</span>
                        <span class="font-medium">{{ participant_stats.evaluators_count|default:0 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">被评价者人数</span>
                        <span class="font-medium">{{ participant_stats.evaluatees_count|default:0 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">总参与人数</span>
                        <span class="font-medium">{{ participant_stats.total_participants|default:0 }}</span>
                    </div>
                </div>
            </div>
            
            <!-- 部门参与情况 -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">部门参与情况</h3>
                <div class="space-y-2">
                    {% for dept_stat in department_stats %}
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">{{ dept_stat.name }}</span>
                        <div class="flex items-center">
                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: {{ dept_stat.completion_rate }}%"></div>
                            </div>
                            <span class="text-sm font-medium">{{ dept_stat.completion_rate|floatformat:0 }}%</span>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-gray-500 text-center py-4">暂无部门统计数据</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 评价记录标签页 -->
    <div id="evaluationsContent" class="p-6 hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">评价者</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">被评价者</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">模板</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">得分</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">权重</th>
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">完成时间</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for relation in evaluation_records %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-3 py-4 text-sm text-gray-900">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-xs font-medium">{{ relation.evaluator.name|first }}</span>
                                </div>
                                <div>
                                    <div class="font-medium">{{ relation.evaluator.name }}</div>
                                    <div class="text-gray-500">{{ relation.evaluator.department.name }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-3 py-4 text-sm text-gray-900">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-xs font-medium">{{ relation.evaluatee.name|first }}</span>
                                </div>
                                <div>
                                    <div class="font-medium">{{ relation.evaluatee.name }}</div>
                                    <div class="text-gray-500">{{ relation.evaluatee.department.name }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-3 py-4 text-sm text-gray-900">{{ relation.template.name }}</td>
                        <td class="px-3 py-4 text-sm text-gray-900">
                            {% if relation.evaluationrecord %}
                                <div class="font-medium">{{ relation.evaluationrecord.total_score|floatformat:1 }}</div>
                                <div class="text-gray-500 text-xs">原始: {{ relation.evaluationrecord.raw_score|floatformat:1 }}</div>
                            {% else %}
                                <span class="text-gray-400">未完成</span>
                            {% endif %}
                        </td>
                        <td class="px-3 py-4 text-sm text-gray-900">{{ relation.weight_factor }}</td>
                        <td class="px-3 py-4 text-sm text-gray-900">
                            {% if relation.evaluationrecord %}
                                {{ relation.evaluationrecord.completion_time|date:"m-d H:i" }}
                            {% else %}
                                <span class="text-gray-400">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-3 py-8 text-center text-gray-500">暂无评价记录</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 分数分析标签页 -->
    <div id="scoresContent" class="p-6 hidden">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">分数分布</h3>
                <div class="space-y-3">
                    {% for score_range in score_distribution %}
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">{{ score_range.range }}</span>
                        <div class="flex items-center">
                            <div class="w-24 bg-gray-200 rounded-full h-2 mr-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: {{ score_range.percentage }}%"></div>
                            </div>
                            <span class="text-sm font-medium">{{ score_range.count }}</span>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-gray-500 text-center py-4">暂无分数分布数据</p>
                    {% endfor %}
                </div>
            </div>
            
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">统计信息</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">平均分</span>
                        <span class="font-medium">{{ score_stats.avg_score|floatformat:2|default:"--" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">最高分</span>
                        <span class="font-medium">{{ score_stats.max_score|floatformat:1|default:"--" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">最低分</span>
                        <span class="font-medium">{{ score_stats.min_score|floatformat:1|default:"--" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">标准差</span>
                        <span class="font-medium">{{ score_stats.std_dev|floatformat:2|default:"--" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模板使用标签页 -->
    <div id="templatesContent" class="p-6 hidden">
        <div class="space-y-4">
            {% for template_stat in template_usage %}
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-medium text-gray-900">{{ template_stat.template_name }}</h4>
                    <span class="text-sm text-gray-500">使用 {{ template_stat.usage_count }} 次</span>
                </div>
                <div class="flex items-center">
                    <div class="flex-1 bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-500 h-2 rounded-full" style="width: {{ template_stat.usage_percentage }}%"></div>
                    </div>
                    <span class="ml-2 text-sm font-medium">{{ template_stat.usage_percentage|floatformat:1 }}%</span>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">暂无模板使用统计</p>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

function switchTab(tabName) {
    // 隐藏所有内容
    document.querySelectorAll('[id$="Content"]').forEach(content => {
        content.classList.add('hidden');
    });
    
    // 重置所有标签样式
    document.querySelectorAll('[id$="Tab"]').forEach(tab => {
        tab.className = 'py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap';
    });
    
    // 显示选中的内容
    document.getElementById(tabName + 'Content').classList.remove('hidden');
    
    // 激活选中的标签
    document.getElementById(tabName + 'Tab').className = 'py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 whitespace-nowrap';
}

function addToComparison(batchId) {
    let selectedBatches = JSON.parse(localStorage.getItem('comparisonBatches') || '[]');
    
    if (!selectedBatches.includes(batchId)) {
        selectedBatches.push(batchId);
        localStorage.setItem('comparisonBatches', JSON.stringify(selectedBatches));
        showMessage('已添加到对比列表', 'success');
    } else {
        showMessage('该批次已在对比列表中', 'warning');
    }
}

function exportBatchDetail(batchId) {
    // 实现批次详情导出
    showMessage('导出功能开发中', 'info');
}

// 消息提示函数
function showMessage(message, type) {
    const alertClass = {
        'success': 'bg-green-100 text-green-800 border-green-200',
        'warning': 'bg-yellow-100 text-yellow-800 border-yellow-200',
        'error': 'bg-red-100 text-red-800 border-red-200',
        'info': 'bg-blue-100 text-blue-800 border-blue-200'
    }[type] || 'bg-gray-100 text-gray-800 border-gray-200';
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 px-4 py-2 rounded-md border ${alertClass} z-50`;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}
</script>
{% endblock %}