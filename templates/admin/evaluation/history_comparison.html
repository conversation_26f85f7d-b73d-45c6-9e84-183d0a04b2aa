{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}批次对比分析{% endblock %}
{% block page_description %}对比分析不同批次的考评数据和统计结果{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <!-- 返回历史列表 -->
    <a href="{% url 'evaluations:admin:history:list' %}" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
    
    <!-- 导出对比报告 -->
    <button onclick="exportComparisonReport()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>导出报告</span>
    </button>
</div>
{% endblock %}

{% block admin_content %}
<!-- 批次选择器 -->
<div class="bg-white shadow rounded-lg p-6 mb-6">
    <h2 class="text-lg font-medium text-gray-900 mb-4">选择对比批次</h2>
    <form method="get" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">批次1</label>
                <select name="batch1" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">请选择批次</option>
                    {% for batch in available_batches %}
                        <option value="{{ batch.id }}" {% if batch1 and batch.id == batch1.id %}selected{% endif %}>
                            {{ batch.name }} ({{ batch.year }})
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">批次2</label>
                <select name="batch2" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">请选择批次</option>
                    {% for batch in available_batches %}
                        <option value="{{ batch.id }}" {% if batch2 and batch.id == batch2.id %}selected{% endif %}>
                            {{ batch.name }} ({{ batch.year }})
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="flex items-end">
                <button type="submit" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center space-x-2">
                    <i data-lucide="bar-chart-3" class="w-4 h-4"></i>
                    <span>开始对比</span>
                </button>
            </div>
        </div>
    </form>
</div>

{% if batch1 and batch2 %}
<!-- 对比结果 -->
<div class="space-y-6">
    <!-- 基本信息对比 -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">基本信息对比</h3>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr class="border-b border-gray-200">
                            <th class="text-left py-3 px-3 text-sm font-medium text-gray-600">对比项</th>
                            <th class="text-left py-3 px-3 text-sm font-medium text-blue-600">{{ batch1.name }}</th>
                            <th class="text-left py-3 px-3 text-sm font-medium text-green-600">{{ batch2.name }}</th>
                            <th class="text-left py-3 px-3 text-sm font-medium text-gray-600">差异</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <tr>
                            <td class="py-3 px-3 text-sm text-gray-900">时间范围</td>
                            <td class="py-3 px-3 text-sm text-gray-900">
                                {{ batch1.start_date|date:"Y-m-d" }} 至 {{ batch1.end_date|date:"Y-m-d" }}
                            </td>
                            <td class="py-3 px-3 text-sm text-gray-900">
                                {{ batch2.start_date|date:"Y-m-d" }} 至 {{ batch2.end_date|date:"Y-m-d" }}
                            </td>
                            <td class="py-3 px-3 text-sm text-gray-500">-</td>
                        </tr>
                        <tr>
                            <td class="py-3 px-3 text-sm text-gray-900">考评关系数</td>
                            <td class="py-3 px-3 text-sm text-gray-900">{{ comparison_data.batch1_relations }}</td>
                            <td class="py-3 px-3 text-sm text-gray-900">{{ comparison_data.batch2_relations }}</td>
                            <td class="py-3 px-3 text-sm {% if comparison_data.relations_diff > 0 %}text-green-600{% elif comparison_data.relations_diff < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                                {% if comparison_data.relations_diff > 0 %}+{% endif %}{{ comparison_data.relations_diff }}
                            </td>
                        </tr>
                        <tr>
                            <td class="py-3 px-3 text-sm text-gray-900">完成数量</td>
                            <td class="py-3 px-3 text-sm text-gray-900">{{ comparison_data.batch1_completed }}</td>
                            <td class="py-3 px-3 text-sm text-gray-900">{{ comparison_data.batch2_completed }}</td>
                            <td class="py-3 px-3 text-sm {% if comparison_data.completed_diff > 0 %}text-green-600{% elif comparison_data.completed_diff < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                                {% if comparison_data.completed_diff > 0 %}+{% endif %}{{ comparison_data.completed_diff }}
                            </td>
                        </tr>
                        <tr>
                            <td class="py-3 px-3 text-sm text-gray-900">完成率</td>
                            <td class="py-3 px-3 text-sm text-gray-900">{{ comparison_data.batch1_rate|floatformat:1 }}%</td>
                            <td class="py-3 px-3 text-sm text-gray-900">{{ comparison_data.batch2_rate|floatformat:1 }}%</td>
                            <td class="py-3 px-3 text-sm {% if comparison_data.rate_diff > 0 %}text-green-600{% elif comparison_data.rate_diff < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                                {% if comparison_data.rate_diff > 0 %}+{% endif %}{{ comparison_data.rate_diff|floatformat:1 }}%
                            </td>
                        </tr>
                        <tr>
                            <td class="py-3 px-3 text-sm text-gray-900">平均分</td>
                            <td class="py-3 px-3 text-sm text-gray-900">{{ comparison_data.batch1_avg_score|floatformat:2|default:"--" }}</td>
                            <td class="py-3 px-3 text-sm text-gray-900">{{ comparison_data.batch2_avg_score|floatformat:2|default:"--" }}</td>
                            <td class="py-3 px-3 text-sm {% if comparison_data.score_diff > 0 %}text-green-600{% elif comparison_data.score_diff < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                                {% if comparison_data.score_diff %}
                                    {% if comparison_data.score_diff > 0 %}+{% endif %}{{ comparison_data.score_diff|floatformat:2 }}
                                {% else %}--{% endif %}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 参与度对比 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ batch1.name }} - 参与统计</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">评价者人数</span>
                    <span class="font-medium">{{ comparison_data.batch1_evaluators }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">被评价者人数</span>
                    <span class="font-medium">{{ comparison_data.batch1_evaluatees }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">总参与人数</span>
                    <span class="font-medium">{{ comparison_data.batch1_total_participants }}</span>
                </div>
            </div>
        </div>
        
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ batch2.name }} - 参与统计</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">评价者人数</span>
                    <span class="font-medium">{{ comparison_data.batch2_evaluators }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">被评价者人数</span>
                    <span class="font-medium">{{ comparison_data.batch2_evaluatees }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">总参与人数</span>
                    <span class="font-medium">{{ comparison_data.batch2_total_participants }}</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 分数分布对比 -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">分数分布对比</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
                <h4 class="font-medium text-blue-600 mb-3">{{ batch1.name }}</h4>
                <div class="space-y-2">
                    {% for range in comparison_data.batch1_score_distribution %}
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">{{ range.label }}</span>
                        <div class="flex items-center">
                            <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: {{ range.percentage }}%"></div>
                            </div>
                            <span class="text-sm font-medium w-8">{{ range.count }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <div>
                <h4 class="font-medium text-green-600 mb-3">{{ batch2.name }}</h4>
                <div class="space-y-2">
                    {% for range in comparison_data.batch2_score_distribution %}
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">{{ range.label }}</span>
                        <div class="flex items-center">
                            <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: {{ range.percentage }}%"></div>
                            </div>
                            <span class="text-sm font-medium w-8">{{ range.count }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 部门对比 -->
    {% if comparison_data.department_comparison %}
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">部门完成率对比</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left py-3 px-3 text-sm font-medium text-gray-600">部门</th>
                        <th class="text-left py-3 px-3 text-sm font-medium text-blue-600">{{ batch1.name }}</th>
                        <th class="text-left py-3 px-3 text-sm font-medium text-green-600">{{ batch2.name }}</th>
                        <th class="text-left py-3 px-3 text-sm font-medium text-gray-600">变化</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    {% for dept in comparison_data.department_comparison %}
                    <tr>
                        <td class="py-3 px-3 text-sm text-gray-900">{{ dept.name }}</td>
                        <td class="py-3 px-3 text-sm text-gray-900">
                            <div class="flex items-center">
                                <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: {{ dept.batch1_rate }}%"></div>
                                </div>
                                <span>{{ dept.batch1_rate|floatformat:1 }}%</span>
                            </div>
                        </td>
                        <td class="py-3 px-3 text-sm text-gray-900">
                            <div class="flex items-center">
                                <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: {{ dept.batch2_rate }}%"></div>
                                </div>
                                <span>{{ dept.batch2_rate|floatformat:1 }}%</span>
                            </div>
                        </td>
                        <td class="py-3 px-3 text-sm {% if dept.rate_change > 0 %}text-green-600{% elif dept.rate_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                            {% if dept.rate_change > 0 %}↗{% elif dept.rate_change < 0 %}↘{% else %}→{% endif %}
                            {% if dept.rate_change != 0 %}{{ dept.rate_change|floatformat:1 }}%{% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}
    
    <!-- 趋势分析 -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">趋势分析</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600 mb-1">
                    {% if comparison_data.participation_trend > 0 %}↗{% elif comparison_data.participation_trend < 0 %}↘{% else %}→{% endif %}
                </div>
                <div class="text-sm text-gray-600">参与度趋势</div>
                <div class="text-xs text-gray-500 mt-1">
                    {% if comparison_data.participation_trend > 0 %}
                        参与度提升 {{ comparison_data.participation_trend|floatformat:1 }}%
                    {% elif comparison_data.participation_trend < 0 %}
                        参与度下降 {{ comparison_data.participation_trend|abs|floatformat:1 }}%
                    {% else %}
                        参与度持平
                    {% endif %}
                </div>
            </div>
            
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600 mb-1">
                    {% if comparison_data.completion_trend > 0 %}↗{% elif comparison_data.completion_trend < 0 %}↘{% else %}→{% endif %}
                </div>
                <div class="text-sm text-gray-600">完成率趋势</div>
                <div class="text-xs text-gray-500 mt-1">
                    {% if comparison_data.completion_trend > 0 %}
                        完成率提升 {{ comparison_data.completion_trend|floatformat:1 }}%
                    {% elif comparison_data.completion_trend < 0 %}
                        完成率下降 {{ comparison_data.completion_trend|abs|floatformat:1 }}%
                    {% else %}
                        完成率持平
                    {% endif %}
                </div>
            </div>
            
            <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600 mb-1">
                    {% if comparison_data.score_trend > 0 %}↗{% elif comparison_data.score_trend < 0 %}↘{% else %}→{% endif %}
                </div>
                <div class="text-sm text-gray-600">评分趋势</div>
                <div class="text-xs text-gray-500 mt-1">
                    {% if comparison_data.score_trend > 0 %}
                        平均分提升 {{ comparison_data.score_trend|floatformat:2 }}分
                    {% elif comparison_data.score_trend < 0 %}
                        平均分下降 {{ comparison_data.score_trend|abs|floatformat:2 }}分
                    {% else %}
                        平均分持平
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<!-- 无对比数据提示 -->
<div class="bg-white shadow rounded-lg p-12 text-center">
    <i data-lucide="bar-chart-3" class="w-16 h-16 text-gray-400 mx-auto mb-4"></i>
    <h3 class="text-lg font-medium text-gray-900 mb-2">请选择要对比的批次</h3>
    <p class="text-gray-500">选择两个不同的考评批次来查看详细的对比分析</p>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

function exportComparisonReport() {
    // 实现对比报告导出
    showMessage('导出功能开发中', 'info');
}

// 消息提示函数
function showMessage(message, type) {
    const alertClass = {
        'success': 'bg-green-100 text-green-800 border-green-200',
        'warning': 'bg-yellow-100 text-yellow-800 border-yellow-200',
        'error': 'bg-red-100 text-red-800 border-red-200',
        'info': 'bg-blue-100 text-blue-800 border-blue-200'
    }[type] || 'bg-gray-100 text-gray-800 border-gray-200';
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 px-4 py-2 rounded-md border ${alertClass} z-50`;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}
</script>
{% endblock %}