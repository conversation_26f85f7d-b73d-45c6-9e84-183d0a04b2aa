{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}考评历史管理{% endblock %}
{% block page_description %}查看和管理历史考评数据，支持数据对比和归档操作{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <!-- 导出历史数据 -->
    <button onclick="exportHistoryData()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>导出数据</span>
    </button>
    
    <!-- 对比分析 -->
    <button onclick="openComparisonModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="bar-chart-3" class="w-4 h-4"></i>
        <span>对比分析</span>
    </button>
    
    <!-- 归档管理 -->
    <a href="{% url 'evaluations:admin:history:archive' %}" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center space-x-2">
        <i data-lucide="archive" class="w-4 h-4"></i>
        <span>归档管理</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<!-- 统计概览 -->
<div class="grid grid-cols-1 md:grid-cols-6 gap-4 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="layers" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总批次数</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_batches }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">已完成</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.completed_batches }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="clock" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">进行中</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.active_batches }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-gray-100 rounded-lg">
                <i data-lucide="file-text" class="w-6 h-6 text-gray-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">草稿</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.draft_batches }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总评价数</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_evaluations }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-orange-100 rounded-lg">
                <i data-lucide="calendar" class="w-6 h-6 text-orange-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">本年批次</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.this_year_batches }}</p>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="bg-white rounded-lg shadow mb-6 p-6">
    <form method="get" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
            <!-- 搜索框 -->
            <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-1">搜索批次</label>
                <div class="relative">
                    <input type="text" name="search" value="{{ search }}" 
                           placeholder="搜索批次名称、描述或年份..." 
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                </div>
            </div>
            
            <!-- 状态筛选 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                <select name="status" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">全部状态</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if current_status == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- 年份筛选 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">年份</label>
                <select name="year" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">全部年份</option>
                    {% for year in years %}
                        <option value="{{ year }}" {% if current_year == year|stringformat:"s" %}selected{% endif %}>{{ year }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- 部门筛选 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">部门</label>
                <select name="department" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">全部部门</option>
                    {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if current_department == dept.id|stringformat:"s" %}selected{% endif %}>{{ dept.name }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
        
        <!-- 日期范围 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                <input type="date" name="date_from" value="{{ date_from }}" 
                       class="w-full border border-gray-300 rounded-md py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                <input type="date" name="date_to" value="{{ date_to }}" 
                       class="w-full border border-gray-300 rounded-md py-2 px-3 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div class="flex items-end space-x-2">
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
                    <i data-lucide="filter" class="w-4 h-4"></i>
                    <span>筛选</span>
                </button>
                <a href="{% url 'evaluations:admin:history_list' %}" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                    清除
                </a>
            </div>
        </div>
    </form>
</div>

<!-- 批次列表 -->
<div class="bg-white shadow rounded-lg overflow-hidden">
    <!-- 批量操作工具栏 -->
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <label class="flex items-center">
                    <input type="checkbox" id="selectAll" class="checkbox">
                    <span class="ml-2 text-sm text-gray-700">全选</span>
                </label>
                <span id="selectedCount" class="text-sm text-gray-500">已选择 0 项</span>
            </div>
            
            <!-- 批量操作 -->
            <div id="batchActions" class="hidden flex items-center space-x-2">
                <button onclick="batchCompare()" class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700">
                    批量对比
                </button>
                <button onclick="batchExport()" class="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700">
                    批量导出
                </button>
                <button onclick="batchArchive()" class="px-3 py-1 text-sm bg-purple-600 text-white rounded hover:bg-purple-700">
                    批量归档
                </button>
            </div>
        </div>
    </div>
    
    <!-- 表格内容 -->
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">选择</th>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">批次信息</th>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">时间范围</th>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">参与统计</th>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">完成情况</th>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">状态</th>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for batch in batches %}
                <tr class="hover:bg-gray-50 batch-row" data-batch-id="{{ batch.id }}">
                    <td class="px-3 py-4">
                        <input type="checkbox" class="row-checkbox checkbox" value="{{ batch.id }}">
                    </td>
                    <td class="px-3 py-4">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-lg mr-3">
                                <i data-lucide="calendar" class="w-4 h-4 text-blue-600"></i>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">{{ batch.name }}</div>
                                <div class="text-sm text-gray-500">{{ batch.year }}年 · {{ batch.description|truncatechars:30 }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-3 py-4 text-sm">
                        <div class="space-y-2">
                            <div class="time-list-item">
                                <i data-lucide="play-circle" class="time-icon text-green-500"></i>
                                <div class="time-badge success">
                                    <i data-lucide="calendar-plus" class="time-icon"></i>
                                    <div class="flex flex-col">
                                        <span class="font-medium">{{ batch.start_date|date:"Y年m月d日"|default:"未设置" }}</span>
                                        <span class="text-xs opacity-75">{{ batch.start_date|date:"H:i"|default:"" }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="time-list-item">
                                <i data-lucide="stop-circle" class="time-icon text-red-500"></i>
                                <div class="time-badge danger">
                                    <i data-lucide="calendar-x" class="time-icon"></i>
                                    <div class="flex flex-col">
                                        <span class="font-medium">{{ batch.end_date|date:"Y年m月d日"|default:"未设置" }}</span>
                                        <span class="text-xs opacity-75">{{ batch.end_date|date:"H:i"|default:"" }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-3 py-4 text-sm text-gray-900">
                        <div>关系数：{{ batch.get_relations_count|default:0 }}</div>
                        <div>参与人：{{ batch.get_participants_count|default:0 }}</div>
                    </td>
                    <td class="px-3 py-4">
                        {% with completion_rate=batch.get_completion_rate %}
                        <div class="flex items-center">
                            <div class="flex-1">
                                <div class="flex justify-between text-sm mb-1">
                                    <span>{{ completion_rate|floatformat:1 }}%</span>
                                    <span>{{ batch.get_completed_count }}/{{ batch.get_relations_count }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ completion_rate }}%"></div>
                                </div>
                            </div>
                        </div>
                        {% endwith %}
                    </td>
                    <td class="px-3 py-4">
                        {% if batch.status == 'completed' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>已完成
                            </span>
                        {% elif batch.status == 'active' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i data-lucide="clock" class="w-3 h-3 mr-1"></i>进行中
                            </span>
                        {% elif batch.status == 'draft' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <i data-lucide="file-text" class="w-3 h-3 mr-1"></i>草稿
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>已取消
                            </span>
                        {% endif %}
                    </td>
                    <td class="px-3 py-4 text-right text-sm font-medium">
                        <div class="flex items-center space-x-2">
                            <a href="{% url 'evaluations:admin:history:detail' batch.pk %}" 
                               class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="查看详情">
                                <i data-lucide="eye" class="w-4 h-4"></i>
                            </a>
                            <button onclick="addToComparison({{ batch.id }})" 
                                    class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="添加到对比">
                                <i data-lucide="bar-chart" class="w-4 h-4"></i>
                            </button>
                            {% if batch.status == 'completed' %}
                            <button onclick="archiveBatch({{ batch.id }})" 
                                    class="text-purple-600 hover:text-purple-900 p-1 rounded hover:bg-purple-50" title="归档">
                                <i data-lucide="archive" class="w-4 h-4"></i>
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="px-3 py-12 text-center">
                        <div class="flex flex-col items-center">
                            <i data-lucide="calendar" class="w-12 h-12 text-gray-400 mb-4"></i>
                            <h3 class="text-sm font-medium text-gray-900 mb-2">暂无历史数据</h3>
                            <p class="text-sm text-gray-500">暂时没有找到符合条件的考评批次</p>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- 分页 -->
    {% if is_paginated %}
    <div class="bg-white px-6 py-3 flex items-center justify-between border-t border-gray-200">
        <div class="flex-1 flex justify-between sm:hidden">
            {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    上一页
                </a>
            {% endif %}
            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    下一页
                </a>
            {% endif %}
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    显示第 <span class="font-medium">{{ page_obj.start_index }}</span> 到 <span class="font-medium">{{ page_obj.end_index }}</span> 条，
                    共 <span class="font-medium">{{ paginator.count }}</span> 条记录
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i data-lucide="chevron-left" class="w-4 h-4"></i>
                        </a>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                {{ num }}
                            </span>
                        {% else %}
                            <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i data-lucide="chevron-right" class="w-4 h-4"></i>
                        </a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 对比分析模态框 -->
<div id="comparisonModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">批次对比分析</h3>
                    <button onclick="closeComparisonModal()" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div id="comparisonContent">
                    <!-- 对比内容将通过JavaScript加载 -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeTableFeatures();
    initializeComparison();
    
    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

// 全选和批量操作
function initializeTableFeatures() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');

    // 全选功能
    selectAllCheckbox?.addEventListener('change', function() {
        rowCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedCount();
        toggleBatchActions();
    });

    // 行选择功能
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();
            toggleBatchActions();

            // 更新全选状态
            const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === rowCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
        });
    });
}

function updateSelectedCount() {
    const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
    const selectedCountElement = document.getElementById('selectedCount');
    if (selectedCountElement) {
        selectedCountElement.textContent = `已选择 ${checkedCount} 项`;
    }
}

function toggleBatchActions() {
    const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
    const batchActions = document.getElementById('batchActions');

    if (batchActions) {
        if (checkedCount > 0) {
            batchActions.classList.remove('hidden');
        } else {
            batchActions.classList.add('hidden');
        }
    }
}

// 对比功能
let selectedBatches = [];

function initializeComparison() {
    selectedBatches = JSON.parse(localStorage.getItem('comparisonBatches') || '[]');
    updateComparisonUI();
}

function addToComparison(batchId) {
    if (!selectedBatches.includes(batchId)) {
        selectedBatches.push(batchId);
        localStorage.setItem('comparisonBatches', JSON.stringify(selectedBatches));
        updateComparisonUI();
        showMessage('已添加到对比列表', 'success');
    } else {
        showMessage('该批次已在对比列表中', 'warning');
    }
}

function updateComparisonUI() {
    // 更新对比按钮状态
    const comparisonButton = document.querySelector('[onclick="openComparisonModal()"]');
    if (comparisonButton && selectedBatches.length > 0) {
        comparisonButton.innerHTML = `
            <i data-lucide="bar-chart-3" class="w-4 h-4"></i>
            <span>对比分析 (${selectedBatches.length})</span>
        `;
    }
}

function openComparisonModal() {
    if (selectedBatches.length < 2) {
        showMessage('请至少选择2个批次进行对比', 'warning');
        return;
    }
    
    document.getElementById('comparisonModal').classList.remove('hidden');
    loadComparisonData();
}

function closeComparisonModal() {
    document.getElementById('comparisonModal').classList.add('hidden');
}

function loadComparisonData() {
    const params = selectedBatches.map(id => `batches=${id}`).join('&');
    fetch(`{% url 'evaluations:admin:history:comparison' %}?${params}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('comparisonContent').innerHTML = html;
        })
        .catch(error => {
            console.error('加载对比数据失败:', error);
            showMessage('加载对比数据失败', 'error');
        });
}

function batchCompare() {
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
    const batchIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (batchIds.length < 2) {
        showMessage('请至少选择2个批次进行对比', 'warning');
        return;
    }
    
    selectedBatches = batchIds;
    localStorage.setItem('comparisonBatches', JSON.stringify(selectedBatches));
    openComparisonModal();
}

function batchExport() {
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
    const batchIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (batchIds.length === 0) {
        showMessage('请选择要导出的批次', 'warning');
        return;
    }
    
    // 创建表单并提交
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{% url "evaluations:admin:history:archive" %}';
    
    // 添加CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (csrfToken) {
        form.appendChild(csrfToken.cloneNode());
    }
    
    // 添加action
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'export';
    form.appendChild(actionInput);
    
    // 添加batch IDs
    batchIds.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'batch_ids';
        input.value = id;
        form.appendChild(input);
    });
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

function batchArchive() {
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
    const batchIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (batchIds.length === 0) {
        showMessage('请选择要归档的批次', 'warning');
        return;
    }
    
    if (confirm(`确定要归档选中的 ${batchIds.length} 个批次吗？归档后将无法在常规列表中查看。`)) {
        // 实现批量归档逻辑
        showMessage('归档功能开发中', 'info');
    }
}

function archiveBatch(batchId) {
    if (confirm('确定要归档这个批次吗？归档后将无法在常规列表中查看。')) {
        // 实现单个归档逻辑
        showMessage('归档功能开发中', 'info');
    }
}

function exportHistoryData() {
    // 导出历史数据
    showMessage('导出功能开发中', 'info');
}

// 消息提示函数
function showMessage(message, type) {
    // 简单的消息提示实现
    const alertClass = {
        'success': 'bg-green-100 text-green-800 border-green-200',
        'warning': 'bg-yellow-100 text-yellow-800 border-yellow-200',
        'error': 'bg-red-100 text-red-800 border-red-200',
        'info': 'bg-blue-100 text-blue-800 border-blue-200'
    }[type] || 'bg-gray-100 text-gray-800 border-gray-200';
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 px-4 py-2 rounded-md border ${alertClass} z-50`;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}
</script>
{% endblock %}