{% extends "admin/base_admin.html" %}

{% block page_title %}权重规则管理{% endblock %}
{% block page_description %}配置智能分配算法的权重计算规则{% endblock %}

{% block header_actions %}
<a href="{% url 'evaluations:admin:rule_create' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
    <i data-lucide="plus" class="w-4 h-4"></i>
    <span>新建规则</span>
</a>
{% endblock %}

{% block admin_content %}
<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="settings" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">规则总数</p>
                <p class="text-2xl font-bold text-gray-900">{{ rules|length|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">启用中</p>
                <p class="text-2xl font-bold text-gray-900">
                    {% for rule in rules %}{% if rule.is_active %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
                </p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="pause-circle" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">已禁用</p>
                <p class="text-2xl font-bold text-gray-900">
                    {% for rule in rules %}{% if not rule.is_active %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
                </p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="trending-up" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">平均权重</p>
                <p class="text-2xl font-bold text-gray-900">
                    {% if rules %}
                        {% with total=0 count=0 %}
                            {% for rule in rules %}
                                {% if rule.is_active %}
                                    {{ total|add:rule.weight_factor|floatformat:2 }}
                                {% endif %}
                            {% endfor %}
                        {% endwith %}
                    {% else %}0.00{% endif %}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i data-lucide="search" class="h-4 w-4 text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" 
                           class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500" 
                           placeholder="搜索规则名称或描述...">
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <select id="conditionFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">所有条件类型</option>
                    <option value="department">部门关系</option>
                    <option value="position_level">职位层级</option>
                    <option value="relation_type">评价关系</option>
                    <option value="default">默认规则</option>
                </select>
                <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">所有状态</option>
                    <option value="active">启用中</option>
                    <option value="inactive">已禁用</option>
                </select>
                <select id="priorityFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">所有优先级</option>
                    <option value="high">高优先级 (≥5)</option>
                    <option value="medium">中优先级 (2-4)</option>
                    <option value="low">低优先级 (1)</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Rules List -->
<div class="space-y-4" id="rulesList">
    {% for rule in rules %}
        <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow rule-card border border-gray-200"
             data-condition="{{ rule.condition_type }}" 
             data-status="{% if rule.is_active %}active{% else %}inactive{% endif %}"
             data-priority="{% if rule.priority >= 5 %}high{% elif rule.priority >= 2 %}medium{% else %}low{% endif %}">
            
            <div class="px-6 py-6">
                <div class="flex items-start justify-between">
                    <!-- 规则信息 -->
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-3">
                            <div class="flex-shrink-0">
                                {% if rule.condition_type == 'department' %}
                                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="building" class="w-6 h-6 text-blue-600"></i>
                                    </div>
                                {% elif rule.condition_type == 'position_level' %}
                                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="layers" class="w-6 h-6 text-green-600"></i>
                                    </div>
                                {% elif rule.condition_type == 'relation_type' %}
                                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="users" class="w-6 h-6 text-purple-600"></i>
                                    </div>
                                {% else %}
                                    <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="settings" class="w-6 h-6 text-gray-600"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">{{ rule.name }}</h3>
                                <div class="flex items-center space-x-4 text-sm text-gray-600">
                                    <span class="flex items-center">
                                        <i data-lucide="tag" class="w-4 h-4 mr-1"></i>
                                        {% if rule.condition_type == 'department' %}部门关系
                                        {% elif rule.condition_type == 'position_level' %}职位层级
                                        {% elif rule.condition_type == 'relation_type' %}评价关系
                                        {% elif rule.condition_type == 'default' %}默认规则
                                        {% endif %}
                                    </span>
                                    <span class="flex items-center">
                                        <i data-lucide="target" class="w-4 h-4 mr-1"></i>
                                        权重：{{ rule.weight_factor }}x
                                    </span>
                                    <span class="flex items-center">
                                        <i data-lucide="arrow-up" class="w-4 h-4 mr-1"></i>
                                        优先级：{{ rule.priority }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        {% if rule.description %}
                            <p class="text-gray-600 mb-4">{{ rule.description|truncatechars:100 }}</p>
                        {% endif %}
                        
                        <!-- 规则详情 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                            <div class="flex items-center text-gray-600">
                                <i data-lucide="filter" class="w-4 h-4 mr-2"></i>
                                <span>条件值：{{ rule.condition_value|default:"无" }}</span>
                            </div>
                            {% if rule.relation_type %}
                                <div class="flex items-center text-gray-600">
                                    <i data-lucide="link" class="w-4 h-4 mr-2"></i>
                                    <span>关系：
                                        {% if rule.relation_type == 'subordinate_to_superior' %}下级评上级
                                        {% elif rule.relation_type == 'superior_to_subordinate' %}上级评下级
                                        {% elif rule.relation_type == 'peer_to_peer' %}同级互评
                                        {% elif rule.relation_type == 'cross_department' %}跨部门评价
                                        {% endif %}
                                    </span>
                                </div>
                            {% endif %}
                            <div class="flex items-center text-gray-600">
                                <i data-lucide="clock" class="w-4 h-4 mr-2"></i>
                                <span>创建：{{ rule.created_at|date:"Y-m-d" }}</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
                                <span>更新：{{ rule.updated_at|date:"Y-m-d" }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 状态和操作 -->
                    <div class="flex flex-col items-end space-y-3 ml-6">
                        <!-- 状态标签 -->
                        {% if rule.is_active %}
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                <i data-lucide="check-circle" class="w-4 h-4 mr-1"></i>
                                启用中
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                <i data-lucide="pause-circle" class="w-4 h-4 mr-1"></i>
                                已禁用
                            </span>
                        {% endif %}
                        
                        <!-- 优先级标签 -->
                        {% if rule.priority >= 5 %}
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
                                高优先级
                            </span>
                        {% elif rule.priority >= 2 %}
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                中优先级
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                低优先级
                            </span>
                        {% endif %}
                        
                        <!-- 操作按钮 -->
                        <div class="flex items-center space-x-2">
                            {% if rule.is_active %}
                                <button onclick="toggleRuleStatus({{ rule.pk }}, false)" 
                                        class="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-50">
                                    <i data-lucide="pause" class="w-4 h-4 inline mr-1"></i>
                                    禁用
                                </button>
                            {% else %}
                                <button onclick="toggleRuleStatus({{ rule.pk }}, true)" 
                                        class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                                    <i data-lucide="play" class="w-4 h-4 inline mr-1"></i>
                                    启用
                                </button>
                            {% endif %}
                            
                            <a href="{% url 'evaluations:admin:rule_update' rule.pk %}" 
                               class="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-50">
                                编辑
                            </a>
                            
                            <div class="relative">
                                <button type="button" onclick="toggleDropdown({{ rule.pk }})" 
                                        class="p-1 text-gray-400 hover:text-gray-600">
                                    <i data-lucide="more-vertical" class="w-4 h-4"></i>
                                </button>
                                <div id="dropdown-{{ rule.pk }}" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                                    <div class="py-1">
                                        <button onclick="copyRule({{ rule.pk }})" 
                                                class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i data-lucide="copy" class="w-4 h-4 mr-2"></i>
                                            复制规则
                                        </button>
                                        <a href="{% url 'evaluations:admin:rule_update' rule.pk %}" 
                                           class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
                                            编辑规则
                                        </a>
                                        <div class="border-t border-gray-100"></div>
                                        <button onclick="deleteRule({{ rule.pk }}, '{{ rule.name }}')" 
                                                class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                            <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                                            删除规则
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% empty %}
        <!-- 空状态 -->
        <div class="text-center py-12">
            <i data-lucide="settings" class="mx-auto h-16 w-16 text-gray-400"></i>
            <h3 class="mt-4 text-lg font-medium text-gray-900">还没有权重规则</h3>
            <p class="mt-2 text-sm text-gray-500">创建第一个权重规则，配置智能分配算法。</p>
            <div class="mt-6">
                <a href="{% url 'evaluations:admin:rule_create' %}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    创建规则
                </a>
            </div>
        </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="mt-8 flex items-center justify-between">
    <div class="text-sm text-gray-700">
        显示第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 项，共 {{ paginator.count }} 项
    </div>
    <nav class="flex items-center space-x-2">
        {% if page_obj.has_previous %}
            <a href="?page=1" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">首页</a>
            <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</a>
        {% endif %}
        
        <span class="px-3 py-2 text-sm bg-blue-50 border border-blue-200 rounded-md text-blue-600">
            {{ page_obj.number }}
        </span>
        
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</a>
            <a href="?page={{ paginator.num_pages }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">末页</a>
        {% endif %}
    </nav>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        filterRules();
    });

    // 条件类型筛选
    document.getElementById('conditionFilter').addEventListener('change', function() {
        filterRules();
    });

    // 状态筛选
    document.getElementById('statusFilter').addEventListener('change', function() {
        filterRules();
    });

    // 优先级筛选
    document.getElementById('priorityFilter').addEventListener('change', function() {
        filterRules();
    });

    function filterRules() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const conditionFilter = document.getElementById('conditionFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const priorityFilter = document.getElementById('priorityFilter').value;
        const ruleCards = document.querySelectorAll('.rule-card');

        ruleCards.forEach(card => {
            const title = card.querySelector('h3').textContent.toLowerCase();
            const description = card.querySelector('p') ? card.querySelector('p').textContent.toLowerCase() : '';
            const condition = card.dataset.condition;
            const status = card.dataset.status;
            const priority = card.dataset.priority;

            let showCard = true;

            // 文本搜索
            if (searchTerm && !title.includes(searchTerm) && !description.includes(searchTerm)) {
                showCard = false;
            }

            // 条件类型筛选
            if (conditionFilter && condition !== conditionFilter) {
                showCard = false;
            }

            // 状态筛选
            if (statusFilter && status !== statusFilter) {
                showCard = false;
            }

            // 优先级筛选
            if (priorityFilter && priority !== priorityFilter) {
                showCard = false;
            }

            card.style.display = showCard ? 'block' : 'none';
        });
    }

    // 切换规则状态
    function toggleRuleStatus(ruleId, isActive) {
        const action = isActive ? '启用' : '禁用';
        if (confirm(`确定要${action}这个权重规则吗？`)) {
            fetch('{% url "evaluations:admin:rule_toggle" 0 %}'.replace('0', ruleId), {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCsrfToken(),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ is_active: isActive })
            })
            .then(response => {
                if (response.ok) {
                    showNotification(`规则${action}成功`, 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification(`${action}失败，请重试`, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification(`${action}失败，请重试`, 'error');
            });
        }
    }

    // 复制规则
    function copyRule(ruleId) {
        if (confirm('确定要复制这个权重规则吗？复制后的规则将处于禁用状态。')) {
            fetch('{% url "evaluations:admin:rule_copy" 0 %}'.replace('0', ruleId), {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCsrfToken(),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => {
                if (response.ok) {
                    showNotification('规则复制成功', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification('复制失败，请重试', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('复制失败，请重试', 'error');
            });
        }
    }

    // 删除规则
    function deleteRule(ruleId, ruleName) {
        if (confirm(`确定要删除权重规则"${ruleName}"吗？此操作不可恢复。`)) {
            fetch('{% url "evaluations:admin:rule_delete" 0 %}'.replace('0', ruleId), {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCsrfToken(),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => {
                if (response.ok) {
                    showNotification('规则删除成功', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification('删除失败，请重试', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('删除失败，请重试', 'error');
            });
        }
    }

    // 切换下拉菜单
    function toggleDropdown(ruleId) {
        const dropdown = document.getElementById(`dropdown-${ruleId}`);
        const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
        
        // 关闭其他下拉菜单
        allDropdowns.forEach(d => {
            if (d !== dropdown) {
                d.classList.add('hidden');
            }
        });
        
        // 切换当前下拉菜单
        dropdown.classList.toggle('hidden');
    }

    // 点击外部关闭下拉菜单
    document.addEventListener('click', function(event) {
        if (!event.target.closest('[onclick*="toggleDropdown"]') && !event.target.closest('[id^="dropdown-"]')) {
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(d => d.classList.add('hidden'));
        }
    });

    // 搜索框自动聚焦
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }
    });

    // 获取CSRF Token
    function getCsrfToken() {
        // 优先从表单中获取token（最可靠）
        const formToken = document.querySelector('[name=csrfmiddlewaretoken]');
        if (formToken && formToken.value) {
            return formToken.value;
        }

        // 备用：从meta标签获取
        const metaToken = document.querySelector('meta[name=csrf-token]');
        if (metaToken && metaToken.content) {
            return metaToken.content;
        }

        // 最后：从cookie获取
        return getCookie('csrftoken');
    }

    // 获取Cookie值
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // 通知函数
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.innerHTML = `
            <div class="flex items-center space-x-3">
                <div class="flex-1">
                    <p class="text-sm font-medium">${message}</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
        
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }
</script>
{% endblock %}