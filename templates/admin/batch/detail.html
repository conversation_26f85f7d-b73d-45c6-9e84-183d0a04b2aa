{% extends "admin/base_admin.html" %}

{% block page_title %}批次详情 - {{ batch.name }}{% endblock %}
{% block page_description %}查看考评批次的详细信息、进度统计和参与人员情况{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'evaluations:admin:batch_list' %}" class="btn btn-secondary btn-md">
        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
        返回列表
    </a>
    
    {% if batch.status == 'draft' %}
        <a href="{% url 'evaluations:admin:batch_template_config' batch.pk %}" class="btn btn-primary btn-md">
            <i data-lucide="settings" class="w-4 h-4 mr-2"></i>
            配置模板
        </a>
        <a href="{% url 'evaluations:admin:batch_update' batch.pk %}" class="btn btn-primary btn-md">
            <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
            编辑批次
        </a>
    {% endif %}
    
    {% if batch.status == 'draft' %}
        <form method="post" action="{% url 'evaluations:admin:batch_activate' batch.pk %}" class="inline">
            {% csrf_token %}
            <button type="submit" class="btn btn-success btn-md" onclick="return confirm('确定要激活这个批次吗？激活后将开始考评流程。')">
                <i data-lucide="play" class="w-4 h-4 mr-2"></i>
                激活批次
            </button>
        </form>
    {% elif batch.status == 'active' %}
        <form method="post" action="{% url 'evaluations:admin:batch_complete' batch.pk %}" class="inline">
            {% csrf_token %}
            <button type="submit" class="btn btn-primary btn-md" onclick="return confirm('确定要完成这个批次吗？完成后将无法继续评分。')">
                <i data-lucide="check" class="w-4 h-4 mr-2"></i>
                完成批次
            </button>
        </form>
    {% endif %}
</div>
{% endblock %}

{% block admin_content %}
<div class="space-y-8">
    <!-- 批次基本信息 -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">基本信息</h3>
                <span class="badge badge-{{ batch.status }} text-sm">{{ batch.get_status_display }}</span>
            </div>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="text-sm font-medium text-gray-600">批次名称</label>
                    <p class="text-base text-gray-900 mt-1">{{ batch.name }}</p>
                </div>
                <div class="time-card green">
                    <div class="flex items-center mb-2">
                        <i data-lucide="play-circle" class="time-icon text-green-600"></i>
                        <label class="text-sm font-medium text-green-700">开始时间</label>
                    </div>
                    <div class="space-y-1">
                        <p class="time-primary">{{ batch.start_date|date:"Y年m月d日" }}</p>
                        <p class="time-secondary">{{ batch.start_date|date:"H:i" }}</p>
                    </div>
                    <div class="time-status-indicator success"></div>
                </div>
                <div class="time-card red">
                    <div class="flex items-center mb-2">
                        <i data-lucide="stop-circle" class="time-icon text-red-600"></i>
                        <label class="text-sm font-medium text-red-700">结束时间</label>
                    </div>
                    <div class="space-y-1">
                        <p class="time-primary">{{ batch.end_date|date:"Y年m月d日" }}</p>
                        <p class="time-secondary">{{ batch.end_date|date:"H:i" }}</p>
                    </div>
                    <div class="time-status-indicator pending"></div>
                </div>
                <div class="time-card blue">
                    <div class="flex items-center mb-2">
                        <i data-lucide="calendar-plus" class="time-icon text-blue-600"></i>
                        <label class="text-sm font-medium text-blue-700">创建时间</label>
                    </div>
                    <div class="space-y-1">
                        <p class="time-primary">{{ batch.created_at|date:"Y年m月d日" }}</p>
                        <p class="time-secondary">{{ batch.created_at|date:"H:i" }}</p>
                    </div>
                    <div class="time-status-indicator"></div>
                </div>
                <div>
                    <label class="text-sm font-medium text-gray-600">创建人</label>
                    <p class="text-base text-gray-900 mt-1">{{ batch.created_by|default:"系统" }}</p>
                </div>
                <div>
                    <label class="text-sm font-medium text-gray-600">是否启用</label>
                    <p class="text-base text-gray-900 mt-1">
                        {% if batch.is_active %}
                            <span class="badge badge-success">启用</span>
                        {% else %}
                            <span class="badge badge-gray">禁用</span>
                        {% endif %}
                    </p>
                </div>
            </div>
            {% if batch.description %}
            <div class="mt-6">
                <label class="text-sm font-medium text-gray-600">批次描述</label>
                <p class="text-base text-gray-900 mt-1">{{ batch.description }}</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="card">
            <div class="card-body text-center">
                <i data-lucide="users" class="w-8 h-8 text-blue-600 mx-auto mb-2"></i>
                <div class="text-2xl font-bold text-gray-900">{{ batch.get_relations_count|default:0 }}</div>
                <div class="text-sm text-gray-600">评价关系</div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body text-center">
                <i data-lucide="check-circle" class="w-8 h-8 text-green-600 mx-auto mb-2"></i>
                <div class="text-2xl font-bold text-gray-900">{{ batch.get_completed_count|default:0 }}</div>
                <div class="text-sm text-gray-600">已完成</div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body text-center">
                <i data-lucide="clock" class="w-8 h-8 text-yellow-600 mx-auto mb-2"></i>
                <div class="text-2xl font-bold text-gray-900">{{ batch.get_relations_count|add:batch.get_completed_count|sub:batch.get_completed_count|default:0 }}</div>
                <div class="text-sm text-gray-600">待完成</div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body text-center">
                <i data-lucide="percent" class="w-8 h-8 text-purple-600 mx-auto mb-2"></i>
                <div class="text-2xl font-bold text-gray-900">{{ batch.get_completion_rate|floatformat:1|default:0 }}%</div>
                <div class="text-sm text-gray-600">完成率</div>
            </div>
        </div>
    </div>

    <!-- 配置的模板信息 -->
    {% if batch_templates %}
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">配置的模板</h3>
        </div>
        <div class="card-body">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模板名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关系类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否首选</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">评分项数</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for bt in batch_templates %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ bt.template.name }}</div>
                                <div class="text-sm text-gray-500">{{ bt.template.get_template_type_display }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="badge badge-secondary">{{ bt.get_relation_type_display }}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if bt.is_preferred %}
                                    <span class="badge badge-success">首选</span>
                                {% else %}
                                    <span class="badge badge-gray">普通</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ bt.template.get_items_count|default:0 }} 个
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 默认模板信息（向后兼容） -->
    {% if batch.default_template and not batch_templates %}
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">默认模板</h3>
        </div>
        <div class="card-body">
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                    <i data-lucide="file-text" class="w-8 h-8 text-blue-600"></i>
                </div>
                <div>
                    <h4 class="text-base font-medium text-gray-900">{{ batch.default_template.name }}</h4>
                    <p class="text-sm text-gray-600">{{ batch.default_template.get_template_type_display }} • {{ batch.default_template.get_items_count|default:0 }} 个评分项</p>
                    {% if batch.default_template.description %}
                        <p class="text-sm text-gray-500 mt-1">{{ batch.default_template.description|truncatewords:20 }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 操作记录 -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900">操作记录</h3>
        </div>
        <div class="card-body">
            <div class="text-center py-8 text-gray-500">
                <i data-lucide="clock" class="w-12 h-12 mx-auto mb-4 text-gray-400"></i>
                <p>暂无操作记录</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图标
    if (window.lucide) {
        lucide.createIcons({ nameAttr: 'data-lucide' });
    }
});
</script>
{% endblock %}