{% extends "admin/base_admin.html" %}

{% block page_title %}{{ page_title }}{% endblock %}
{% block page_description %}为考评批次配置可用模板和关系类型映射，实现灵活的差异化考评{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'evaluations:admin:batch_list' %}" class="btn btn-secondary btn-md">
        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
        返回批次列表
    </a>
    <button type="submit" form="templateConfigForm" class="btn btn-primary btn-md">
        <i data-lucide="save" class="w-4 h-4 mr-2"></i>
        保存配置
    </button>
</div>
{% endblock %}

{% block admin_content %}
<div class="space-y-8">
    <!-- 批次信息卡片 -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">批次信息</h3>
                <span class="badge badge-{{ batch.status }}">{{ batch.get_status_display }}</span>
            </div>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="text-sm font-medium text-gray-600">批次名称</label>
                    <p class="text-base text-gray-900 mt-1">{{ batch.name }}</p>
                </div>
                <div>
                    <label class="text-sm font-medium text-gray-600">开始时间</label>
                    <p class="text-base text-gray-900 mt-1">{{ batch.start_date|date:"Y-m-d H:i" }}</p>
                </div>
                <div>
                    <label class="text-sm font-medium text-gray-600">结束时间</label>
                    <p class="text-base text-gray-900 mt-1">{{ batch.end_date|date:"Y-m-d H:i" }}</p>
                </div>
            </div>
            {% if batch.description %}
            <div class="mt-4">
                <label class="text-sm font-medium text-gray-600">批次描述</label>
                <p class="text-base text-gray-900 mt-1">{{ batch.description }}</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 模板配置表单 -->
    <form id="templateConfigForm" method="post" class="space-y-8">
        {% csrf_token %}
        
        <!-- 可用模板选择 -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">可用模板选择</h3>
                <p class="text-sm text-gray-600 mt-1">选择本批次可以使用的考评模板</p>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for choice in form.available_templates %}
                        <div class="template-choice-item p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors duration-200">
                            <div class="flex items-start space-x-3">
                                {{ choice.tag }}
                                <div class="flex-1 min-w-0">
                                    <label for="{{ choice.id_for_label }}" class="block text-sm font-medium text-gray-900 cursor-pointer">
                                        {{ choice.choice_label }}
                                    </label>
                                    <p class="text-xs text-gray-500 mt-1">
                                        {% with template=choice.choice_value %}
                                        {{ template.get_template_type_display }} • 
                                        {{ template.get_items_count }} 个评分项
                                        {% endwith %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    {% empty %}
                        <div class="col-span-full text-center py-8 text-gray-500">
                            <i data-lucide="inbox" class="w-12 h-12 mx-auto mb-4 text-gray-400"></i>
                            <p>暂无可用的考评模板</p>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- 关系类型首选模板 -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">关系类型映射</h3>
                <p class="text-sm text-gray-600 mt-1">为不同的评价关系类型设置首选模板，实现差异化考评</p>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for field in form %}
                        {% if 'preferred_' in field.name %}
                            <div class="space-y-2">
                                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    {{ field.label }}
                                </label>
                                {{ field }}
                                {% if field.help_text %}
                                    <p class="text-xs text-gray-500">{{ field.help_text }}</p>
                                {% endif %}
                                {% if field.errors %}
                                    <div class="text-red-600 text-sm">
                                        {% for error in field.errors %}
                                            <p>{{ error }}</p>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- 当前配置预览 -->
        {% if current_templates %}
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-gray-900">当前配置</h3>
                <p class="text-sm text-gray-600 mt-1">已配置的模板和关系类型映射</p>
            </div>
            <div class="card-body">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模板名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关系类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否首选</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">排序</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for bt in current_templates %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="text-sm font-medium text-gray-900">{{ bt.template.name }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="badge badge-secondary">{{ bt.get_relation_type_display }}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if bt.is_preferred %}
                                        <span class="badge badge-success">首选</span>
                                    {% else %}
                                        <span class="badge badge-gray">普通</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ bt.sort_order }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图标
    if (window.lucide) {
        lucide.createIcons({ nameAttr: 'data-lucide' });
    }
    
    // 模板选择交互效果
    const templateItems = document.querySelectorAll('.template-choice-item');
    templateItems.forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        if (checkbox) {
            // 初始状态
            updateItemState(item, checkbox.checked);
            
            // 监听变化
            checkbox.addEventListener('change', function() {
                updateItemState(item, this.checked);
            });
        }
    });
    
    function updateItemState(item, checked) {
        if (checked) {
            item.classList.add('border-blue-500', 'bg-blue-50');
            item.classList.remove('border-gray-200');
        } else {
            item.classList.remove('border-blue-500', 'bg-blue-50');
            item.classList.add('border-gray-200');
        }
    }
    
    // 表单提交确认
    const form = document.getElementById('templateConfigForm');
    form.addEventListener('submit', function(e) {
        const checkedTemplates = form.querySelectorAll('input[name="available_templates"]:checked');
        if (checkedTemplates.length === 0) {
            e.preventDefault();
            alert('请至少选择一个可用模板');
            return false;
        }
    });
});
</script>
{% endblock %}