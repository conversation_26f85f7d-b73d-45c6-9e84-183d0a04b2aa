{% extends "admin/base_admin.html" %}

{% block page_title %}{{ title|default:"考评关系管理" }}{% endblock %}
{% block page_description %}
    {% if action == 'create' %}
    创建新的考评关系，建立评价者与被评价者的对应关系
    {% else %}
    编辑现有考评关系的配置信息
    {% endif %}
{% endblock %}

{% block header_actions %}
<a href="{% url 'evaluations:admin:relation_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
    <i data-lucide="arrow-left" class="w-4 h-4"></i>
    <span>返回列表</span>
</a>
{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">{{ title }}</h3>
            <p class="mt-1 text-sm text-gray-600">
                {% if action == 'create' %}
                填写下面的表单信息来创建新的考评关系。系统会自动检查关系的有效性。
                {% else %}
                修改考评关系的配置信息。请注意，如果该关系已有评分记录，某些修改可能会影响已有数据。
                {% endif %}
            </p>
        </div>

        <form method="post" class="p-6 space-y-6" id="relationForm">
            {% csrf_token %}
            
            <!-- 基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 考评批次 -->
                <div>
                    <label for="id_batch" class="block text-sm font-medium text-gray-700 mb-2">
                        考评批次 <span class="text-red-500">*</span>
                    </label>
                    <select name="batch" id="id_batch" required 
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">请选择考评批次</option>
                        {% for batch in form.batch.field.queryset %}
                        <option value="{{ batch.id }}" 
                                {% if form.batch.value == batch.id %}selected{% endif %}
                                data-status="{{ batch.status }}"
                                data-start-date="{{ batch.start_date|date:'Y-m-d' }}"
                                data-end-date="{{ batch.end_date|date:'Y-m-d' }}">
                            {{ batch.name }} ({{ batch.get_status_display }})
                        </option>
                        {% endfor %}
                    </select>
                    {% if form.batch.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.batch.help_text }}</p>
                    {% endif %}
                    {% if form.batch.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.batch.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- 考评模板 -->
                <div>
                    <label for="id_template" class="block text-sm font-medium text-gray-700 mb-2">
                        考评模板 <span class="text-red-500">*</span>
                    </label>
                    <select name="template" id="id_template" required
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">请选择考评模板</option>
                        {% for template in form.template.field.queryset %}
                        <option value="{{ template.id }}" 
                                {% if form.template.value == template.id %}selected{% endif %}
                                data-type="{{ template.template_type }}"
                                data-items-count="{{ template.evaluationitem_set.count }}">
                            {{ template.name }} ({{ template.get_template_type_display }})
                        </option>
                        {% endfor %}
                    </select>
                    <div id="templatePreview" class="mt-2 hidden">
                        <div class="p-3 bg-blue-50 rounded-md">
                            <div class="flex items-center">
                                <i data-lucide="info" class="w-4 h-4 text-blue-600 mr-2"></i>
                                <span class="text-sm text-blue-800" id="templateInfo"></span>
                            </div>
                        </div>
                    </div>
                    {% if form.template.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.template.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- 人员选择 -->
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">人员配置</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 评价者 -->
                    <div>
                        <label for="id_evaluator" class="block text-sm font-medium text-gray-700 mb-2">
                            评价者 <span class="text-red-500">*</span>
                        </label>
                        <div class="space-y-2">
                            <!-- 部门筛选 -->
                            <select id="evaluatorDeptFilter" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">选择部门筛选评价者</option>
                                {% for dept in departments %}
                                <option value="{{ dept.id }}">{{ dept.name }}</option>
                                {% endfor %}
                            </select>
                            
                            <!-- 评价者选择 -->
                            <select name="evaluator" id="id_evaluator" required
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">请选择评价者</option>
                                {% for staff in form.evaluator.field.queryset %}
                                <option value="{{ staff.id }}" 
                                        {% if form.evaluator.value == staff.id %}selected{% endif %}
                                        data-department="{{ staff.department.id }}"
                                        data-position="{{ staff.position.name|default:'未设置' }}"
                                        data-level="{{ staff.position.level|default:'0' }}">
                                    {{ staff.name }} - {{ staff.department.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div id="evaluatorInfo" class="mt-2 hidden">
                            <div class="p-2 bg-gray-50 rounded text-sm text-gray-600">
                                <span id="evaluatorDetails"></span>
                            </div>
                        </div>
                        {% if form.evaluator.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.evaluator.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 被评价者 -->
                    <div>
                        <label for="id_evaluatee" class="block text-sm font-medium text-gray-700 mb-2">
                            被评价者 <span class="text-red-500">*</span>
                        </label>
                        <div class="space-y-2">
                            <!-- 部门筛选 -->
                            <select id="evaluateeDeptFilter" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">选择部门筛选被评价者</option>
                                {% for dept in departments %}
                                <option value="{{ dept.id }}">{{ dept.name }}</option>
                                {% endfor %}
                            </select>
                            
                            <!-- 被评价者选择 -->
                            <select name="evaluatee" id="id_evaluatee" required
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">请选择被评价者</option>
                                {% for staff in form.evaluatee.field.queryset %}
                                <option value="{{ staff.id }}" 
                                        {% if form.evaluatee.value == staff.id %}selected{% endif %}
                                        data-department="{{ staff.department.id }}"
                                        data-position="{{ staff.position.name|default:'未设置' }}"
                                        data-level="{{ staff.position.level|default:'0' }}">
                                    {{ staff.name }} - {{ staff.department.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div id="evaluateeInfo" class="mt-2 hidden">
                            <div class="p-2 bg-gray-50 rounded text-sm text-gray-600">
                                <span id="evaluateeDetails"></span>
                            </div>
                        </div>
                        {% if form.evaluatee.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.evaluatee.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- 关系验证提示 -->
                <div id="relationValidation" class="mt-4 hidden">
                    <div id="validationMessage" class="p-3 rounded-md"></div>
                </div>
            </div>

            <!-- 高级配置 -->
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">高级配置</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 权重系数 -->
                    <div>
                        <label for="id_weight_factor" class="block text-sm font-medium text-gray-700 mb-2">
                            权重系数 <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="number" name="weight_factor" id="id_weight_factor" 
                                   min="0.1" max="5.0" step="0.1"
                                   value="{{ form.weight_factor.value|default:'1.0' }}"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   required>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <span class="text-gray-500 text-sm">×</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="flex items-center space-x-4 text-xs text-gray-500">
                                <span class="flex items-center">
                                    <span class="w-2 h-2 bg-yellow-400 rounded mr-1"></span>
                                    &lt; 1.0 降权
                                </span>
                                <span class="flex items-center">
                                    <span class="w-2 h-2 bg-gray-400 rounded mr-1"></span>
                                    = 1.0 标准
                                </span>
                                <span class="flex items-center">
                                    <span class="w-2 h-2 bg-red-400 rounded mr-1"></span>
                                    &gt; 1.0 加权
                                </span>
                            </div>
                        </div>
                        {% if form.weight_factor.help_text %}
                        <p class="mt-1 text-sm text-gray-500">{{ form.weight_factor.help_text }}</p>
                        {% endif %}
                        {% if form.weight_factor.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.weight_factor.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 分配方式 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            分配方式 <span class="text-red-500">*</span>
                        </label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="assignment_method" value="auto" 
                                       {% if form.assignment_method.value == 'auto' or not form.assignment_method.value %}checked{% endif %}
                                       class="form-radio">
                                <span class="ml-2 text-sm text-gray-900">
                                    <i data-lucide="zap" class="w-4 h-4 inline mr-1 text-blue-600"></i>
                                    自动分配
                                </span>
                                <span class="ml-2 text-xs text-gray-500">由系统智能分配算法生成</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="assignment_method" value="manual"
                                       {% if form.assignment_method.value == 'manual' %}checked{% endif %}
                                       class="form-radio">
                                <span class="ml-2 text-sm text-gray-900">
                                    <i data-lucide="user" class="w-4 h-4 inline mr-1 text-gray-600"></i>
                                    手动分配
                                </span>
                                <span class="ml-2 text-xs text-gray-500">由管理员手动创建</span>
                            </label>
                        </div>
                        {% if form.assignment_method.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.assignment_method.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 表单操作 -->
            <div class="border-t border-gray-200 pt-6">
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'evaluations:admin:relation_list' %}" 
                       class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </a>
                    <button type="submit" id="submitBtn"
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i data-lucide="save" class="w-4 h-4 inline mr-1"></i>
                        {% if action == 'create' %}创建关系{% else %}保存修改{% endif %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 部门筛选功能
    function setupDepartmentFilter(deptFilterId, staffSelectId) {
        const deptFilter = document.getElementById(deptFilterId);
        const staffSelect = document.getElementById(staffSelectId);
        const allOptions = Array.from(staffSelect.options).slice(1); // 保留第一个空选项
        
        deptFilter.addEventListener('change', function() {
            const selectedDeptId = this.value;
            
            // 清空并重新添加第一个空选项
            staffSelect.innerHTML = '<option value="">请选择人员</option>';
            
            if (selectedDeptId) {
                // 筛选指定部门的人员
                allOptions.forEach(option => {
                    if (option.dataset.department === selectedDeptId) {
                        staffSelect.appendChild(option.cloneNode(true));
                    }
                });
            } else {
                // 显示所有人员
                allOptions.forEach(option => {
                    staffSelect.appendChild(option.cloneNode(true));
                });
            }
            
            // 触发人员选择变化事件
            staffSelect.dispatchEvent(new Event('change'));
        });
    }
    
    // 设置部门筛选
    setupDepartmentFilter('evaluatorDeptFilter', 'id_evaluator');
    setupDepartmentFilter('evaluateeDeptFilter', 'id_evaluatee');
    
    // 人员信息显示
    function setupStaffInfo(staffSelectId, infoId, detailsId) {
        const staffSelect = document.getElementById(staffSelectId);
        const infoDiv = document.getElementById(infoId);
        const detailsSpan = document.getElementById(detailsId);
        
        staffSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            
            if (this.value && selectedOption) {
                const position = selectedOption.dataset.position;
                const level = selectedOption.dataset.level;
                const department = selectedOption.textContent.split(' - ')[1];
                
                detailsSpan.textContent = `职位：${position}，级别：${level}级，部门：${department}`;
                infoDiv.classList.remove('hidden');
            } else {
                infoDiv.classList.add('hidden');
            }
            
            validateRelation();
        });
    }
    
    // 设置人员信息显示
    setupStaffInfo('id_evaluator', 'evaluatorInfo', 'evaluatorDetails');
    setupStaffInfo('id_evaluatee', 'evaluateeInfo', 'evaluateeDetails');
    
    // 模板预览
    document.getElementById('id_template').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const previewDiv = document.getElementById('templatePreview');
        const infoSpan = document.getElementById('templateInfo');
        
        if (this.value && selectedOption) {
            const type = selectedOption.dataset.type;
            const itemsCount = selectedOption.dataset.itemsCount;
            infoSpan.textContent = `模板类型：${selectedOption.textContent.split('(')[1].replace(')', '')}，包含 ${itemsCount} 个评分项`;
            previewDiv.classList.remove('hidden');
        } else {
            previewDiv.classList.add('hidden');
        }
    });
    
    // 关系验证
    function validateRelation() {
        const evaluatorId = document.getElementById('id_evaluator').value;
        const evaluateeId = document.getElementById('id_evaluatee').value;
        const validationDiv = document.getElementById('relationValidation');
        const messageDiv = document.getElementById('validationMessage');
        
        if (evaluatorId && evaluateeId) {
            if (evaluatorId === evaluateeId) {
                // 自评检查
                messageDiv.className = 'p-3 rounded-md bg-red-50 border border-red-200';
                messageDiv.innerHTML = '<div class="flex items-center"><i data-lucide="alert-circle" class="w-4 h-4 text-red-600 mr-2"></i><span class="text-red-800 text-sm">错误：评价者和被评价者不能是同一人</span></div>';
                validationDiv.classList.remove('hidden');
                document.getElementById('submitBtn').disabled = true;
            } else {
                // 检查关系类型和合理性
                const evaluatorOption = document.querySelector(`#id_evaluator option[value="${evaluatorId}"]`);
                const evaluateeOption = document.querySelector(`#id_evaluatee option[value="${evaluateeId}"]`);
                
                if (evaluatorOption && evaluateeOption) {
                    const evaluatorLevel = parseInt(evaluatorOption.dataset.level);
                    const evaluateeLevel = parseInt(evaluateeOption.dataset.level);
                    const evaluatorDept = evaluatorOption.dataset.department;
                    const evaluateeDept = evaluateeOption.dataset.department;
                    
                    let relationType = '';
                    let typeClass = 'bg-green-50 border-green-200';
                    let iconClass = 'text-green-600';
                    let textClass = 'text-green-800';
                    
                    if (evaluatorDept !== evaluateeDept) {
                        relationType = '跨部门评价';
                        typeClass = 'bg-blue-50 border-blue-200';
                        iconClass = 'text-blue-600';
                        textClass = 'text-blue-800';
                    } else if (evaluatorLevel < evaluateeLevel) {
                        relationType = '下级评价上级';
                        typeClass = 'bg-yellow-50 border-yellow-200';
                        iconClass = 'text-yellow-600';
                        textClass = 'text-yellow-800';
                    } else if (evaluatorLevel > evaluateeLevel) {
                        relationType = '上级评价下级';
                    } else {
                        relationType = '同级互评';
                        typeClass = 'bg-purple-50 border-purple-200';
                        iconClass = 'text-purple-600';
                        textClass = 'text-purple-800';
                    }
                    
                    messageDiv.className = `p-3 rounded-md ${typeClass}`;
                    messageDiv.innerHTML = `<div class="flex items-center"><i data-lucide="info" class="w-4 h-4 ${iconClass} mr-2"></i><span class="${textClass} text-sm">关系类型：${relationType}</span></div>`;
                    validationDiv.classList.remove('hidden');
                    document.getElementById('submitBtn').disabled = false;
                }
            }
        } else {
            validationDiv.classList.add('hidden');
            document.getElementById('submitBtn').disabled = false;
        }
    }
    
    // 权重系数实时验证
    document.getElementById('id_weight_factor').addEventListener('input', function() {
        const value = parseFloat(this.value);
        if (value < 0.1 || value > 5.0) {
            this.setCustomValidity('权重系数必须在0.1到5.0之间');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // 表单提交处理
    document.getElementById('relationForm').addEventListener('submit', function(e) {
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 inline mr-1 animate-spin"></i>正在保存...';
        
        // 如果有验证错误，阻止提交
        const evaluatorId = document.getElementById('id_evaluator').value;
        const evaluateeId = document.getElementById('id_evaluatee').value;
        
        if (evaluatorId === evaluateeId) {
            e.preventDefault();
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i data-lucide="save" class="w-4 h-4 inline mr-1"></i>{% if action == "create" %}创建关系{% else %}保存修改{% endif %}';
            UI.showMessage('评价者和被评价者不能是同一人', 'error');
        }
    });
    
    // 页面加载时初始化
    document.getElementById('id_template').dispatchEvent(new Event('change'));
    document.getElementById('id_evaluator').dispatchEvent(new Event('change'));
    document.getElementById('id_evaluatee').dispatchEvent(new Event('change'));
});
</script>
{% endblock %}