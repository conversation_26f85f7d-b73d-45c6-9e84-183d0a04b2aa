{% extends "admin/base_admin.html" %}

{% block page_title %}考评关系管理{% endblock %}
{% block page_description %}管理考评关系分配，支持自动分配和手动调整{% endblock %}

{% block header_actions %}
<a href="{% url 'evaluations:admin:relation_create' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
    <i data-lucide="plus" class="w-4 h-4"></i>
    <span>新建关系</span>
</a>
{% endblock %}

{% block admin_content %}
<!-- 统计卡片 -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总关系数</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_relations|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">已分配</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.assigned_relations|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="clock" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">待分配</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.pending_relations|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="trending-up" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">分配率</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.assignment_rate|default:0 }}%</p>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和搜索区域 -->
<div class="bg-white rounded-lg shadow mb-6">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4">筛选条件</h3>
        
        <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- 批次筛选 -->
            <div>
                <label for="batch" class="block text-sm font-medium text-gray-700 mb-2">考评批次</label>
                <select name="batch" id="batch" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="">全部批次</option>
                    {% for batch in batches %}
                    <option value="{{ batch.id }}" {% if current_filters.batch == batch.id|stringformat:"s" %}selected{% endif %}>
                        {{ batch.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <!-- 部门筛选 -->
            <div>
                <label for="department" class="block text-sm font-medium text-gray-700 mb-2">相关部门</label>
                <select name="department" id="department" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="">全部部门</option>
                    {% for dept in departments %}
                    <option value="{{ dept.id }}" {% if current_filters.department == dept.id|stringformat:"s" %}selected{% endif %}>
                        {{ dept.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <!-- 分配状态筛选 -->
            <div>
                <label for="assigned" class="block text-sm font-medium text-gray-700 mb-2">分配状态</label>
                <select name="assigned" id="assigned" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="">全部状态</option>
                    <option value="true" {% if current_filters.assigned == "true" %}selected{% endif %}>已分配</option>
                    <option value="false" {% if current_filters.assigned == "false" %}selected{% endif %}>未分配</option>
                </select>
            </div>

            <!-- 搜索框 -->
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">搜索关键词</label>
                <div class="relative">
                    <input type="text" name="search" id="search" 
                           value="{{ current_filters.search }}" 
                           placeholder="搜索评价者、被评价者或批次名称"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <i data-lucide="search" class="w-4 h-4 text-gray-400"></i>
                    </div>
                </div>
            </div>
        </form>

        <div class="flex justify-between items-center mt-4">
            <button type="submit" form="search-form" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
                <i data-lucide="filter" class="w-4 h-4"></i>
                <span>应用筛选</span>
            </button>
            
            <a href="{% url 'evaluations:admin:relation_list' %}" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                清除筛选
            </a>
        </div>
    </div>

    <!-- 批量操作区域 -->
    <div class="p-6 border-b border-gray-200 bg-gray-50" id="bulk-actions" style="display: none;">
        <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">
                已选择 <span id="selected-count">0</span> 个关系
            </span>
            <div class="flex space-x-2">
                <button type="button" onclick="bulkAssign()" class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                    批量分配
                </button>
                <button type="button" onclick="bulkDelete()" class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700">
                    批量删除
                </button>
                <button type="button" onclick="clearSelection()" class="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700">
                    取消选择
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 关系列表 -->
<div class="bg-white rounded-lg shadow overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left">
                        <input type="checkbox" id="select-all" onchange="toggleSelectAll()" 
                               class="checkbox">
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        考评批次
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        评价者
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        被评价者
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        评价模板
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        权重系数
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        分配状态
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        分配方式
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        创建时间
                    </th>
                    <th scope="col" class="relative px-6 py-3">
                        <span class="sr-only">操作</span>
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for relation in relations %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" class="relation-checkbox checkbox" 
                               value="{{ relation.id }}" onchange="updateBulkActions()">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i data-lucide="calendar" class="w-4 h-4 text-blue-600"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">{{ relation.batch.name }}</div>
                                <div class="text-sm text-gray-500">{{ relation.batch.get_status_display }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <i data-lucide="user" class="w-4 h-4 text-green-600"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">{{ relation.evaluator.name }}</div>
                                <div class="text-sm text-gray-500">{{ relation.evaluator.department.name }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <i data-lucide="user-check" class="w-4 h-4 text-purple-600"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">{{ relation.evaluatee.name }}</div>
                                <div class="text-sm text-gray-500">{{ relation.evaluatee.department.name }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ relation.template.name }}</div>
                        <div class="text-sm text-gray-500">{{ relation.template.get_template_type_display }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                     {% if relation.weight_factor > 1.0 %}bg-red-100 text-red-800{% elif relation.weight_factor < 1.0 %}bg-yellow-100 text-yellow-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ relation.weight_factor }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if relation.is_assigned %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i data-lucide="check" class="w-3 h-3 mr-1"></i>
                            已分配
                        </span>
                        {% else %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                            待分配
                        </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                     {% if relation.assignment_method == 'auto' %}bg-blue-100 text-blue-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                            {% if relation.assignment_method == 'auto' %}
                                <i data-lucide="zap" class="w-3 h-3 mr-1"></i>
                                自动分配
                            {% else %}
                                <i data-lucide="user" class="w-3 h-3 mr-1"></i>
                                手动分配
                            {% endif %}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ relation.created_at|date:"Y-m-d H:i" }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center space-x-2">
                            <a href="{% url 'evaluations:admin:relation_update' relation.pk %}" 
                               class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" 
                               title="编辑关系">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </a>
                            <button onclick="deleteRelation({{ relation.id }}, '{{ relation.evaluator.name }}', '{{ relation.evaluatee.name }}')" 
                                    class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" 
                                    title="删除关系">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="10" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center">
                            <i data-lucide="users" class="w-12 h-12 text-gray-400 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无考评关系</h3>
                            <p class="text-sm text-gray-500 mb-4">还没有创建任何考评关系，请先创建考评批次或使用智能分配功能。</p>
                            <a href="{% url 'evaluations:admin:relation_create' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                创建第一个关系
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- 分页 -->
    {% if is_paginated %}
    <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    上一页
                </a>
                {% endif %}
                {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    下一页
                </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        显示第 <span class="font-medium">{{ page_obj.start_index }}</span> 到 
                        <span class="font-medium">{{ page_obj.end_index }}</span> 条，
                        共 <span class="font-medium">{{ page_obj.paginator.count }}</span> 条记录
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i data-lucide="chevron-left" class="w-4 h-4"></i>
                        </a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i data-lucide="chevron-right" class="w-4 h-4"></i>
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 删除确认模态框 -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i data-lucide="alert-triangle" class="w-6 h-6 text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">确认删除</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="deleteMessage">
                    确定要删除这个考评关系吗？此操作不可恢复。
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirmDelete" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">
                    确认删除
                </button>
                <button onclick="closeDeleteModal()" class="mt-3 px-4 py-2 bg-white text-gray-700 text-base font-medium rounded-md w-full shadow-sm border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-300">
                    取消
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('select-all');
    const checkboxes = document.querySelectorAll('.relation-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateBulkActions();
}

// 更新批量操作界面
function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.relation-checkbox:checked');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');
    
    if (checkboxes.length > 0) {
        bulkActions.style.display = 'block';
        selectedCount.textContent = checkboxes.length;
    } else {
        bulkActions.style.display = 'none';
    }
    
    // 更新全选复选框状态
    const allCheckboxes = document.querySelectorAll('.relation-checkbox');
    const selectAll = document.getElementById('select-all');
    selectAll.checked = allCheckboxes.length > 0 && checkboxes.length === allCheckboxes.length;
    selectAll.indeterminate = checkboxes.length > 0 && checkboxes.length < allCheckboxes.length;
}

// 清除选择
function clearSelection() {
    document.querySelectorAll('.relation-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('select-all').checked = false;
    updateBulkActions();
}

// 批量分配
function bulkAssign() {
    const checkboxes = document.querySelectorAll('.relation-checkbox:checked');
    const relationIds = Array.from(checkboxes).map(cb => cb.value);
    
    if (relationIds.length === 0) {
        alert('请先选择要分配的关系');
        return;
    }
    
    if (confirm(`确定要批量分配选中的 ${relationIds.length} 个关系吗？`)) {
        // 发送批量分配请求
        Utils.post('/evaluations/admin/relations/bulk-assign/', {
            relation_ids: relationIds
        }).then(response => {
            if (response.success) {
                UI.showMessage('批量分配成功', 'success');
                window.location.reload();
            } else {
                UI.showMessage(response.message || '批量分配失败', 'error');
            }
        }).catch(error => {
            console.error('批量分配失败:', error);
            UI.showMessage('批量分配失败，请重试', 'error');
        });
    }
}

// 批量删除
function bulkDelete() {
    const checkboxes = document.querySelectorAll('.relation-checkbox:checked');
    const relationIds = Array.from(checkboxes).map(cb => cb.value);
    
    if (relationIds.length === 0) {
        alert('请先选择要删除的关系');
        return;
    }
    
    if (confirm(`确定要批量删除选中的 ${relationIds.length} 个关系吗？此操作不可恢复。`)) {
        // 发送批量删除请求
        Utils.post('/evaluations/admin/relations/bulk-delete/', {
            relation_ids: relationIds
        }).then(response => {
            if (response.success) {
                UI.showMessage('批量删除成功', 'success');
                window.location.reload();
            } else {
                UI.showMessage(response.message || '批量删除失败', 'error');
            }
        }).catch(error => {
            console.error('批量删除失败:', error);
            UI.showMessage('批量删除失败，请重试', 'error');
        });
    }
}

// 删除单个关系
let relationToDelete = null;

function deleteRelation(relationId, evaluatorName, evaluateeName) {
    relationToDelete = relationId;
    const message = `确定要删除评价关系"${evaluatorName} → ${evaluateeName}"吗？此操作不可恢复。`;
    document.getElementById('deleteMessage').innerHTML = message;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    relationToDelete = null;
}

// 确认删除
document.getElementById('confirmDelete').addEventListener('click', function() {
    if (relationToDelete) {
        Utils.post(`/evaluations/admin/relations/${relationToDelete}/delete/`, {})
        .then(response => {
            if (response.success) {
                UI.showMessage('关系删除成功', 'success');
                window.location.reload();
            } else {
                UI.showMessage(response.message || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('删除失败:', error);
            UI.showMessage('删除失败，请重试', 'error');
        })
        .finally(() => {
            closeDeleteModal();
        });
    }
});

// 表单提交处理
document.querySelector('form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // 获取表单数据并跳转
    const formData = new FormData(this);
    const params = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }
    
    window.location.href = '?' + params.toString();
});

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    updateBulkActions();
});
</script>
{% endblock %}