{% extends "admin/base_admin.html" %}

{% block page_title %}编辑员工 - {{ object.name }}{% endblock %}
{% block page_description %}修改员工的基本信息和账号设置{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'organizations:admin:staff_detail' object.pk %}" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="eye" class="w-4 h-4"></i>
        <span>查看详情</span>
    </a>
    <a href="{% url 'organizations:admin:staff_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <i data-lucide="user" class="w-6 h-6 text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">编辑员工信息</h3>
                    <p class="mt-1 text-sm text-gray-600">
                        修改 {{ object.name }} 的基本信息和账号设置
                    </p>
                </div>
            </div>
        </div>

        <form method="post" class="p-6 space-y-6" id="staffForm">
            {% csrf_token %}
            
            <!-- 基本信息 -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">基本信息</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 员工编号 -->
                    <div>
                        <label for="id_employee_no" class="block text-sm font-medium text-gray-700 mb-2">
                            员工编号 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="employee_no" id="id_employee_no" required
                               value="{{ object.employee_no }}"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入员工编号">
                        <p class="mt-1 text-sm text-gray-500">唯一的员工标识编号</p>
                        {% if form.employee_no.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.employee_no.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 员工姓名 -->
                    <div>
                        <label for="id_name" class="block text-sm font-medium text-gray-700 mb-2">
                            员工姓名 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="id_name" required
                               value="{{ object.name }}"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入员工姓名">
                        {% if form.name.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 所属部门 -->
                    <div>
                        <label for="id_department" class="block text-sm font-medium text-gray-700 mb-2">
                            所属部门 <span class="text-red-500">*</span>
                        </label>
                        <select name="department" id="id_department" required
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择部门</option>
                            {% for dept in form.department.field.queryset %}
                            <option value="{{ dept.id }}" {% if dept.id == object.department.id %}selected{% endif %}>
                                {{ dept.name }}
                            </option>
                            {% endfor %}
                        </select>
                        {% if form.department.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.department.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 职位 -->
                    <div>
                        <label for="id_position" class="block text-sm font-medium text-gray-700 mb-2">
                            职位
                        </label>
                        <select name="position" id="id_position"
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择职位</option>
                            {% for pos in form.position.field.queryset %}
                            <option value="{{ pos.id }}" 
                                    data-department="{{ pos.department.id }}" 
                                    data-level="{{ pos.level }}"
                                    {% if object.position and pos.id == object.position.id %}selected{% endif %}>
                                {{ pos.name }} ({{ pos.level }}级)
                            </option>
                            {% endfor %}
                        </select>
                        <p class="mt-1 text-sm text-gray-500">职位将根据选择的部门进行筛选。不选择职位的员工视为普通员工（1-4级）</p>
                        {% if form.position.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.position.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 邮箱 -->
                    <div>
                        <label for="id_email" class="block text-sm font-medium text-gray-700 mb-2">
                            邮箱地址
                        </label>
                        <input type="email" name="email" id="id_email"
                               value="{{ object.email }}"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入邮箱地址">
                        {% if form.email.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 手机号 -->
                    <div>
                        <label for="id_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            手机号码
                        </label>
                        <input type="tel" name="phone" id="id_phone"
                               value="{{ object.phone }}"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入手机号码">
                        {% if form.phone.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.phone.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 账号设置 -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">账号设置</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 登录用户名 -->
                    <div>
                        <label for="id_username" class="block text-sm font-medium text-gray-700 mb-2">
                            登录用户名 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="username" id="id_username" required
                               value="{{ object.username }}"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入登录用户名">
                        <p class="mt-1 text-sm text-gray-500">用于管理端登录的用户名，必须唯一</p>
                        {% if form.username.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.username.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 匿名编号 -->
                    <div>
                        <label for="id_anonymous_code" class="block text-sm font-medium text-gray-700 mb-2">
                            匿名编号 <span class="text-red-500">*</span>
                        </label>
                        
                        <!-- 当前使用的编号显示 -->
                        {% if object.new_anonymous_code %}
                            <div class="mb-3 p-3 bg-green-50 border border-green-200 rounded-md">
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm font-medium text-green-800">当前安全编号：</span>
                                    <span class="font-mono text-green-900">{{ object.new_anonymous_code }}</span>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">安全</span>
                                </div>
                                <div class="text-xs text-green-600 mt-1">使用SHA256安全加密算法生成</div>
                            </div>
                        {% elif object.anonymous_code %}
                            <div class="mb-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm font-medium text-yellow-800">当前编号：</span>
                                    <span class="font-mono text-yellow-900">{{ object.anonymous_code }}</span>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">待升级</span>
                                </div>
                                <div class="text-xs text-yellow-600 mt-1">建议升级到安全编号</div>
                            </div>
                        {% endif %}
                        
                        <div class="flex">
                            <input type="text" name="anonymous_code" id="id_anonymous_code" required
                                   value="{% if object.new_anonymous_code %}{{ object.new_anonymous_code }}{% else %}{{ object.anonymous_code }}{% endif %}"
                                   class="flex-1 border border-gray-300 rounded-l-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono">
                            <button type="button" onclick="generateAnonymousCode()" 
                                    class="px-3 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 flex items-center">
                                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">用于匿名评分的唯一编号，点击刷新按钮重新生成安全编号</p>
                        {% if form.anonymous_code.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.anonymous_code.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 用户角色 -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            用户角色 <span class="text-red-500">*</span>
                        </label>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                            {% for value, label in form.role.field.choices %}
                            <label class="flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="role" value="{{ value }}" 
                                       {% if value == object.role %}checked{% endif %}
                                       class="form-radio">
                                <span class="ml-2 text-sm text-gray-900">{{ label }}</span>
                            </label>
                            {% endfor %}
                        </div>
                        {% if form.role.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.role.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 密码修改 -->
            <div class="border-b border-gray-200 pb-6">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-md font-medium text-gray-900">密码设置</h4>
                    <button type="button" onclick="togglePasswordSection()" id="togglePasswordBtn"
                            class="text-sm text-blue-600 hover:text-blue-800">
                        修改密码
                    </button>
                </div>
                
                <div id="passwordSection" class="hidden">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                        <div class="flex">
                            <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-400 mr-2"></i>
                            <div class="text-sm text-yellow-700">
                                <p class="font-medium">密码修改提醒</p>
                                <p>如果不需要修改密码，请保持密码字段为空。只有在需要重置密码时才填写新密码。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="id_new_password" class="block text-sm font-medium text-gray-700 mb-2">
                                新密码
                            </label>
                            <div class="relative">
                                <input type="password" name="new_password" id="id_new_password"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="留空表示不修改密码">
                                <button type="button" onclick="togglePassword('id_new_password')" 
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <i data-lucide="eye" class="w-4 h-4 text-gray-400"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div>
                            <label for="id_confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                                确认新密码
                            </label>
                            <div class="relative">
                                <input type="password" name="confirm_password" id="id_confirm_password"
                                       class="w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="再次输入新密码">
                                <button type="button" onclick="togglePassword('id_confirm_password')" 
                                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <i data-lucide="eye" class="w-4 h-4 text-gray-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他设置 -->
            <div>
                <h4 class="text-md font-medium text-gray-900 mb-4">其他设置</h4>
                
                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" name="is_active" id="id_is_active" 
                               {% if object.is_active %}checked{% endif %}
                               class="checkbox">
                        <label for="id_is_active" class="ml-2 text-sm text-gray-900">
                            启用账号
                        </label>
                    </div>
                    <p class="text-sm text-gray-500">禁用后该员工将无法登录系统</p>
                    
                    <!-- 显示基本统计信息 -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h5 class="text-sm font-medium text-gray-900 mb-2">账号信息</h5>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">创建时间：</span>
                                <span class="text-gray-900">{{ object.created_at|date:"Y-m-d" }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">最后登录：</span>
                                <span class="text-gray-900">
                                    {% if object.last_login %}{{ object.last_login|date:"Y-m-d H:i" }}{% else %}从未登录{% endif %}
                                </span>
                            </div>
                            <div>
                                <span class="text-gray-500">参与考评：</span>
                                <span class="text-gray-900">{{ evaluator_relations|length }} 次</span>
                            </div>
                            <div>
                                <span class="text-gray-500">被评价：</span>
                                <span class="text-gray-900">{{ evaluatee_relations|length }} 次</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 表单操作 -->
            <div class="border-t border-gray-200 pt-6">
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'organizations:admin:staff_detail' object.pk %}" 
                       class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </a>
                    <button type="submit" id="submitBtn"
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i data-lucide="save" class="w-4 h-4 inline mr-1"></i>
                        保存修改
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 部门联动职位筛选
    const departmentSelect = document.getElementById('id_department');
    const positionSelect = document.getElementById('id_position');
    const allPositions = Array.from(positionSelect.options).slice(1); // 保留第一个空选项
    
    departmentSelect.addEventListener('change', function() {
        const selectedDeptId = this.value;
        const currentPositionId = {{ object.position.id|default:'null' }};
        
        // 清空并重新添加第一个空选项
        positionSelect.innerHTML = '<option value="">请选择职位</option>';
        
        if (selectedDeptId) {
            // 筛选指定部门的职位
            allPositions.forEach(option => {
                if (option.dataset.department === selectedDeptId) {
                    const newOption = option.cloneNode(true);
                    if (currentPositionId && parseInt(option.value) === currentPositionId) {
                        newOption.selected = true;
                    }
                    positionSelect.appendChild(newOption);
                }
            });
        } else {
            // 显示所有职位
            allPositions.forEach(option => {
                const newOption = option.cloneNode(true);
                if (currentPositionId && parseInt(option.value) === currentPositionId) {
                    newOption.selected = true;
                }
                positionSelect.appendChild(newOption);
            });
        }
    });
    
    // 初始化时触发部门变化事件
    departmentSelect.dispatchEvent(new Event('change'));
});

// 生成安全匿名编号
function generateAnonymousCode() {
    const anonymousCodeInput = document.getElementById('id_anonymous_code');
    const refreshButton = document.querySelector('button[onclick="generateAnonymousCode()"]');
    const refreshIcon = refreshButton.querySelector('i');
    
    // 获取当前员工ID（用于编辑时重新生成）
    const currentUrl = window.location.pathname;
    const staffIdMatch = currentUrl.match(/\/staff\/(\d+)\/update\//);
    const staffId = staffIdMatch ? staffIdMatch[1] : null;
    
    // 禁用按钮并显示加载状态
    refreshButton.disabled = true;
    refreshIcon.classList.add('animate-spin');
    
    // 获取CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    
    // 发送API请求
    fetch('{% url "organizations:admin:generate_anonymous_code" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
        },
        body: JSON.stringify({
            staff_id: staffId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            anonymousCodeInput.value = data.anonymous_code;
            showNotification(data.message || '安全编号生成成功', 'success');
            
            // 如果是编辑页面，刷新页面显示更新
            if (staffId) {
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        } else {
            showNotification(data.error || '生成编号失败', 'error');
        }
    })
    .catch(error => {
        console.error('生成匿名编号失败:', error);
        showNotification('生成编号失败，请重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        refreshButton.disabled = false;
        refreshIcon.classList.remove('animate-spin');
    });
}

// 切换密码显示
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = input.nextElementSibling.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.setAttribute('data-lucide', 'eye-off');
        lucide.createIcons();
    } else {
        input.type = 'password';
        icon.setAttribute('data-lucide', 'eye');
        lucide.createIcons();
    }
}

// 切换密码修改区域
function togglePasswordSection() {
    const passwordSection = document.getElementById('passwordSection');
    const toggleBtn = document.getElementById('togglePasswordBtn');
    
    if (passwordSection.classList.contains('hidden')) {
        passwordSection.classList.remove('hidden');
        toggleBtn.textContent = '取消修改';
    } else {
        passwordSection.classList.add('hidden');
        toggleBtn.textContent = '修改密码';
        // 清空密码字段
        document.getElementById('id_new_password').value = '';
        document.getElementById('id_confirm_password').value = '';
    }
}

// 表单提交处理
document.getElementById('staffForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    
    // 密码确认验证
    const newPassword = document.getElementById('id_new_password').value;
    const confirmPassword = document.getElementById('id_confirm_password').value;
    
    if (newPassword && newPassword !== confirmPassword) {
        e.preventDefault();
        UI.showMessage('两次输入的密码不一致', 'error');
        return;
    }
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 inline mr-1 animate-spin"></i>正在保存...';
});

// 通知功能
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-4 py-3 rounded-md text-white z-50 shadow-lg transform transition-all duration-300 ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
    }`;
    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <i data-lucide="${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : type === 'warning' ? 'alert-triangle' : 'info'}" class="w-4 h-4"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    lucide.createIcons();
    
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
</script>
{% endblock %}