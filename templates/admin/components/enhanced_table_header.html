{% comment %}
增强型表格头部组件
包含改进的多选框和批量操作功能
使用方法：
{% include "admin/components/enhanced_table_header.html" with batch_actions=batch_actions selected_text="已选择" %}
{% endcomment %}

<!-- 表格头部批量操作区域 -->
<div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <!-- 增强型全选多选框 -->
            <label class="flex items-center cursor-pointer hover:bg-gray-100 px-3 py-2 rounded-lg transition-all duration-200 group">
                <input type="checkbox" 
                       id="selectAll" 
                       class="checkbox-table" 
                       data-select-all
                       title="全选/取消全选 (Ctrl+A)">
                <span class="ml-3 text-sm text-gray-700 font-medium group-hover:text-gray-900">
                    全选
                </span>
                <span class="ml-2 text-xs text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity">
                    Ctrl+A
                </span>
            </label>
            
            <!-- 选择计数器 -->
            <div class="flex items-center space-x-2">
                <span id="selectedCount" 
                      class="text-sm text-gray-500" 
                      data-selection-counter>
                    {{ selected_text|default:"已选择" }} 0 项
                </span>
                
                <!-- 选择状态指示器 -->
                <div id="selectionIndicator" class="hidden">
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        <span class="text-xs text-blue-600">批量操作模式</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 批量操作按钮组 -->
        <div id="batchActionsContainer" 
             class="flex items-center space-x-2 opacity-0 transform translate-x-4 transition-all duration-300"
             data-batch-actions>
            
            {% if batch_actions %}
                {% for action in batch_actions %}
                <button type="button"
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                        onclick="{{ action.onclick }}"
                        title="{{ action.title }}">
                    {% if action.icon %}
                        <i data-lucide="{{ action.icon }}" class="w-4 h-4 mr-2"></i>
                    {% endif %}
                    {{ action.label }}
                </button>
                {% endfor %}
            {% else %}
                <!-- 默认批量操作 -->
                <button type="button"
                        id="batchDeleteBtn"
                        class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200"
                        onclick="showBatchDeleteConfirm()"
                        title="删除选中项目">
                    <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                    批量删除
                </button>
                
                <button type="button"
                        id="batchExportBtn"
                        class="inline-flex items-center px-3 py-2 border border-green-300 shadow-sm text-sm leading-4 font-medium rounded-md text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
                        onclick="exportSelected()"
                        title="导出选中项目">
                    <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                    批量导出
                </button>
            {% endif %}
            
            <!-- 清除选择按钮 -->
            <button type="button"
                    id="clearSelectionBtn"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
                    onclick="clearAllSelections()"
                    title="清除所有选择 (Esc)">
                <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                清除选择
            </button>
        </div>
    </div>
    
    <!-- 快捷操作提示 -->
    <div id="shortcutHints" class="mt-3 text-xs text-gray-500 opacity-0 transition-opacity duration-300">
        <div class="flex items-center space-x-4">
            <span><kbd class="px-1 py-0.5 bg-gray-200 rounded">Ctrl+A</kbd> 全选</span>
            <span><kbd class="px-1 py-0.5 bg-gray-200 rounded">Shift+Click</kbd> 范围选择</span>
            <span><kbd class="px-1 py-0.5 bg-gray-200 rounded">Esc</kbd> 清除选择</span>
        </div>
    </div>
</div>

<style>
/* 批量操作动画效果 */
.batch-actions-visible {
    opacity: 1 !important;
    transform: translateX(0) !important;
}

.selection-active #selectionIndicator {
    display: flex !important;
}

.selection-active #shortcutHints {
    opacity: 1 !important;
}

/* 键盘快捷键样式 */
kbd {
    font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 0.75rem;
    font-weight: 600;
}
</style>

<script>
// 增强型表格头部交互
document.addEventListener('DOMContentLoaded', function() {
    const batchActionsContainer = document.getElementById('batchActionsContainer');
    const selectionIndicator = document.getElementById('selectionIndicator');
    const shortcutHints = document.getElementById('shortcutHints');
    const selectedCount = document.getElementById('selectedCount');
    
    // 监听选择变化事件
    document.addEventListener('selectionChanged', function(e) {
        const count = e.detail.count;
        const selectedText = "{{ selected_text|default:'已选择' }}";
        
        // 更新计数显示
        selectedCount.textContent = `${selectedText} ${count} 项`;
        
        // 显示/隐藏批量操作
        if (count > 0) {
            batchActionsContainer.classList.add('batch-actions-visible');
            document.body.classList.add('selection-active');
        } else {
            batchActionsContainer.classList.remove('batch-actions-visible');
            document.body.classList.remove('selection-active');
        }
    });
    
    // 清除所有选择
    window.clearAllSelections = function() {
        if (window.enhancedCheckbox) {
            window.enhancedCheckbox.clearSelection();
        }
    };
    
    // 默认批量删除确认
    window.showBatchDeleteConfirm = function() {
        if (!window.enhancedCheckbox) return;
        
        const selectedItems = window.enhancedCheckbox.getSelectedItems();
        if (selectedItems.length === 0) {
            alert('请先选择要删除的项目');
            return;
        }
        
        if (confirm(`确定要删除选中的 ${selectedItems.length} 个项目吗？此操作不可撤销。`)) {
            // 这里应该调用实际的删除函数
            console.log('批量删除:', selectedItems);
            // batchDelete(selectedItems);
        }
    };
    
    // 默认批量导出
    window.exportSelected = function() {
        if (!window.enhancedCheckbox) return;
        
        const selectedItems = window.enhancedCheckbox.getSelectedItems();
        if (selectedItems.length === 0) {
            alert('请先选择要导出的项目');
            return;
        }
        
        // 这里应该调用实际的导出函数
        console.log('批量导出:', selectedItems);
        // batchExport(selectedItems);
    };
});
</script>
