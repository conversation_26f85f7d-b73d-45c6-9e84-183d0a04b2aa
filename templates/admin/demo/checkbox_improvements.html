{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}多选框改进演示{% endblock %}
{% block page_description %}展示新的多选框设计和交互功能{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <span class="text-sm text-gray-600">演示页面</span>
    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
</div>
{% endblock %}

{% block admin_content %}
<!-- 改进对比展示 -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- 原有设计 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">原有设计</h3>
            <p class="text-sm text-gray-600 mt-1">18px × 18px 多选框，点击区域有限</p>
        </div>
        <div class="p-6">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left py-2">
                            <input type="checkbox" class="checkbox" style="width: 18px; height: 18px;">
                        </th>
                        <th class="text-left py-2 text-sm font-medium text-gray-700">项目名称</th>
                        <th class="text-left py-2 text-sm font-medium text-gray-700">状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="border-b border-gray-100">
                        <td class="py-3">
                            <input type="checkbox" class="checkbox" style="width: 18px; height: 18px;" value="old-1">
                        </td>
                        <td class="py-3 text-sm text-gray-900">示例项目 1</td>
                        <td class="py-3 text-sm text-green-600">活跃</td>
                    </tr>
                    <tr class="border-b border-gray-100">
                        <td class="py-3">
                            <input type="checkbox" class="checkbox" style="width: 18px; height: 18px;" value="old-2">
                        </td>
                        <td class="py-3 text-sm text-gray-900">示例项目 2</td>
                        <td class="py-3 text-sm text-gray-500">待审核</td>
                    </tr>
                    <tr>
                        <td class="py-3">
                            <input type="checkbox" class="checkbox" style="width: 18px; height: 18px;" value="old-3">
                        </td>
                        <td class="py-3 text-sm text-gray-900">示例项目 3</td>
                        <td class="py-3 text-sm text-red-600">已禁用</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 改进设计 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">改进设计</h3>
            <p class="text-sm text-gray-600 mt-1">22px × 22px 多选框，扩展点击区域，增强交互</p>
        </div>
        <div class="p-6">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-center py-2">
                            <input type="checkbox" class="checkbox-table" id="demoSelectAll">
                        </th>
                        <th class="text-left py-2 text-sm font-medium text-gray-700">项目名称</th>
                        <th class="text-left py-2 text-sm font-medium text-gray-700">状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="border-b border-gray-100">
                        <td class="py-3 checkbox-cell text-center">
                            <input type="checkbox" class="checkbox-table demo-checkbox" value="new-1" data-value="new-1">
                        </td>
                        <td class="py-3 text-sm text-gray-900">示例项目 1</td>
                        <td class="py-3 text-sm text-green-600">活跃</td>
                    </tr>
                    <tr class="border-b border-gray-100">
                        <td class="py-3 checkbox-cell text-center">
                            <input type="checkbox" class="checkbox-table demo-checkbox" value="new-2" data-value="new-2">
                        </td>
                        <td class="py-3 text-sm text-gray-900">示例项目 2</td>
                        <td class="py-3 text-sm text-gray-500">待审核</td>
                    </tr>
                    <tr>
                        <td class="py-3 checkbox-cell text-center">
                            <input type="checkbox" class="checkbox-table demo-checkbox" value="new-3" data-value="new-3">
                        </td>
                        <td class="py-3 text-sm text-gray-900">示例项目 3</td>
                        <td class="py-3 text-sm text-red-600">已禁用</td>
                    </tr>
                </tbody>
            </table>
            
            <!-- 演示控制面板 -->
            <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center justify-between">
                    <span id="demoCounter" class="text-sm text-gray-600">已选择 0 项</span>
                    <div class="flex items-center space-x-2">
                        <button onclick="demoSelectAll()" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">
                            全选
                        </button>
                        <button onclick="demoClearAll()" class="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">
                            清除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 功能特性展示 -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">功能特性对比</h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 尺寸对比 -->
            <div class="text-center">
                <h4 class="font-medium text-gray-900 mb-4">尺寸对比</h4>
                <div class="space-y-4">
                    <div class="flex items-center justify-center space-x-4">
                        <span class="text-sm text-gray-600">原有:</span>
                        <input type="checkbox" class="checkbox" style="width: 18px; height: 18px;">
                        <span class="text-xs text-gray-500">18px</span>
                    </div>
                    <div class="flex items-center justify-center space-x-4">
                        <span class="text-sm text-gray-600">改进:</span>
                        <input type="checkbox" class="checkbox-table">
                        <span class="text-xs text-gray-500">22px</span>
                    </div>
                    <div class="flex items-center justify-center space-x-4">
                        <span class="text-sm text-gray-600">大尺寸:</span>
                        <input type="checkbox" class="checkbox-lg">
                        <span class="text-xs text-gray-500">24px</span>
                    </div>
                    <div class="flex items-center justify-center space-x-4">
                        <span class="text-sm text-gray-600">超大:</span>
                        <input type="checkbox" class="checkbox-xl">
                        <span class="text-xs text-gray-500">28px</span>
                    </div>
                </div>
            </div>

            <!-- 交互功能 -->
            <div>
                <h4 class="font-medium text-gray-900 mb-4">交互功能</h4>
                <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        扩展点击区域
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        键盘快捷键支持
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        Shift 批量选择
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        行高亮反馈
                    </li>
                    <li class="flex items-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                        悬停动画效果
                    </li>
                </ul>
            </div>

            <!-- 快捷键说明 -->
            <div>
                <h4 class="font-medium text-gray-900 mb-4">快捷键</h4>
                <div class="space-y-2 text-sm">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">全选</span>
                        <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Ctrl+A</kbd>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">清除选择</span>
                        <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Esc</kbd>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">范围选择</span>
                        <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Shift+Click</kbd>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 使用指南 -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">使用指南</h3>
    </div>
    <div class="p-6">
        <div class="prose max-w-none">
            <h4>1. 更新现有模板</h4>
            <p class="text-gray-600">将现有的多选框替换为新的样式类：</p>
            <pre class="bg-gray-100 p-4 rounded-lg text-sm"><code>&lt;td class="checkbox-cell"&gt;
    &lt;input type="checkbox" class="checkbox-table" value="item-id"&gt;
&lt;/td&gt;</code></pre>

            <h4 class="mt-6">2. 引入增强脚本</h4>
            <p class="text-gray-600">在模板底部添加脚本引用：</p>
            <pre class="bg-gray-100 p-4 rounded-lg text-sm"><code>&lt;script src="{% static 'js/enhanced-checkbox.js' %}"&gt;&lt;/script&gt;</code></pre>

            <h4 class="mt-6">3. 使用增强型表格头部</h4>
            <p class="text-gray-600">替换现有的表格头部：</p>
            <pre class="bg-gray-100 p-4 rounded-lg text-sm"><code>{% include "admin/components/enhanced_table_header.html" %}</code></pre>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/enhanced-checkbox.js' %}"></script>
<script>
// 演示页面专用脚本
document.addEventListener('DOMContentLoaded', function() {
    // 初始化演示区域的多选框
    const demoCheckboxes = document.querySelectorAll('.demo-checkbox');
    const demoSelectAllCheckbox = document.getElementById('demoSelectAll');
    const demoCounter = document.getElementById('demoCounter');
    
    // 更新计数器
    function updateDemoCounter() {
        const checkedCount = document.querySelectorAll('.demo-checkbox:checked').length;
        demoCounter.textContent = `已选择 ${checkedCount} 项`;
        
        // 更新全选状态
        if (checkedCount === 0) {
            demoSelectAllCheckbox.checked = false;
            demoSelectAllCheckbox.indeterminate = false;
        } else if (checkedCount === demoCheckboxes.length) {
            demoSelectAllCheckbox.checked = true;
            demoSelectAllCheckbox.indeterminate = false;
        } else {
            demoSelectAllCheckbox.checked = false;
            demoSelectAllCheckbox.indeterminate = true;
        }
    }
    
    // 绑定事件
    demoCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateDemoCounter);
    });
    
    demoSelectAllCheckbox.addEventListener('change', function() {
        demoCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateDemoCounter();
    });
    
    // 全选函数
    window.demoSelectAll = function() {
        demoCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
        updateDemoCounter();
    };
    
    // 清除函数
    window.demoClearAll = function() {
        demoCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        updateDemoCounter();
    };
    
    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>
{% endblock %}
