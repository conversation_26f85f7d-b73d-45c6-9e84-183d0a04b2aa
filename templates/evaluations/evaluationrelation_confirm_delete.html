{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}删除评价关系{% endblock %}

{% block breadcrumb %}
<a href="{% url 'organizations:admin:dashboard' %}" class="text-blue-600 hover:text-blue-800">首页</a>
<i data-lucide="chevron-right" class="w-4 h-4 breadcrumb-separator"></i>
<a href="{% url 'evaluations:admin:batch_list' %}" class="text-blue-600 hover:text-blue-800">批次管理</a>
<i data-lucide="chevron-right" class="w-4 h-4 breadcrumb-separator"></i>
<span class="text-gray-900">删除评价关系</span>
{% endblock %}

{% block admin_content %}
<div class="max-w-md mx-auto mt-12">
    <div class="card">
        <div class="card-body text-center">
            <div class="mb-4">
                <i data-lucide="alert-triangle" class="w-16 h-16 text-red-500 mx-auto"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">确认删除</h3>
            <p class="text-gray-600 mb-2">您确定要删除评价关系</p>
            <p class="text-lg font-semibold text-gray-900 mb-2">"{{ object.evaluator.name }} → {{ object.evaluatee.name }}"</p>
            <p class="text-sm text-gray-500 mb-6">此操作会影响相关的评价结果</p>
            
            <form method="post" class="inline">
                {% csrf_token %}
                <div class="flex justify-center space-x-4">
                    <a href="{% url 'evaluations:admin:batch_list' %}" class="btn btn-secondary btn-md">取消</a>
                    <button type="submit" class="btn btn-danger btn-md">
                        <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                        确认删除
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    if (window.lucide) {
        lucide.createIcons({ nameAttr: 'data-lucide' });
    }
});
</script>
{% endblock %}