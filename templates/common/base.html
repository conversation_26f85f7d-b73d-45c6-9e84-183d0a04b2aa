{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}考评系统{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="{% static 'lib/lucide.js' %}"></script>
    
    <!-- ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    
    <!-- Sortable.js -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">

    <!-- 时间显示美化样式 -->
    <link rel="stylesheet" href="{% static 'css/time-display-enhancement.css' %}">

    <!-- Custom JavaScript Libraries -->
    <script src="{% static 'js/common.js' %}"></script>
    <script src="{% static 'js/charts.js' %}"></script>
    <script src="{% static 'js/jwt-auth.js' %}"></script>
    <script src="{% static 'js/time-display-enhancement.js' %}"></script>
    
    <style>
        .sidebar-collapsed {
            width: 4rem !important;
            position: fixed !important;
            left: 0 !important;
            top: 0 !important;
            height: 100vh !important;
            z-index: 50 !important;
        }
        .sidebar-expanded {
            width: 16rem !important;
            position: fixed !important;
            left: 0 !important;
            top: 0 !important;
            height: 100vh !important;
            z-index: 50 !important;
        }
        .sidebar-transition {
            transition: width 0.3s ease-in-out;
        }
        .content-collapsed {
            margin-left: 4rem !important;
            width: calc(100% - 4rem) !important;
            min-height: 100vh !important;
            box-sizing: border-box !important;
            padding: 0 !important;
            background-color: #f8fafc !important;
            display: flex !important;
            flex-direction: column !important;
        }
        .content-expanded {
            margin-left: 16rem !important;
            width: calc(100% - 16rem) !important;
            min-height: 100vh !important;
            box-sizing: border-box !important;
            padding: 0 !important;
            background-color: #f8fafc !important;
            display: flex !important;
            flex-direction: column !important;
        }
        .content-transition {
            transition: margin-left 0.3s ease-in-out, width 0.3s ease-in-out;
        }
        .dropdown-menu {
            display: none;
        }
        .dropdown-menu.show {
            display: block;
        }
        
        /* 头部组件关键样式 */
        .main-header {
            margin: 0 !important;
            padding: 2rem 1.5rem !important;
            width: 100% !important;
            min-height: 100px !important;
            box-sizing: border-box !important;
            background-color: #ffffff !important;
            border-bottom: 1px solid #e5e7eb !important;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
            position: sticky;
            top: 0;
            z-index: 10;
            display: flex !important;
            align-items: center !important;
        }
        
        .main-content {
            margin: 0 !important;
            padding: 2rem 1.5rem 1.5rem 1.5rem !important;
            width: 100% !important;
            box-sizing: border-box !important;
            flex: 1;
            overflow-y: auto;
            background-color: #f8fafc !important;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50">
    <!-- 消息提示区域 -->
    {% if messages %}
        <div id="messages" class="fixed top-4 right-4 z-50 space-y-2">
            {% for message in messages %}
                <div class="px-4 py-3 rounded-md text-white shadow-lg transform transition-all duration-300 
                           {% if message.tags == 'success' %}bg-green-500{% elif message.tags == 'error' %}bg-red-500{% elif message.tags == 'warning' %}bg-yellow-500{% else %}bg-blue-500{% endif %}">
                    <div class="flex items-center space-x-2">
                        <i data-lucide="{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}x-circle{% elif message.tags == 'warning' %}alert-triangle{% else %}info{% endif %}" class="w-4 h-4"></i>
                        <span>{{ message }}</span>
                        <button type="button" class="ml-2 hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                            <i data-lucide="x" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="flex h-screen">
        {% block sidebar %}{% endblock %}
        
        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col overflow-hidden">
            {% block header %}{% endblock %}
            
            <!-- 页面内容 -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- 模态框区域 -->
    <div id="modal-container"></div>

    <script>
        // 初始化图标和消息
        document.addEventListener('DOMContentLoaded', function() {
            // 确保 lucide 已加载
            if (typeof lucide !== 'undefined' && lucide.createIcons) {
                lucide.createIcons();
            } else {
                console.warn('Lucide图标库未正确加载');
            }
            
            // 消息自动隐藏
            const messages = document.getElementById('messages');
            if (messages) {
                setTimeout(() => {
                    messages.style.opacity = '0';
                    setTimeout(() => messages.remove(), 300);
                }, 5000);
            }
        });

        // 通知功能
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 px-4 py-3 rounded-md text-white z-50 shadow-lg transform transition-all duration-300 ${
                type === 'success' ? 'bg-green-500' : 
                type === 'error' ? 'bg-red-500' : 
                type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
            }`;
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <i data-lucide="${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : type === 'warning' ? 'alert-triangle' : 'info'}" class="w-4 h-4"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            // 确保 lucide 已加载再创建图标
            if (typeof lucide !== 'undefined' && lucide.createIcons) {
                lucide.createIcons();
            }
            
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }

        // 下拉菜单功能
        function toggleDropdown(button) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== button.nextElementSibling) {
                    menu.classList.add('hidden');
                    menu.classList.remove('show');
                }
            });
            
            const menu = button.nextElementSibling;
            menu.classList.toggle('hidden');
            menu.classList.toggle('show');
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.relative')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.add('hidden');
                    menu.classList.remove('show');
                });
            }
        });

        // 获取Cookie函数
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>