{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JWT功能测试 - 企业考评系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-2xl">
        <h1 class="text-2xl font-bold text-center mb-6">JWT功能测试</h1>
        
        <!-- 测试工具 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            
            <!-- 登录测试 -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-semibold mb-3">登录测试</h3>
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">用户名</label>
                        <input type="text" id="test-username" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" value="admin">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">密码</label>
                        <input type="password" id="test-password" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" value="123456">
                    </div>
                    <button onclick="testLogin()" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                        测试登录
                    </button>
                </div>
                <div id="login-result" class="mt-3 text-sm"></div>
            </div>
            
            <!-- Token状态测试 -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="font-semibold mb-3">Token状态</h3>
                <div class="space-y-2">
                    <button onclick="checkTokenStatus()" class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700">
                        检查Token状态
                    </button>
                    <button onclick="refreshToken()" class="w-full bg-yellow-600 text-white py-2 px-4 rounded-md hover:bg-yellow-700">
                        刷新Token
                    </button>
                    <button onclick="testLogout()" class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700">
                        测试退出
                    </button>
                </div>
                <div id="token-result" class="mt-3 text-sm"></div>
            </div>
            
            <!-- 存储信息 -->
            <div class="border border-gray-200 rounded-lg p-4 md:col-span-2">
                <h3 class="font-semibold mb-3">存储信息</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span>Access Token:</span>
                        <span id="access-token-status" class="text-gray-600">未存储</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Refresh Token:</span>
                        <span id="refresh-token-status" class="text-gray-600">未存储</span>
                    </div>
                    <div class="flex justify-between">
                        <span>用户信息:</span>
                        <span id="user-info-status" class="text-gray-600">未存储</span>
                    </div>
                </div>
                <button onclick="clearStorage()" class="mt-3 w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700">
                    清理存储
                </button>
            </div>
            
            <!-- 调试信息 -->
            <div class="border border-gray-200 rounded-lg p-4 md:col-span-2">
                <h3 class="font-semibold mb-3">调试信息</h3>
                <pre id="debug-info" class="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40"></pre>
                <button onclick="updateDebugInfo()" class="mt-2 text-sm text-blue-600 hover:text-blue-800">
                    更新调试信息
                </button>
            </div>
            
        </div>
        
        <!-- 快速链接 -->
        <div class="mt-6 text-center">
            <a href="/admin/login/" class="text-blue-600 hover:text-blue-800">前往登录页</a>
            <span class="mx-2 text-gray-400">|</span>
            <a href="/admin/" class="text-green-600 hover:text-green-800">前往管理后台</a>
        </div>
    </div>

    <script src="{% static 'js/jwt-auth.js' %}"></script>
    
    <script>
        // 更新存储信息显示
        function updateStorageDisplay() {
            const accessToken = localStorage.getItem('access_token');
            const refreshToken = localStorage.getItem('refresh_token');
            const userInfo = localStorage.getItem('user_info');
            
            document.getElementById('access-token-status').textContent = 
                accessToken ? '已存储 (' + accessToken.substring(0, 20) + '...)' : '未存储';
            document.getElementById('refresh-token-status').textContent = 
                refreshToken ? '已存储' : '未存储';
            document.getElementById('user-info-status').textContent = 
                userInfo ? '已存储' : '未存储';
        }

        // 测试登录
        async function testLogin() {
            const username = document.getElementById('test-username').value;
            const password = document.getElementById('test-password').value;
            
            try {
                const result = await authManager.login(username, password);
                const resultDiv = document.getElementById('login-result');
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="text-green-600">登录成功！欢迎 ${result.user.name}</div>
                    `;
                    updateStorageDisplay();
                    updateDebugInfo();
                } else {
                    resultDiv.innerHTML = `
                        <div class="text-red-600">登录失败：${result.message}</div>
                    `;
                }
            } catch (error) {
                document.getElementById('login-result').innerHTML = `
                    <div class="text-red-600">错误：${error.message}</div>
                `;
            }
        }

        // 检查Token状态
        async function checkTokenStatus() {
            try {
                const isValid = await authManager.validateToken();
                const resultDiv = document.getElementById('token-result');
                
                if (isValid) {
                    resultDiv.innerHTML = `
                        <div class="text-green-600">Token有效</div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="text-red-600">Token无效或已过期</div>
                    `;
                }
            } catch (error) {
                document.getElementById('token-result').innerHTML = `
                    <div class="text-red-600">检查失败：${error.message}</div>
                `;
            }
        }

        // 刷新Token
        async function refreshToken() {
            try {
                const refreshed = await authManager.refreshAccessToken();
                const resultDiv = document.getElementById('token-result');
                
                if (refreshed) {
                    resultDiv.innerHTML = `
                        <div class="text-green-600">Token刷新成功</div>
                    `;
                    updateStorageDisplay();
                } else {
                    resultDiv.innerHTML = `
                        <div class="text-red-600">Token刷新失败</div>
                    `;
                }
            } catch (error) {
                document.getElementById('token-result').innerHTML = `
                    <div class="text-red-600">刷新失败：${error.message}</div>
                `;
            }
        }

        // 测试退出
        async function testLogout() {
            try {
                await authManager.logout();
                updateStorageDisplay();
                updateDebugInfo();
                document.getElementById('token-result').innerHTML = `
                    <div class="text-green-600">退出成功</div>
                `;
            } catch (error) {
                document.getElementById('token-result').innerHTML = `
                    <div class="text-red-600">退出失败：${error.message}</div>
                `;
            }
        }

        // 清理存储
        function clearStorage() {
            authManager.clearTokens();
            updateStorageDisplay();
            updateDebugInfo();
        }

        // 更新调试信息
        function updateDebugInfo() {
            const debugInfo = {
                accessToken: localStorage.getItem('access_token'),
                refreshToken: localStorage.getItem('refresh_token'),
                userInfo: localStorage.getItem('user_info'),
                currentURL: window.location.href,
                timestamp: new Date().toISOString()
            };
            
            document.getElementById('debug-info').textContent = JSON.stringify(debugInfo, null, 2);
        }

        // 页面加载时更新显示
        document.addEventListener('DOMContentLoaded', function() {
            updateStorageDisplay();
            updateDebugInfo();
        });

        // 每5秒自动更新显示
        setInterval(function() {
            updateStorageDisplay();
            updateDebugInfo();
        }, 5000);
    </script>
</body>
</html>