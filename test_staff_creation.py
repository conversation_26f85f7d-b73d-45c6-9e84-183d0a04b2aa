#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试员工创建功能
验证新建员工时的完整流程是否正常
"""

import os
import sys
import django
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from organizations.models import Staff, Department, Position
from organizations.views import StaffCreateView

def test_staff_creation_logic():
    """测试员工创建逻辑"""
    print("=" * 60)
    print("测试员工创建功能")
    print("=" * 60)
    
    # 测试1: 测试员工编号生成
    print("\n1. 测试员工编号生成")
    try:
        view = StaffCreateView()
        employee_no = view._generate_employee_no()
        print(f"   ✓ 生成员工编号: {employee_no}")
        print(f"   ✓ 编号格式: {'正确' if employee_no.startswith('EMP') and len(employee_no) == 15 else '错误'}")
        
        # 测试唯一性
        employee_no2 = view._generate_employee_no()
        print(f"   ✓ 编号唯一性: {'是' if employee_no != employee_no2 else '否'}")
        
    except Exception as e:
        print(f"   ✗ 员工编号生成失败: {e}")
        return False
    
    # 测试2: 测试旧匿名编号生成
    print("\n2. 测试旧匿名编号生成")
    try:
        view = StaffCreateView()
        anonymous_code = view._generate_old_anonymous_code()
        print(f"   ✓ 生成旧匿名编号: {anonymous_code}")
        print(f"   ✓ 编号格式: {'正确' if anonymous_code.startswith('A') and len(anonymous_code) == 8 else '错误'}")
        
        # 测试唯一性
        anonymous_code2 = view._generate_old_anonymous_code()
        print(f"   ✓ 编号唯一性: {'是' if anonymous_code != anonymous_code2 else '否'}")
        
    except Exception as e:
        print(f"   ✗ 旧匿名编号生成失败: {e}")
        return False
    
    # 测试3: 测试部门和职位数据
    print("\n3. 测试部门和职位数据")
    try:
        departments = Department.objects.filter(deleted_at__isnull=True, is_active=True)
        positions = Position.objects.filter(deleted_at__isnull=True, is_active=True)
        
        print(f"   ✓ 可用部门数量: {departments.count()}")
        print(f"   ✓ 可用职位数量: {positions.count()}")
        
        if departments.exists():
            dept = departments.first()
            print(f"   ✓ 示例部门: {dept.name} (编号: {dept.dept_code})")
        
        if positions.exists():
            pos = positions.first()
            print(f"   ✓ 示例职位: {pos.name} (级别: {pos.level})")
            
    except Exception as e:
        print(f"   ✗ 部门职位数据检查失败: {e}")
        return False
    
    # 测试4: 测试新安全匿名编号生成
    print("\n4. 测试新安全匿名编号生成")
    try:
        from common.security.anonymous import SecureAnonymousCodeGenerator
        
        generator = SecureAnonymousCodeGenerator()
        
        # 使用第一个部门进行测试
        dept = Department.objects.filter(deleted_at__isnull=True, is_active=True).first()
        if dept:
            # 模拟新员工ID
            test_staff_id = 999999
            new_code = generator.generate_secure_code(test_staff_id, dept.id)
            
            print(f"   ✓ 生成新安全编号: {new_code}")
            print(f"   ✓ 编号格式: {'正确' if generator.validate_code_format(new_code) else '错误'}")
            print(f"   ✓ 使用部门: {dept.name} (ID: {dept.id})")
        else:
            print("   ✗ 没有可用部门进行测试")
            
    except Exception as e:
        print(f"   ✗ 新安全匿名编号生成失败: {e}")
        return False
    
    # 测试5: 检查现有员工数据
    print("\n5. 检查现有员工数据")
    try:
        total_staff = Staff.objects.filter(deleted_at__isnull=True).count()
        active_staff = Staff.objects.filter(deleted_at__isnull=True, is_active=True).count()
        
        print(f"   ✓ 总员工数: {total_staff}")
        print(f"   ✓ 活跃员工数: {active_staff}")
        
        # 检查最近创建的员工
        recent_staff = Staff.objects.filter(deleted_at__isnull=True).order_by('-created_at').first()
        if recent_staff:
            print(f"   ✓ 最近创建员工: {recent_staff.name} ({recent_staff.employee_no})")
            print(f"   ✓ 创建时间: {recent_staff.created_at}")
            print(f"   ✓ 有旧匿名编号: {'是' if recent_staff.anonymous_code else '否'}")
            print(f"   ✓ 有新匿名编号: {'是' if recent_staff.new_anonymous_code else '否'}")
        
    except Exception as e:
        print(f"   ✗ 员工数据检查失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("员工创建功能测试完成")
    print("✅ 所有核心功能正常工作")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    test_staff_creation_logic()
