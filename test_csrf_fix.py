#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试CSRF token修复
验证CSRF token的获取和使用是否正常
"""

import os
import sys
import django
import requests
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from django.conf import settings
from django.test import Client
from django.middleware.csrf import get_token
from django.http import HttpRequest

def test_csrf_configuration():
    """测试CSRF配置"""
    print("=" * 60)
    print("测试CSRF配置")
    print("=" * 60)
    
    print(f"CSRF_COOKIE_SECURE: {getattr(settings, 'CSRF_COOKIE_SECURE', 'Not set')}")
    print(f"CSRF_COOKIE_HTTPONLY: {getattr(settings, 'CSRF_COOKIE_HTTPONLY', 'Not set')}")
    print(f"CSRF_COOKIE_SAMESITE: {getattr(settings, 'CSRF_COOKIE_SAMESITE', 'Not set')}")
    print(f"CSRF_COOKIE_NAME: {getattr(settings, 'CSRF_COOKIE_NAME', 'Not set')}")
    print(f"CSRF_HEADER_NAME: {getattr(settings, 'CSRF_HEADER_NAME', 'Not set')}")
    
    # 检查配置是否正确
    if getattr(settings, 'CSRF_COOKIE_HTTPONLY', True) == False:
        print("✅ CSRF_COOKIE_HTTPONLY 设置正确，JavaScript可以访问CSRF cookie")
    else:
        print("❌ CSRF_COOKIE_HTTPONLY 设置错误，JavaScript无法访问CSRF cookie")

def test_csrf_token_generation():
    """测试CSRF token生成"""
    print("\n" + "=" * 60)
    print("测试CSRF token生成")
    print("=" * 60)
    
    try:
        # 创建测试客户端
        client = Client()
        
        # 获取登录页面，这会设置CSRF token
        response = client.get('/admin/login/')
        print(f"登录页面状态码: {response.status_code}")
        
        # 检查CSRF token是否在cookie中
        csrf_cookie = response.cookies.get('csrftoken')
        if csrf_cookie:
            print(f"✅ CSRF cookie存在")
            print(f"   Token值: {csrf_cookie.value}")
            print(f"   Token长度: {len(csrf_cookie.value)}")
            print(f"   HttpOnly: {csrf_cookie.get('httponly', False)}")
        else:
            print("❌ CSRF cookie不存在")
        
        # 检查CSRF token是否在HTML中
        html_content = response.content.decode('utf-8')
        if 'csrfmiddlewaretoken' in html_content:
            print("✅ HTML中包含CSRF token")
            # 提取token值
            import re
            token_match = re.search(r'name=["\']csrfmiddlewaretoken["\'] value=["\']([^"\']+)["\']', html_content)
            if token_match:
                html_token = token_match.group(1)
                print(f"   HTML Token值: {html_token}")
                print(f"   HTML Token长度: {len(html_token)}")
                
                # 比较cookie和HTML中的token
                if csrf_cookie and csrf_cookie.value == html_token:
                    print("✅ Cookie和HTML中的token一致")
                else:
                    print("❌ Cookie和HTML中的token不一致")
            else:
                print("❌ 无法从HTML中提取token")
        else:
            print("❌ HTML中不包含CSRF token")
            
    except Exception as e:
        print(f"❌ CSRF token生成测试失败: {e}")

def test_csrf_header_formats():
    """测试不同的CSRF header格式"""
    print("\n" + "=" * 60)
    print("测试CSRF header格式")
    print("=" * 60)
    
    # 测试不同的header名称
    headers_to_test = [
        'X-CSRFToken',
        'X-CSRFTOKEN', 
        'HTTP_X_CSRFTOKEN',
        'X-Csrftoken'
    ]
    
    for header in headers_to_test:
        print(f"测试header: {header}")
        
        # Django内部处理header名称的方式
        django_header = f"HTTP_{header.replace('-', '_').upper()}"
        print(f"   Django内部名称: {django_header}")
        
        # 检查是否匹配配置
        csrf_header_name = getattr(settings, 'CSRF_HEADER_NAME', 'HTTP_X_CSRFTOKEN')
        if django_header == csrf_header_name:
            print(f"   ✅ 匹配配置的header名称")
        else:
            print(f"   ❌ 不匹配配置的header名称 ({csrf_header_name})")

def test_ajax_request_simulation():
    """模拟AJAX请求测试"""
    print("\n" + "=" * 60)
    print("模拟AJAX请求测试")
    print("=" * 60)
    
    try:
        # 使用requests模拟浏览器行为
        session = requests.Session()
        
        # 1. 获取登录页面和CSRF token
        login_url = "http://127.0.0.1:8000/admin/login/"
        response = session.get(login_url)
        
        if response.status_code == 200:
            print("✅ 成功获取登录页面")
            
            # 提取CSRF token
            csrf_token = None
            
            # 从cookie获取
            csrf_cookie = session.cookies.get('csrftoken')
            if csrf_cookie:
                print(f"✅ 从cookie获取token: {csrf_cookie[:10]}...")
                csrf_token = csrf_cookie
            
            # 从HTML获取
            import re
            token_match = re.search(r'name=["\']csrfmiddlewaretoken["\'] value=["\']([^"\']+)["\']', response.text)
            if token_match:
                html_token = token_match.group(1)
                print(f"✅ 从HTML获取token: {html_token[:10]}...")
                
                if csrf_token and csrf_token != html_token:
                    print(f"⚠️  Cookie和HTML token不同")
                    print(f"   Cookie长度: {len(csrf_token)}")
                    print(f"   HTML长度: {len(html_token)}")
                
                # 使用HTML token（更可靠）
                csrf_token = html_token
            
            if csrf_token:
                print(f"✅ 最终使用token长度: {len(csrf_token)}")
                
                # 2. 模拟AJAX请求（不实际发送，只检查token格式）
                headers = {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrf_token,
                    'Referer': login_url
                }
                
                print("✅ AJAX请求headers准备完成")
                print(f"   Content-Type: {headers['Content-Type']}")
                print(f"   X-CSRFToken: {headers['X-CSRFToken'][:10]}...")
                print(f"   Token长度: {len(headers['X-CSRFToken'])}")
                
            else:
                print("❌ 无法获取CSRF token")
        else:
            print(f"❌ 无法访问登录页面: {response.status_code}")
            
    except Exception as e:
        print(f"❌ AJAX请求模拟失败: {e}")

if __name__ == "__main__":
    test_csrf_configuration()
    test_csrf_token_generation()
    test_csrf_header_formats()
    test_ajax_request_simulation()
    
    print("\n" + "=" * 60)
    print("CSRF修复测试完成")
    print("=" * 60)
