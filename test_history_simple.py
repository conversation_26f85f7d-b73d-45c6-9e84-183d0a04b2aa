#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的考评历史修复测试
"""

import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from evaluations.models import EvaluationBatch

def test_year_queries():
    """测试年份相关查询"""
    print("测试年份查询修复...")
    
    try:
        # 1. 测试基本查询
        all_batches = EvaluationBatch.objects.all()
        print(f"✅ 总批次数: {all_batches.count()}")
        
        # 2. 测试年份筛选查询
        year_2025_batches = EvaluationBatch.objects.filter(start_date__year=2025)
        print(f"✅ 2025年批次数: {year_2025_batches.count()}")
        
        # 3. 测试年份列表生成
        years = EvaluationBatch.objects.filter(
            start_date__isnull=False
        ).values_list('start_date__year', flat=True).distinct()
        years_list = sorted(set(filter(None, years)), reverse=True)
        print(f"✅ 可用年份: {years_list}")
        
        # 4. 测试搜索查询（不包含year字段）
        search_batches = EvaluationBatch.objects.filter(
            name__icontains='test'
        )
        print(f"✅ 搜索查询正常: {search_batches.count()} 个结果")
        
        print("✅ 所有年份查询测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 年份查询测试失败: {e}")
        return False

def test_batch_properties():
    """测试批次属性"""
    print("\n测试批次属性...")
    
    try:
        batches = EvaluationBatch.objects.all()
        
        for batch in batches:
            print(f"批次: {batch.name}")
            print(f"  - start_date: {batch.start_date}")
            print(f"  - year属性: {batch.year}")
            print(f"  - 状态: {batch.status}")
        
        print("✅ 批次属性访问正常！")
        return True
        
    except Exception as e:
        print(f"❌ 批次属性测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("考评历史修复验证")
    print("=" * 50)
    
    success1 = test_year_queries()
    success2 = test_batch_properties()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 修复验证成功！历史页面应该可以正常访问了。")
    else:
        print("❌ 修复验证失败，需要进一步检查。")
    print("=" * 50)
