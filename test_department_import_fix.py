#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试部门导入修复
验证pandas DataFrame处理NaN值的问题是否已解决
"""

import pandas as pd
import numpy as np
from io import BytesIO

def test_dataframe_str_operations():
    """测试DataFrame字符串操作"""
    print("=== 测试DataFrame字符串操作 ===\n")
    
    # 创建测试数据，包含NaN值
    test_data = {
        '部门编号*': ['DEPT001', 'DEPT002', 'DEPT003'],
        '部门名称*': ['总经理室', '技术部', '人事部'],
        '上级部门名称': ['', '总经理室', np.nan],  # 包含空字符串和NaN
        '备注': ['顶级部门', '', np.nan]
    }
    
    df = pd.DataFrame(test_data)
    print("原始数据:")
    print(df)
    print(f"数据类型:\n{df.dtypes}")
    print()
    
    # 测试修复前的问题（这会导致错误）
    print("测试修复前的问题:")
    try:
        # 这行代码会导致错误：Can only use .str accessor with string values!
        result = df['上级部门名称'].notna() & (df['上级部门名称'].str.strip() != '')
        print("❌ 没有出现预期的错误")
    except Exception as e:
        print(f"✅ 预期的错误: {e}")
    print()
    
    # 测试修复后的方法
    print("测试修复后的方法:")
    try:
        # 先转换为字符串
        df_fixed = df.copy()
        df_fixed['上级部门名称'] = df_fixed['上级部门名称'].astype(str)
        
        # 然后安全地使用.str方法
        df_fixed['has_parent'] = (
            df_fixed['上级部门名称'].notna() & 
            (df_fixed['上级部门名称'] != 'nan') & 
            (df_fixed['上级部门名称'].str.strip() != '')
        )
        
        print("✅ 修复成功!")
        print("修复后的数据:")
        print(df_fixed[['上级部门名称', 'has_parent']])
        print()
        
        # 测试排序
        df_sorted = df_fixed.sort_values(['has_parent', '上级部门名称'])
        print("排序后的数据:")
        print(df_sorted[['部门名称*', '上级部门名称', 'has_parent']])
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
    
    return True

def test_excel_clean_data():
    """测试Excel数据清理"""
    print("\n=== 测试Excel数据清理 ===\n")
    
    # 模拟Excel数据清理过程
    test_data = {
        '部门编号*': ['DEPT001', 'DEPT002', '  DEPT003  '],
        '部门名称*': ['总经理室', '  技术部  ', '人事部'],
        '上级部门名称': ['', '总经理室', np.nan],
        '备注': ['顶级部门', '  ', np.nan]
    }
    
    df = pd.DataFrame(test_data)
    print("清理前的数据:")
    print(df)
    print()
    
    # 应用修复后的清理逻辑
    for col in df.select_dtypes(include=['object']).columns:
        # 只对非空值进行字符串处理
        mask = df[col].notna()
        df.loc[mask, col] = df.loc[mask, col].astype(str).str.strip()
    
    # 替换空字符串和'nan'字符串为NaN
    df = df.replace(['', 'nan', 'NaN', 'None'], pd.NA)
    
    print("清理后的数据:")
    print(df)
    print(f"数据类型:\n{df.dtypes}")
    
    return True

if __name__ == '__main__':
    print("部门导入修复测试\n")
    
    success1 = test_dataframe_str_operations()
    success2 = test_excel_clean_data()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！修复应该有效。")
    else:
        print("\n❌ 测试失败，需要进一步调试。")
