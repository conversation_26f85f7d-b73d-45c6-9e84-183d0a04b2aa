/* 时间显示美化样式 */

/* 时间卡片基础样式 */
.time-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    border-radius: 12px;
    padding: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.time-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #06b6d4);
    border-radius: 12px 12px 0 0;
}

.time-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 时间图标样式 */
.time-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    transition: transform 0.2s ease;
}

.time-card:hover .time-icon {
    transform: scale(1.1);
}

/* 时间文本样式 */
.time-primary {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
    letter-spacing: -0.025em;
}

.time-secondary {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
}

/* 特定颜色主题 */
.time-card.green {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border-color: #bbf7d0;
}

.time-card.green::before {
    background: linear-gradient(90deg, #22c55e, #16a34a);
}

.time-card.green .time-primary {
    color: #15803d;
}

.time-card.green .time-secondary {
    color: #16a34a;
}

.time-card.red {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    border-color: #fca5a5;
}

.time-card.red::before {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.time-card.red .time-primary {
    color: #dc2626;
}

.time-card.red .time-secondary {
    color: #ef4444;
}

.time-card.blue {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-color: #93c5fd;
}

.time-card.blue::before {
    background: linear-gradient(90deg, #3b82f6, #2563eb);
}

.time-card.blue .time-primary {
    color: #1d4ed8;
}

.time-card.blue .time-secondary {
    color: #2563eb;
}

/* 时间范围显示样式 */
.time-range {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    transition: all 0.2s ease;
}

.time-range:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-color: #cbd5e1;
}

.time-range-separator {
    color: #64748b;
    font-weight: 500;
    margin: 0 8px;
}

/* 时间标签样式 */
.time-label {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    background: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid rgba(59, 130, 246, 0.2);
    transition: all 0.2s ease;
}

.time-label:hover {
    background: rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.3);
}

.time-label.success {
    background: rgba(34, 197, 94, 0.1);
    color: #15803d;
    border-color: rgba(34, 197, 94, 0.2);
}

.time-label.success:hover {
    background: rgba(34, 197, 94, 0.15);
    border-color: rgba(34, 197, 94, 0.3);
}

.time-label.danger {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border-color: rgba(239, 68, 68, 0.2);
}

.time-label.danger:hover {
    background: rgba(239, 68, 68, 0.15);
    border-color: rgba(239, 68, 68, 0.3);
}

/* 时间列表项样式 */
.time-list-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.time-list-item:last-child {
    border-bottom: none;
}

.time-list-item:hover {
    background: rgba(59, 130, 246, 0.05);
    border-radius: 6px;
    padding-left: 8px;
    padding-right: 8px;
}

/* 时间徽章样式 */
.time-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    color: #475569;
    transition: all 0.2s ease;
}

.time-badge:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
}

.time-badge .time-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .time-card {
        padding: 12px;
    }
    
    .time-primary {
        font-size: 16px;
    }
    
    .time-secondary {
        font-size: 12px;
    }
    
    .time-range {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }
    
    .time-range-separator {
        margin: 0;
    }
}

/* 动画效果 */
@keyframes timeCardFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.time-card {
    animation: timeCardFadeIn 0.3s ease-out;
}

/* 时间状态指示器 */
.time-status-indicator {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #22c55e;
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

.time-status-indicator.pending {
    background: #f59e0b;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.time-status-indicator.expired {
    background: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* 时间进度条 */
.time-progress {
    width: 100%;
    height: 4px;
    background: #f1f5f9;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 8px;
}

.time-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #06b6d4);
    border-radius: 2px;
    transition: width 0.3s ease;
}
