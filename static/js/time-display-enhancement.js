/**
 * 时间显示增强脚本
 * 提供时间相关的交互效果和功能
 */

document.addEventListener('DOMContentLoaded', function() {
    initTimeDisplayEnhancements();
});

function initTimeDisplayEnhancements() {
    // 初始化时间卡片动画
    initTimeCardAnimations();
    
    // 初始化时间状态指示器
    initTimeStatusIndicators();
    
    // 初始化时间进度条
    initTimeProgressBars();
    
    // 初始化时间工具提示
    initTimeTooltips();
    
    // 初始化相对时间显示
    initRelativeTimeDisplay();
}

/**
 * 初始化时间卡片动画
 */
function initTimeCardAnimations() {
    const timeCards = document.querySelectorAll('.time-card');
    
    timeCards.forEach((card, index) => {
        // 添加延迟动画
        card.style.animationDelay = `${index * 0.1}s`;
        
        // 添加悬停效果
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
            this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '';
        });
    });
}

/**
 * 初始化时间状态指示器
 */
function initTimeStatusIndicators() {
    const indicators = document.querySelectorAll('.time-status-indicator');
    
    indicators.forEach(indicator => {
        // 添加脉冲动画
        if (!indicator.classList.contains('expired')) {
            indicator.style.animation = 'pulse 2s infinite';
        }
    });
    
    // 添加脉冲动画样式
    if (!document.querySelector('#pulse-animation-style')) {
        const style = document.createElement('style');
        style.id = 'pulse-animation-style';
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.2); opacity: 0.7; }
                100% { transform: scale(1); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * 初始化时间进度条
 */
function initTimeProgressBars() {
    const progressBars = document.querySelectorAll('.time-progress-bar');
    
    progressBars.forEach(bar => {
        const progress = bar.getAttribute('data-progress') || 0;
        
        // 动画显示进度
        setTimeout(() => {
            bar.style.width = `${progress}%`;
        }, 300);
    });
}

/**
 * 初始化时间工具提示
 */
function initTimeTooltips() {
    const timeElements = document.querySelectorAll('[data-time-tooltip]');
    
    timeElements.forEach(element => {
        const tooltip = element.getAttribute('data-time-tooltip');
        
        element.addEventListener('mouseenter', function(e) {
            showTooltip(e.target, tooltip);
        });
        
        element.addEventListener('mouseleave', function() {
            hideTooltip();
        });
    });
}

/**
 * 显示工具提示
 */
function showTooltip(element, text) {
    const tooltip = document.createElement('div');
    tooltip.className = 'time-tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
        position: absolute;
        background: #1f2937;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease;
    `;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.left = `${rect.left + rect.width / 2 - tooltip.offsetWidth / 2}px`;
    tooltip.style.top = `${rect.top - tooltip.offsetHeight - 8}px`;
    
    setTimeout(() => {
        tooltip.style.opacity = '1';
    }, 10);
}

/**
 * 隐藏工具提示
 */
function hideTooltip() {
    const tooltip = document.querySelector('.time-tooltip');
    if (tooltip) {
        tooltip.style.opacity = '0';
        setTimeout(() => {
            tooltip.remove();
        }, 200);
    }
}

/**
 * 初始化相对时间显示
 */
function initRelativeTimeDisplay() {
    const timeElements = document.querySelectorAll('[data-relative-time]');
    
    timeElements.forEach(element => {
        const timestamp = element.getAttribute('data-relative-time');
        const relativeTime = getRelativeTime(new Date(timestamp));
        
        // 创建相对时间显示
        const relativeSpan = document.createElement('span');
        relativeSpan.className = 'relative-time text-xs text-gray-500 ml-2';
        relativeSpan.textContent = `(${relativeTime})`;
        
        element.appendChild(relativeSpan);
    });
    
    // 每分钟更新相对时间
    setInterval(updateRelativeTimes, 60000);
}

/**
 * 获取相对时间
 */
function getRelativeTime(date) {
    const now = new Date();
    const diff = now - date;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const months = Math.floor(days / 30);
    const years = Math.floor(days / 365);
    
    if (years > 0) return `${years}年前`;
    if (months > 0) return `${months}个月前`;
    if (days > 0) return `${days}天前`;
    if (hours > 0) return `${hours}小时前`;
    if (minutes > 0) return `${minutes}分钟前`;
    return '刚刚';
}

/**
 * 更新相对时间显示
 */
function updateRelativeTimes() {
    const relativeElements = document.querySelectorAll('.relative-time');
    
    relativeElements.forEach(element => {
        const parent = element.parentElement;
        const timestamp = parent.getAttribute('data-relative-time');
        if (timestamp) {
            const relativeTime = getRelativeTime(new Date(timestamp));
            element.textContent = `(${relativeTime})`;
        }
    });
}

/**
 * 时间格式化工具函数
 */
function formatTime(date, format = 'YYYY-MM-DD HH:mm') {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * 时间范围计算
 */
function calculateTimeRange(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diff = end - start;
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (days > 0) {
        return `${days}天${hours > 0 ? hours + '小时' : ''}`;
    } else {
        return `${hours}小时`;
    }
}

/**
 * 时间状态检查
 */
function checkTimeStatus(startDate, endDate) {
    const now = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (now < start) {
        return 'pending'; // 未开始
    } else if (now >= start && now <= end) {
        return 'active'; // 进行中
    } else {
        return 'expired'; // 已结束
    }
}

// 导出函数供其他脚本使用
window.TimeDisplayEnhancement = {
    formatTime,
    calculateTimeRange,
    checkTimeStatus,
    getRelativeTime
};
