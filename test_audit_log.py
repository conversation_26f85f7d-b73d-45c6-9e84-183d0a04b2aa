#!/usr/bin/env python
"""
测试审计日志功能是否正常
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
sys.path.append('/mnt/d/code/newmachinecode/UniversalStaffEvaluation3')

django.setup()

def test_audit_log():
    """测试审计日志模型"""
    try:
        from common.models import AuditLog
        
        # 测试创建审计日志
        log = AuditLog.objects.create(
            user='test_user',
            action='update',
            target_model='EvaluationBatch',
            target_id=1,
            description='测试审计日志功能',
            ip_address='127.0.0.1',
            user_agent='Test Agent'
        )
        
        print("✅ 审计日志创建成功")
        print(f"📝 日志ID: {log.id}")
        print(f"👤 用户: {log.user}")
        print(f"🎯 操作: {log.get_action_display()}")
        print(f"📊 目标模型: {log.target_model}")
        print(f"🔢 目标ID: {log.target_id}")
        print(f"📄 描述: {log.description}")
        
        # 清理测试数据
        log.delete()
        print("🧹 测试数据已清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 审计日志测试失败: {e}")
        return False

if __name__ == '__main__':
    print("🔍 测试审计日志功能...")
    success = test_audit_log()
    
    if success:
        print("✅ 审计日志功能正常，批次模板配置功能已修复！")
    else:
        print("❌ 审计日志功能存在问题")
    
    sys.exit(0 if success else 1)