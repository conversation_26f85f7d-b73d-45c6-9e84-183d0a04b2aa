# -*- coding: utf-8 -*-
"""
时间显示演示视图
用于展示时间显示美化效果
"""

from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required


@login_required
@staff_member_required
def time_display_demo_view(request):
    """
    时间显示演示页面
    展示各种时间显示美化效果
    """
    return render(request, 'admin/time-display-demo.html', {
        'page_title': '时间显示效果演示',
        'page_description': '展示考评模板和考评批次页面的时间显示美化效果'
    })
