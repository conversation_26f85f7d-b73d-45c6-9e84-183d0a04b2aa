<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试前端修复</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 8px 16px; margin: 5px; }
        input { padding: 8px; margin: 5px; width: 200px; }
        .flex { display: flex; align-items: center; }
        .animate-spin { animation: spin 1s linear infinite; }
        @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <h1>前端JavaScript修复测试</h1>
    
    <div class="test-section">
        <h2>测试1: 元素查找</h2>
        <div class="flex">
            <input type="text" id="id_anonymous_code" readonly placeholder="匿名编号将在这里显示">
            <button type="button" id="refresh-anonymous-code-btn" onclick="generateAnonymousCode()">
                <span id="refresh-anonymous-code-icon">🔄</span> 生成编号
            </button>
        </div>
        <p id="test1-result" class="info">等待测试...</p>
    </div>
    
    <div class="test-section">
        <h2>测试2: 部门选择</h2>
        <select id="id_department">
            <option value="">请选择部门</option>
            <option value="1">办公室</option>
            <option value="2">技术部</option>
            <option value="3">人事部</option>
        </select>
        <p id="test2-result" class="info">选择部门后点击生成编号测试</p>
    </div>
    
    <div class="test-section">
        <h2>测试3: 错误处理</h2>
        <button onclick="testErrorHandling()">测试错误处理</button>
        <p id="test3-result" class="info">点击按钮测试错误处理</p>
    </div>
    
    <div class="test-section">
        <h2>测试日志</h2>
        <div id="log" style="background: #f5f5f5; padding: 10px; height: 200px; overflow-y: auto;"></div>
    </div>

    <script>
        // 模拟CSRF token
        const mockCSRF = document.createElement('input');
        mockCSRF.name = 'csrfmiddlewaretoken';
        mockCSRF.value = 'mock-csrf-token';
        mockCSRF.type = 'hidden';
        document.body.appendChild(mockCSRF);
        
        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // 模拟通知函数
        function showNotification(message, type) {
            log(`通知: ${message}`, type);
            alert(`${type.toUpperCase()}: ${message}`);
        }
        
        // 修复后的生成匿名编号函数
        function generateAnonymousCode() {
            log('开始生成匿名编号...');
            
            const anonymousCodeInput = document.getElementById('id_anonymous_code');
            const refreshButton = document.getElementById('refresh-anonymous-code-btn');
            const refreshIcon = document.getElementById('refresh-anonymous-code-icon');
            
            // 检查元素是否存在
            if (!anonymousCodeInput || !refreshButton || !refreshIcon) {
                const missing = [];
                if (!anonymousCodeInput) missing.push('匿名编号输入框');
                if (!refreshButton) missing.push('刷新按钮');
                if (!refreshIcon) missing.push('刷新图标');
                
                const errorMsg = `无法找到必要的页面元素: ${missing.join(', ')}`;
                log(errorMsg, 'error');
                showNotification('页面元素加载错误，请刷新页面重试', 'error');
                document.getElementById('test1-result').innerHTML = `<span class="error">❌ ${errorMsg}</span>`;
                return;
            }
            
            log('所有页面元素找到，开始处理...', 'success');
            document.getElementById('test1-result').innerHTML = '<span class="success">✅ 所有元素找到成功</span>';
            
            // 禁用按钮并显示加载状态
            refreshButton.disabled = true;
            refreshIcon.classList.add('animate-spin');
            log('按钮已禁用，开始加载动画');
            
            // 获取CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            log(`CSRF Token: ${csrfToken}`);
            
            // 获取部门ID（如果已选择）
            const departmentSelect = document.getElementById('id_department');
            const departmentId = departmentSelect ? departmentSelect.value : 1;
            log(`部门ID: ${departmentId || '未选择(使用默认值1)'}`);
            
            // 模拟API调用
            setTimeout(() => {
                try {
                    // 模拟成功响应
                    const mockCode = 'TEST-' + Math.random().toString(36).substr(2, 8).toUpperCase();
                    anonymousCodeInput.value = mockCode;
                    log(`生成成功: ${mockCode}`, 'success');
                    showNotification('安全编号生成成功', 'success');
                    
                    document.getElementById('test2-result').innerHTML = 
                        `<span class="success">✅ 部门ID传递成功: ${departmentId}</span>`;
                    
                } catch (error) {
                    log(`生成失败: ${error.message}`, 'error');
                    showNotification('生成编号失败，请重试', 'error');
                } finally {
                    // 恢复按钮状态（安全检查）
                    if (refreshButton) {
                        refreshButton.disabled = false;
                        log('按钮已重新启用');
                    }
                    if (refreshIcon) {
                        refreshIcon.classList.remove('animate-spin');
                        log('加载动画已停止');
                    }
                }
            }, 1000); // 模拟1秒的API调用时间
        }
        
        // 测试错误处理
        function testErrorHandling() {
            log('测试错误处理...');
            
            // 临时移除元素来测试错误处理
            const originalButton = document.getElementById('refresh-anonymous-code-btn');
            const originalIcon = document.getElementById('refresh-anonymous-code-icon');
            
            if (originalButton) originalButton.id = 'temp-hidden-btn';
            if (originalIcon) originalIcon.id = 'temp-hidden-icon';
            
            // 调用函数，应该触发错误处理
            generateAnonymousCode();
            
            // 恢复元素
            setTimeout(() => {
                if (originalButton) originalButton.id = 'refresh-anonymous-code-btn';
                if (originalIcon) originalIcon.id = 'refresh-anonymous-code-icon';
                
                document.getElementById('test3-result').innerHTML = 
                    '<span class="success">✅ 错误处理测试完成，检查日志</span>';
                log('错误处理测试完成，元素已恢复', 'success');
            }, 100);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化测试');
            
            // 延迟自动生成匿名编号，确保页面完全加载
            setTimeout(() => {
                log('开始自动生成匿名编号测试...');
                generateAnonymousCode();
            }, 100);
        });
    </script>
</body>
</html>
