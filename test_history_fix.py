#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试考评历史视图修复
验证年份字段查询问题是否已解决
"""

import os
import sys
import django
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from django.test import Client, RequestFactory
from django.contrib.auth.models import AnonymousUser
from evaluations.models import EvaluationBatch
from evaluations.views.history_views import EvaluationHistoryListView
from organizations.models import Staff

def test_evaluation_batch_model():
    """测试EvaluationBatch模型的year属性"""
    print("=" * 60)
    print("测试EvaluationBatch模型")
    print("=" * 60)
    
    try:
        # 检查是否有批次数据
        batch_count = EvaluationBatch.objects.count()
        print(f"✅ 数据库中有 {batch_count} 个考评批次")
        
        if batch_count > 0:
            # 测试第一个批次的year属性
            first_batch = EvaluationBatch.objects.first()
            print(f"✅ 第一个批次: {first_batch.name}")
            print(f"✅ 开始时间: {first_batch.start_date}")
            print(f"✅ year属性: {first_batch.year}")
            
            # 测试数据库查询
            print("\n测试数据库查询:")
            
            # 正确的查询方式
            if first_batch.start_date:
                year = first_batch.start_date.year
                batches_by_year = EvaluationBatch.objects.filter(start_date__year=year)
                print(f"✅ 按年份({year})查询: 找到 {batches_by_year.count()} 个批次")
            
            # 测试年份列表生成
            years = EvaluationBatch.objects.filter(
                start_date__isnull=False
            ).values_list('start_date__year', flat=True).distinct()
            print(f"✅ 可用年份: {sorted(set(years), reverse=True)}")
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False
    
    return True

def test_history_view_queryset():
    """测试历史视图的查询集"""
    print("\n" + "=" * 60)
    print("测试历史视图查询集")
    print("=" * 60)
    
    try:
        # 创建视图实例
        view = EvaluationHistoryListView()
        
        # 模拟请求
        factory = RequestFactory()
        
        # 测试无参数查询
        request = factory.get('/evaluations/admin/history/')
        request.user = AnonymousUser()
        view.request = request
        
        queryset = view.get_queryset()
        print(f"✅ 无参数查询: 返回 {queryset.count()} 个批次")
        
        # 测试搜索查询
        request = factory.get('/evaluations/admin/history/?search=测试')
        request.user = AnonymousUser()
        view.request = request
        
        queryset = view.get_queryset()
        print(f"✅ 搜索查询: 返回 {queryset.count()} 个批次")
        
        # 测试年份筛选
        current_year = datetime.now().year
        request = factory.get(f'/evaluations/admin/history/?year={current_year}')
        request.user = AnonymousUser()
        view.request = request
        
        queryset = view.get_queryset()
        print(f"✅ 年份筛选({current_year}): 返回 {queryset.count()} 个批次")
        
        # 测试无效年份
        request = factory.get('/evaluations/admin/history/?year=invalid')
        request.user = AnonymousUser()
        view.request = request
        
        queryset = view.get_queryset()
        print(f"✅ 无效年份筛选: 返回 {queryset.count()} 个批次（应该忽略无效年份）")
        
    except Exception as e:
        print(f"❌ 视图查询集测试失败: {e}")
        return False
    
    return True

def test_available_years_method():
    """测试可用年份方法"""
    print("\n" + "=" * 60)
    print("测试可用年份方法")
    print("=" * 60)
    
    try:
        view = EvaluationHistoryListView()
        years = view._get_available_years()
        
        print(f"✅ 可用年份列表: {years}")
        print(f"✅ 年份数量: {len(years)}")
        
        # 验证年份是否按降序排列
        if len(years) > 1:
            is_descending = all(years[i] >= years[i+1] for i in range(len(years)-1))
            print(f"✅ 年份排序: {'降序' if is_descending else '升序'}")
        
    except Exception as e:
        print(f"❌ 可用年份方法测试失败: {e}")
        return False
    
    return True

def test_context_data():
    """测试上下文数据"""
    print("\n" + "=" * 60)
    print("测试上下文数据")
    print("=" * 60)
    
    try:
        view = EvaluationHistoryListView()
        factory = RequestFactory()
        request = factory.get('/evaluations/admin/history/')
        request.user = AnonymousUser()
        view.request = request
        
        # 设置object_list（模拟ListView的行为）
        view.object_list = view.get_queryset()
        
        context = view.get_context_data()
        
        print(f"✅ 上下文数据获取成功")
        print(f"✅ 统计数据: {context.get('stats', {})}")
        print(f"✅ 可用年份: {context.get('years', [])}")
        print(f"✅ 状态选择: {len(context.get('status_choices', []))} 个选项")
        
    except Exception as e:
        print(f"❌ 上下文数据测试失败: {e}")
        return False
    
    return True

def test_http_request():
    """测试HTTP请求"""
    print("\n" + "=" * 60)
    print("测试HTTP请求")
    print("=" * 60)
    
    try:
        client = Client()
        
        # 测试历史页面访问（不登录，可能会重定向）
        response = client.get('/evaluations/admin/history/')
        print(f"✅ 历史页面响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 页面加载成功")
        elif response.status_code == 302:
            print("✅ 重定向到登录页面（正常行为）")
        else:
            print(f"⚠️  意外的响应状态: {response.status_code}")
        
    except Exception as e:
        print(f"❌ HTTP请求测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("开始测试考评历史视图修复...")
    
    success_count = 0
    total_tests = 5
    
    if test_evaluation_batch_model():
        success_count += 1
    
    if test_history_view_queryset():
        success_count += 1
    
    if test_available_years_method():
        success_count += 1
    
    if test_context_data():
        success_count += 1
    
    if test_http_request():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试完成: {success_count}/{total_tests} 个测试通过")
    if success_count == total_tests:
        print("✅ 所有测试通过，修复成功！")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    print("=" * 60)
