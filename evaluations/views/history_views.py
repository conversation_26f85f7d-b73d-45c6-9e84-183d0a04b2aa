# -*- coding: utf-8 -*-
"""
考评历史管理视图
提供历史考评数据查看、对比分析和归档管理功能
"""

from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Count, Avg, Max, Min
from django.utils import timezone
from datetime import datetime, timedelta
from django.core.paginator import Paginator

from evaluations.models import EvaluationBatch, EvaluationRecord, EvaluationRelation
from organizations.models import Department, Staff
from common.models import AuditLog
from common.views import AdminRequiredMixin
import json


class EvaluationHistoryListView(AdminRequiredMixin, ListView):
    """
    考评历史列表视图
    显示所有历史考评批次的概览信息
    """
    model = EvaluationBatch
    template_name = 'admin/evaluation/history_list.html'
    context_object_name = 'batches'
    paginate_by = 20
    
    def get_queryset(self):
        """获取考评批次查询集，包含统计信息"""
        queryset = EvaluationBatch.all_objects.all().select_related(
            'created_by_staff', 'default_template'
        ).prefetch_related('evaluationrelation_set')
        
        # 搜索筛选
        search = self.request.GET.get('search', '').strip()
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )
        
        # 状态筛选
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        # 年份筛选
        year = self.request.GET.get('year')
        if year:
            try:
                year_int = int(year)
                queryset = queryset.filter(start_date__year=year_int)
            except (ValueError, TypeError):
                pass  # 忽略无效的年份值
        
        # 部门筛选
        department = self.request.GET.get('department')
        if department:
            queryset = queryset.filter(
                evaluationrelation__evaluatee__department_id=department
            ).distinct()
        
        # 时间范围筛选
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        if date_from:
            queryset = queryset.filter(start_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(end_date__lte=date_to)
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)
        
        # 统计数据
        context['stats'] = self._get_statistics()
        
        # 筛选选项
        context['departments'] = Department.objects.all()
        context['years'] = self._get_available_years()
        context['status_choices'] = EvaluationBatch.STATUS_CHOICES
        
        # 保持搜索参数
        context['search'] = self.request.GET.get('search', '')
        context['current_status'] = self.request.GET.get('status', '')
        context['current_year'] = self.request.GET.get('year', '')
        context['current_department'] = self.request.GET.get('department', '')
        context['date_from'] = self.request.GET.get('date_from', '')
        context['date_to'] = self.request.GET.get('date_to', '')
        
        return context
    
    def _get_statistics(self):
        """获取统计数据"""
        all_batches = EvaluationBatch.all_objects.all()
        
        return {
            'total_batches': all_batches.count(),
            'completed_batches': all_batches.filter(status='completed').count(),
            'active_batches': all_batches.filter(status='active').count(),
            'draft_batches': all_batches.filter(status='draft').count(),
            'total_evaluations': EvaluationRecord.objects.count(),
            'this_year_batches': all_batches.filter(
                created_at__year=timezone.now().year
            ).count(),
        }
    
    def _get_available_years(self):
        """获取可用的年份列表"""
        # 获取所有有开始时间的批次，然后在Python中提取年份
        batches = EvaluationBatch.objects.filter(
            start_date__isnull=False
        ).values_list('start_date', flat=True)

        years = set()
        for start_date in batches:
            if start_date:
                years.add(start_date.year)

        return sorted(years, reverse=True)


class EvaluationHistoryDetailView(AdminRequiredMixin, DetailView):
    """
    考评历史详情视图
    显示单个考评批次的详细信息和统计数据
    """
    model = EvaluationBatch
    template_name = 'admin/evaluation/history_detail.html'
    context_object_name = 'batch'
    
    def get_object(self):
        """获取对象，包括已删除的记录"""
        return get_object_or_404(
            EvaluationBatch.all_objects.select_related('default_template'),
            pk=self.kwargs['pk']
        )
    
    def get_context_data(self, **kwargs):
        """添加详细统计信息"""
        context = super().get_context_data(**kwargs)
        batch = self.object
        
        # 考评关系统计
        relations = EvaluationRelation.objects.filter(batch=batch)
        context['relations_stats'] = {
            'total': relations.count(),
            'completed': relations.filter(status='completed').count(),
            'pending': relations.filter(status='pending').count(),
            'in_progress': relations.filter(status='in_progress').count(),
        }
        
        # 部门参与统计
        context['department_stats'] = self._get_department_statistics(batch)
        
        # 评分统计
        context['score_stats'] = self._get_score_statistics(batch)
        
        # 最近活动记录
        context['recent_activities'] = self._get_recent_activities(batch)
        
        # 参与人员统计
        context['participant_stats'] = self._get_participant_statistics(batch)
        
        return context
    
    def _get_department_statistics(self, batch):
        """获取部门参与统计"""
        from django.db.models import Count, Q
        
        dept_stats = Department.objects.annotate(
            total_relations=Count(
                'staff__evaluationrelation_evaluatee_set',
                filter=Q(staff__evaluationrelation_evaluatee_set__batch=batch)
            ),
            completed_relations=Count(
                'staff__evaluationrelation_evaluatee_set',
                filter=Q(
                    staff__evaluationrelation_evaluatee_set__batch=batch,
                    staff__evaluationrelation_evaluatee_set__status='completed'
                )
            )
        ).filter(total_relations__gt=0)
        
        return dept_stats
    
    def _get_score_statistics(self, batch):
        """获取评分统计"""
        records = EvaluationRecord.objects.filter(
            relation__batch=batch,
            status='submitted'
        )
        
        if not records.exists():
            return None
        
        return {
            'total_records': records.count(),
            'avg_score': records.aggregate(Avg('total_score'))['total_score__avg'],
            'max_score': records.aggregate(Max('total_score'))['total_score__max'],
            'min_score': records.aggregate(Min('total_score'))['total_score__min'],
            'score_distribution': self._get_score_distribution(records),
        }
    
    def _get_score_distribution(self, records):
        """获取分数分布"""
        from django.db.models import Case, When, IntegerField, Count
        
        return records.aggregate(
            excellent=Count(Case(When(total_score__gte=90, then=1), output_field=IntegerField())),
            good=Count(Case(When(total_score__gte=80, total_score__lt=90, then=1), output_field=IntegerField())),
            average=Count(Case(When(total_score__gte=70, total_score__lt=80, then=1), output_field=IntegerField())),
            below_average=Count(Case(When(total_score__lt=70, then=1), output_field=IntegerField())),
        )
    
    def _get_recent_activities(self, batch):
        """获取最近活动记录"""
        return AuditLog.objects.filter(
            Q(target_model='EvaluationBatch', target_id=batch.id) |
            Q(extra_data__batch_id=batch.id)
        ).order_by('-created_at')[:10]
    
    def _get_participant_statistics(self, batch):
        """获取参与人员统计"""
        # 评价者统计
        evaluators = Staff.objects.filter(
            evaluationrelation_evaluator_set__batch=batch
        ).distinct()
        
        # 被评价者统计
        evaluatees = Staff.objects.filter(
            evaluationrelation_evaluatee_set__batch=batch
        ).distinct()
        
        return {
            'total_evaluators': evaluators.count(),
            'total_evaluatees': evaluatees.count(),
            'active_evaluators': evaluators.filter(
                evaluationrelation_evaluator_set__batch=batch,
                evaluationrelation_evaluator_set__status='completed'
            ).distinct().count(),
        }


class EvaluationComparisonView(AdminRequiredMixin, ListView):
    """
    考评对比分析视图
    支持多个批次之间的数据对比
    """
    template_name = 'admin/evaluation/history_comparison.html'
    
    def get(self, request, *args, **kwargs):
        """处理GET请求"""
        # 获取待对比的批次ID列表
        batch_ids = request.GET.getlist('batches')
        if not batch_ids:
            messages.warning(request, '请选择要对比的考评批次')
            return render(request, self.template_name, {'batches': []})
        
        # 获取批次数据及对比分析
        batches_data = self._get_comparison_data(batch_ids)
        
        context = {
            'batches_data': batches_data,
            'comparison_charts': self._get_comparison_charts(batches_data),
            'selected_batches': batch_ids,
        }
        
        return render(request, self.template_name, context)
    
    def _get_comparison_data(self, batch_ids):
        """获取对比数据"""
        batches = EvaluationBatch.all_objects.filter(id__in=batch_ids)
        comparison_data = []
        
        for batch in batches:
            # 基本信息
            basic_info = {
                'batch': batch,
                'relations_count': EvaluationRelation.objects.filter(batch=batch).count(),
                'completed_count': EvaluationRelation.objects.filter(
                    batch=batch, status='completed'
                ).count(),
            }
            
            # 评分统计
            records = EvaluationRecord.objects.filter(
                relation__batch=batch, status='submitted'
            )
            
            if records.exists():
                basic_info.update({
                    'avg_score': records.aggregate(Avg('total_score'))['total_score__avg'],
                    'max_score': records.aggregate(Max('total_score'))['total_score__max'],
                    'min_score': records.aggregate(Min('total_score'))['total_score__min'],
                    'total_records': records.count(),
                })
            else:
                basic_info.update({
                    'avg_score': 0,
                    'max_score': 0,
                    'min_score': 0,
                    'total_records': 0,
                })
            
            # 部门分布
            basic_info['department_distribution'] = self._get_department_distribution(batch)
            
            comparison_data.append(basic_info)
        
        return comparison_data
    
    def _get_department_distribution(self, batch):
        """获取部门分布数据"""
        return Department.objects.annotate(
            participant_count=Count(
                'staff__evaluationrelation_evaluatee_set',
                filter=Q(staff__evaluationrelation_evaluatee_set__batch=batch)
            )
        ).filter(participant_count__gt=0).values('name', 'participant_count')
    
    def _get_comparison_charts(self, batches_data):
        """生成对比图表数据"""
        chart_data = {
            'avg_scores': {
                'labels': [data['batch'].name for data in batches_data],
                'data': [float(data['avg_score'] or 0) for data in batches_data],
            },
            'completion_rates': {
                'labels': [data['batch'].name for data in batches_data],
                'data': [
                    (data['completed_count'] / data['relations_count'] * 100) 
                    if data['relations_count'] > 0 else 0
                    for data in batches_data
                ],
            },
            'participation': {
                'labels': [data['batch'].name for data in batches_data],
                'data': [data['total_records'] for data in batches_data],
            }
        }
        
        return chart_data


class EvaluationArchiveView(AdminRequiredMixin, ListView):
    """
    考评归档管理视图
    管理已完成考评的归档状态
    """
    model = EvaluationBatch
    template_name = 'admin/evaluation/history_archive.html'
    context_object_name = 'batches'
    paginate_by = 20
    
    def get_queryset(self):
        """获取可归档的批次"""
        # 只显示已完成且未归档的批次
        return EvaluationBatch.objects.filter(
            status='completed'
        ).select_related('default_template').order_by('-end_date')
    
    def post(self, request, *args, **kwargs):
        """处理归档操作"""
        action = request.POST.get('action')
        batch_ids = request.POST.getlist('batch_ids')
        
        if not batch_ids:
            messages.error(request, '请选择要操作的批次')
            return self.get(request, *args, **kwargs)
        
        batches = EvaluationBatch.objects.filter(id__in=batch_ids)
        
        if action == 'archive':
            return self._archive_batches(request, batches)
        elif action == 'export':
            return self._export_batches(request, batches)
        else:
            messages.error(request, '无效的操作')
            return self.get(request, *args, **kwargs)
    
    def _archive_batches(self, request, batches):
        """归档批次数据"""
        archived_count = 0
        
        for batch in batches:
            if batch.status == 'completed':
                # 标记为已归档（可以添加归档字段到模型）
                # 这里暂时使用软删除作为归档标记
                batch.soft_delete(deleted_by=request.current_staff.name)
                archived_count += 1
                
                # 记录归档操作
                AuditLog.objects.create(
                    user=request.current_staff.username,
                    action='archive',
                    target_model='EvaluationBatch',
                    target_id=batch.id,
                    description=f'归档考评批次：{batch.name}',
                    ip_address=request.META.get('REMOTE_ADDR'),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    extra_data={'batch_name': batch.name, 'batch_year': batch.year}
                )
        
        messages.success(request, f'成功归档 {archived_count} 个考评批次')
        return self.get(request, *args, **kwargs)
    
    def _export_batches(self, request, batches):
        """导出批次数据"""
        # 这里可以实现数据导出功能
        # 暂时返回一个简单的JSON响应
        export_data = []
        for batch in batches:
            export_data.append({
                'name': batch.name,
                'year': batch.year,
                'start_date': batch.start_date.isoformat() if batch.start_date else None,
                'end_date': batch.end_date.isoformat() if batch.end_date else None,
                'status': batch.status,
                'description': batch.description,
            })
        
        response = HttpResponse(
            json.dumps(export_data, ensure_ascii=False, indent=2),
            content_type='application/json'
        )
        response['Content-Disposition'] = 'attachment; filename="evaluation_batches_export.json"'
        
        # 记录导出操作
        AuditLog.objects.create(
            user=request.current_staff.username,
            action='export',
            target_model='EvaluationBatch',
            description=f'导出考评批次数据，共 {len(batches)} 个批次',
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            extra_data={'batch_count': len(batches), 'batch_ids': [b.id for b in batches]}
        )
        
        return response


class EvaluationHistoryAPIView(AdminRequiredMixin, ListView):
    """
    考评历史API视图
    为前端图表提供JSON数据
    """
    
    def get(self, request, *args, **kwargs):
        """返回历史数据的JSON格式"""
        data_type = request.GET.get('type', 'trends')
        
        if data_type == 'trends':
            return self._get_trends_data(request)
        elif data_type == 'department_stats':
            return self._get_department_stats(request)
        elif data_type == 'score_distribution':
            return self._get_score_distribution_data(request)
        else:
            return JsonResponse({'error': '不支持的数据类型'}, status=400)
    
    def _get_trends_data(self, request):
        """获取趋势数据"""
        # 最近12个月的考评趋势
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=365)
        
        # 按月统计考评批次数量
        from django.db.models import Count
        from django.db.models.functions import TruncMonth
        
        monthly_stats = EvaluationBatch.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).annotate(
            month=TruncMonth('created_at')
        ).values('month').annotate(
            batch_count=Count('id'),
            completed_count=Count('id', filter=Q(status='completed'))
        ).order_by('month')
        
        return JsonResponse({
            'success': True,
            'data': {
                'labels': [item['month'].strftime('%Y-%m') for item in monthly_stats],
                'datasets': [
                    {
                        'label': '总批次数',
                        'data': [item['batch_count'] for item in monthly_stats],
                        'backgroundColor': 'rgba(59, 130, 246, 0.1)',
                        'borderColor': 'rgb(59, 130, 246)',
                    },
                    {
                        'label': '已完成批次',
                        'data': [item['completed_count'] for item in monthly_stats],
                        'backgroundColor': 'rgba(34, 197, 94, 0.1)',
                        'borderColor': 'rgb(34, 197, 94)',
                    }
                ]
            }
        })
    
    def _get_department_stats(self, request):
        """获取部门统计数据"""
        # 各部门参与考评的统计
        dept_stats = Department.objects.annotate(
            total_evaluations=Count('staff__evaluationrelation_evaluatee_set'),
            completed_evaluations=Count(
                'staff__evaluationrelation_evaluatee_set',
                filter=Q(staff__evaluationrelation_evaluatee_set__status='completed')
            )
        ).filter(total_evaluations__gt=0)
        
        return JsonResponse({
            'success': True,
            'data': {
                'labels': [dept.name for dept in dept_stats],
                'datasets': [{
                    'label': '参与考评次数',
                    'data': [dept.total_evaluations for dept in dept_stats],
                    'backgroundColor': [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(251, 191, 36, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(168, 85, 247, 0.8)',
                        'rgba(236, 72, 153, 0.8)',
                    ]
                }]
            }
        })
    
    def _get_score_distribution_data(self, request):
        """获取分数分布数据"""
        # 所有评分记录的分数分布
        from django.db.models import Case, When, IntegerField, Count
        
        distribution = EvaluationRecord.objects.filter(
            status='submitted'
        ).aggregate(
            excellent=Count(Case(When(total_score__gte=90, then=1), output_field=IntegerField())),
            good=Count(Case(When(total_score__gte=80, total_score__lt=90, then=1), output_field=IntegerField())),
            average=Count(Case(When(total_score__gte=70, total_score__lt=80, then=1), output_field=IntegerField())),
            below_average=Count(Case(When(total_score__lt=70, then=1), output_field=IntegerField())),
        )
        
        return JsonResponse({
            'success': True,
            'data': {
                'labels': ['优秀(90+)', '良好(80-89)', '一般(70-79)', '待改进(<70)'],
                'datasets': [{
                    'data': [
                        distribution['excellent'],
                        distribution['good'],
                        distribution['average'],
                        distribution['below_average']
                    ],
                    'backgroundColor': [
                        'rgba(34, 197, 94, 0.8)',   # 绿色 - 优秀
                        'rgba(59, 130, 246, 0.8)',   # 蓝色 - 良好  
                        'rgba(251, 191, 36, 0.8)',   # 黄色 - 一般
                        'rgba(239, 68, 68, 0.8)',    # 红色 - 待改进
                    ]
                }]
            }
        })