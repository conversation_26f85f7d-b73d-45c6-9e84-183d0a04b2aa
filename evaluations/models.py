# -*- coding: utf-8 -*-
"""
考评业务模型
包含考评模板、批次、关系、记录、权重规则等核心业务
"""

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from common.models import BaseModel
import json


class EvaluationTemplate(BaseModel):
    """
    考评模板模型
    定义考评表单的结构和评分标准
    """
    TEMPLATE_TYPES = [
        ('structured', '结构化评分'),
        ('open', '开放式评分'),
        ('mixed', '混合式评分'),
    ]
    
    name = models.CharField(
        max_length=100,
        verbose_name='模板名称',
        help_text='考评模板的名称'
    )
    description = models.TextField(
        blank=True,
        verbose_name='模板描述',
        help_text='模板的详细描述和使用说明'
    )
    template_type = models.CharField(
        max_length=20,
        choices=TEMPLATE_TYPES,
        default='structured',
        verbose_name='模板类型',
        help_text='评分模式类型'
    )
    is_default = models.BooleanField(
        default=False,
        verbose_name='是否默认模板',
        help_text='系统默认使用的模板'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用',
        help_text='模板是否可用'
    )
    sort_order = models.PositiveIntegerField(
        default=0,
        verbose_name='排序',
        help_text='模板显示顺序'
    )
    
    class Meta:
        verbose_name = '考评模板'
        verbose_name_plural = '考评模板'
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name
    
    def get_items_count(self):
        """获取模板包含的评分项数量"""
        return self.evaluationitem_set.filter(is_active=True).count()
    
    def calculate_total_score(self):
        """计算模板总分"""
        return sum(item.max_score for item in self.evaluationitem_set.filter(is_active=True))


class EvaluationItem(BaseModel):
    """
    考评项模型
    模板中的具体评分项目
    """
    SCORING_MODES = [
        ('score', '数值评分'),
        ('level', '等级评分'),
        ('text', '文本评价'),
    ]
    
    template = models.ForeignKey(
        EvaluationTemplate,
        on_delete=models.CASCADE,
        verbose_name='所属模板',
        help_text='评分项所属的模板'
    )
    name = models.CharField(
        max_length=100,
        verbose_name='评分项名称',
        help_text='评分项的名称'
    )
    description = models.TextField(
        blank=True,
        verbose_name='评分说明',
        help_text='评分项的详细说明和标准'
    )
    scoring_mode = models.CharField(
        max_length=20,
        choices=SCORING_MODES,
        default='score',
        verbose_name='评分模式',
        help_text='该项目的评分方式'
    )
    max_score = models.PositiveIntegerField(
        default=10,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        verbose_name='最高分',
        help_text='该项目的最高得分'
    )
    weight = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=1.00,
        validators=[MinValueValidator(0.1), MaxValueValidator(5.0)],
        verbose_name='权重',
        help_text='该项目在总分中的权重'
    )
    is_required = models.BooleanField(
        default=True,
        verbose_name='是否必填',
        help_text='评分时是否必须填写'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用',
        help_text='评分项是否可用'
    )
    sort_order = models.PositiveIntegerField(
        default=0,
        verbose_name='排序',
        help_text='评分项显示顺序'
    )
    
    class Meta:
        verbose_name = '评分项'
        verbose_name_plural = '评分项'
        ordering = ['template', 'sort_order']
        unique_together = [['template', 'name']]
    
    def __str__(self):
        return f'{self.template.name} - {self.name}'


class ScoringTier(BaseModel):
    """
    评分等级模型
    定义等级评分的具体等级和分值
    """
    item = models.ForeignKey(
        EvaluationItem,
        on_delete=models.CASCADE,
        verbose_name='所属评分项',
        help_text='等级所属的评分项'
    )
    tier_name = models.CharField(
        max_length=50,
        verbose_name='等级名称',
        help_text='等级的名称（如：优秀、良好等）'
    )
    tier_value = models.PositiveIntegerField(
        verbose_name='等级分值',
        help_text='该等级对应的分值'
    )
    description = models.TextField(
        blank=True,
        verbose_name='等级描述',
        help_text='该等级的详细描述和标准'
    )
    sort_order = models.PositiveIntegerField(
        default=0,
        verbose_name='排序',
        help_text='等级显示顺序'
    )
    
    class Meta:
        verbose_name = '评分等级'
        verbose_name_plural = '评分等级'
        ordering = ['item', 'sort_order']
        unique_together = [['item', 'tier_name']]
    
    def __str__(self):
        return f'{self.item.name} - {self.tier_name}({self.tier_value}分)'


class WeightingRule(BaseModel):
    """
    权重规则模型
    定义智能分配中的权重计算规则
    """
    CONDITION_TYPES = [
        ('department', '部门关系'),
        ('position_level', '职位层级'),
        ('relation_type', '评价关系'),
        ('default', '默认规则'),
    ]
    
    RELATION_TYPES = [
        ('subordinate_to_superior', '下级评上级'),
        ('superior_to_subordinate', '上级评下级'),
        ('peer_to_peer', '同级互评'),
        ('cross_department', '跨部门评价'),
    ]
    
    name = models.CharField(
        max_length=100,
        verbose_name='规则名称',
        help_text='权重规则的名称'
    )
    description = models.TextField(
        blank=True,
        verbose_name='规则描述',
        help_text='规则的详细说明'
    )
    condition_type = models.CharField(
        max_length=20,
        choices=CONDITION_TYPES,
        verbose_name='条件类型',
        help_text='规则匹配的条件类型'
    )
    condition_value = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='条件值',
        help_text='具体的条件值'
    )
    relation_type = models.CharField(
        max_length=30,
        choices=RELATION_TYPES,
        blank=True,
        verbose_name='关系类型',
        help_text='评价者与被评价者的关系'
    )
    weight_factor = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=1.00,
        validators=[MinValueValidator(0.1), MaxValueValidator(5.0)],
        verbose_name='权重系数',
        help_text='该规则的权重系数'
    )
    priority = models.PositiveIntegerField(
        default=1,
        verbose_name='优先级',
        help_text='规则匹配的优先级（数字越大优先级越高）'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用',
        help_text='规则是否生效'
    )
    
    class Meta:
        verbose_name = '权重规则'
        verbose_name_plural = '权重规则'
        ordering = ['-priority', 'name']
    
    def __str__(self):
        return f'{self.name} (权重: {self.weight_factor})'
    
    def matches_condition(self, evaluator, evaluatee):
        """检查规则是否匹配给定的评价关系"""
        if not self.is_active:
            return False
        
        if self.condition_type == 'department':
            return str(evaluator.department_id) == self.condition_value
        elif self.condition_type == 'position_level':
            return str(evaluator.position.level) == self.condition_value
        elif self.condition_type == 'relation_type':
            return self.relation_type == self.get_relation_type(evaluator, evaluatee)
        elif self.condition_type == 'default':
            return True
        
        return False
    
    def get_relation_type(self, evaluator, evaluatee):
        """判断评价者与被评价者的关系类型"""
        if evaluator.department != evaluatee.department:
            return 'cross_department'
        
        eval_level = evaluator.position.level if evaluator.position else 0
        evaled_level = evaluatee.position.level if evaluatee.position else 0
        
        if eval_level < evaled_level:
            return 'subordinate_to_superior'
        elif eval_level > evaled_level:
            return 'superior_to_subordinate'
        else:
            return 'peer_to_peer'


class BatchTemplate(BaseModel):
    """
    批次模板关联模型
    定义批次可用的模板集合和关系类型映射
    """
    batch = models.ForeignKey(
        'EvaluationBatch',
        on_delete=models.CASCADE,
        verbose_name='所属批次',
        help_text='关联的考评批次'
    )
    template = models.ForeignKey(
        EvaluationTemplate,
        on_delete=models.CASCADE,
        verbose_name='可用模板',
        help_text='该批次可使用的模板'
    )
    relation_type = models.CharField(
        max_length=30,
        choices=[
            ('subordinate_to_superior', '下级评上级'),
            ('superior_to_subordinate', '上级评下级'),
            ('peer_to_peer', '同级互评'),
            ('cross_department', '跨部门评价'),
            ('all', '通用模板'),
        ],
        default='all',
        verbose_name='适用关系类型',
        help_text='该模板适用的评价关系类型'
    )
    is_preferred = models.BooleanField(
        default=False,
        verbose_name='是否首选',
        help_text='是否为该关系类型的首选模板'
    )
    sort_order = models.PositiveIntegerField(
        default=0,
        verbose_name='排序',
        help_text='模板在批次中的显示顺序'
    )
    
    class Meta:
        verbose_name = '批次模板'
        verbose_name_plural = '批次模板'
        unique_together = ['batch', 'template']
        ordering = ['sort_order', 'template__name']
    
    def __str__(self):
        return f'{self.batch.name} - {self.template.name} ({self.get_relation_type_display()})'


class EvaluationBatch(BaseModel):
    """
    考评批次模型
    管理考评活动的批次信息
    """
    BATCH_STATUS = [
        ('draft', '草稿'),
        ('active', '进行中'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
    ]
    
    name = models.CharField(
        max_length=100,
        verbose_name='批次名称',
        help_text='考评批次的名称'
    )
    description = models.TextField(
        blank=True,
        verbose_name='批次描述',
        help_text='考评批次的详细说明'
    )
    default_template = models.ForeignKey(
        EvaluationTemplate,
        on_delete=models.CASCADE,
        related_name='default_batches',
        verbose_name='默认模板',
        help_text='该批次默认使用的考评模板'
    )
    start_date = models.DateTimeField(
        verbose_name='开始时间',
        help_text='考评开始时间'
    )
    end_date = models.DateTimeField(
        verbose_name='结束时间',
        help_text='考评结束时间'
    )
    status = models.CharField(
        max_length=20,
        choices=BATCH_STATUS,
        default='draft',
        verbose_name='批次状态',
        help_text='当前批次的状态'
    )
    auto_assign = models.BooleanField(
        default=True,
        verbose_name='自动分配',
        help_text='是否使用智能分配算法'
    )
    allow_self_evaluation = models.BooleanField(
        default=False,
        verbose_name='允许自评',
        help_text='是否允许员工进行自我评价'
    )
    anonymous_results = models.BooleanField(
        default=True,
        verbose_name='匿名结果',
        help_text='评价结果是否对被评价者匿名'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用',
        help_text='批次是否启用，禁用后不会在列表中显示'
    )
    templates = models.ManyToManyField(
        EvaluationTemplate,
        through='BatchTemplate',
        related_name='available_batches',
        verbose_name='可用模板',
        help_text='该批次可使用的考评模板集合'
    )
    
    class Meta:
        verbose_name = '考评批次'
        verbose_name_plural = '考评批次'
        ordering = ['-start_date']
    
    def __str__(self):
        return f'{self.name} ({self.get_status_display()})'
    
    def is_batch_active(self):
        """检查批次是否处于活跃状态"""
        now = timezone.now()
        return (self.status == 'active' and 
                self.start_date <= now <= self.end_date)
    
    def get_available_templates(self, relation_type=None):
        """获取批次可用的模板"""
        queryset = self.batchtemplate_set.filter(template__is_active=True)
        if relation_type:
            queryset = queryset.filter(
                models.Q(relation_type=relation_type) | 
                models.Q(relation_type='all')
            )
        return queryset.order_by('sort_order', 'template__name')
    
    def get_preferred_template(self, relation_type):
        """获取指定关系类型的首选模板"""
        # 优先查找特定关系类型的首选模板
        preferred = self.batchtemplate_set.filter(
            relation_type=relation_type,
            is_preferred=True,
            template__is_active=True
        ).first()
        
        if preferred:
            return preferred.template
            
        # 如果没有首选，查找通用首选模板
        general_preferred = self.batchtemplate_set.filter(
            relation_type='all',
            is_preferred=True,
            template__is_active=True
        ).first()
        
        if general_preferred:
            return general_preferred.template
            
        # 如果都没有，返回该关系类型的第一个可用模板
        available = self.get_available_templates(relation_type).first()
        return available.template if available else None
    
    def get_progress(self):
        """获取批次完成进度"""
        total_relations = self.evaluationrelation_set.count()
        completed_relations = self.evaluationrelation_set.filter(
            evaluationrecord__isnull=False
        ).distinct().count()
        
        if total_relations == 0:
            return 0
        return round((completed_relations / total_relations) * 100, 2)
    
    def get_participants_count(self):
        """获取参与人数统计"""
        evaluators = self.evaluationrelation_set.values_list('evaluator', flat=True).distinct()
        evaluatees = self.evaluationrelation_set.values_list('evaluatee', flat=True).distinct()
        return {
            'evaluators': len(evaluators),
            'evaluatees': len(evaluatees),
            'total': len(set(evaluators) | set(evaluatees))
        }
    
    def get_relations_count(self):
        """获取考评关系总数"""
        return self.evaluationrelation_set.count()
    
    def get_completed_count(self):
        """获取已完成的考评关系数"""
        return self.evaluationrelation_set.filter(
            evaluationrecord__isnull=False
        ).count()
    
    def get_completion_rate(self):
        """获取完成率"""
        total = self.get_relations_count()
        if total == 0:
            return 0
        completed = self.get_completed_count()
        return (completed / total) * 100
    
    @property
    def year(self):
        """获取批次年份"""
        return self.start_date.year if self.start_date else None


class EvaluationRelation(BaseModel):
    """
    考评关系模型
    定义评价者与被评价者的关系
    """
    batch = models.ForeignKey(
        EvaluationBatch,
        on_delete=models.CASCADE,
        verbose_name='所属批次',
        help_text='考评关系所属的批次'
    )
    evaluator = models.ForeignKey(
        'organizations.Staff',
        on_delete=models.CASCADE,
        related_name='evaluator_relations',
        verbose_name='评价者',
        help_text='执行评价的员工'
    )
    evaluatee = models.ForeignKey(
        'organizations.Staff',
        on_delete=models.CASCADE,
        related_name='evaluatee_relations',
        verbose_name='被评价者',
        help_text='接受评价的员工'
    )
    template = models.ForeignKey(
        EvaluationTemplate,
        on_delete=models.CASCADE,
        verbose_name='使用模板',
        help_text='该关系使用的考评模板'
    )
    weight_factor = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=1.00,
        validators=[MinValueValidator(0.1), MaxValueValidator(5.0)],
        verbose_name='权重系数',
        help_text='该关系的权重系数'
    )
    is_assigned = models.BooleanField(
        default=False,
        verbose_name='是否已分配',
        help_text='关系是否已分配给评价者'
    )
    assignment_method = models.CharField(
        max_length=20,
        choices=[
            ('auto', '自动分配'),
            ('manual', '手动分配'),
        ],
        default='auto',
        verbose_name='分配方式',
        help_text='关系的分配方式'
    )
    
    class Meta:
        verbose_name = '考评关系'
        verbose_name_plural = '考评关系'
        unique_together = [['batch', 'evaluator', 'evaluatee']]
        ordering = ['batch', 'evaluator', 'evaluatee']
    
    def __str__(self):
        return f'{self.evaluator.name} 评价 {self.evaluatee.name}'
    
    def is_completed(self):
        """检查该关系是否已完成评价"""
        return hasattr(self, 'evaluationrecord')
    
    def get_relation_type(self):
        """获取关系类型"""
        if self.evaluator.department != self.evaluatee.department:
            return 'cross_department'
        
        eval_level = self.evaluator.position.level if self.evaluator.position else 0
        evaled_level = self.evaluatee.position.level if self.evaluatee.position else 0
        
        if eval_level < evaled_level:
            return 'subordinate_to_superior'
        elif eval_level > evaled_level:
            return 'superior_to_subordinate'
        else:
            return 'peer_to_peer'


class EvaluationRecord(BaseModel):
    """
    考评记录模型
    存储具体的评价结果
    """
    relation = models.OneToOneField(
        EvaluationRelation,
        on_delete=models.CASCADE,
        verbose_name='考评关系',
        help_text='对应的考评关系'
    )
    total_score = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='总得分',
        help_text='加权计算后的总分'
    )
    raw_score = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='原始得分',
        help_text='未加权的原始总分'
    )
    completion_time = models.DateTimeField(
        verbose_name='完成时间',
        help_text='评价完成的时间'
    )
    time_spent = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='用时（秒）',
        help_text='完成评价花费的时间'
    )
    comments = models.TextField(
        blank=True,
        verbose_name='综合评价',
        help_text='对被评价者的综合评价意见'
    )
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name='IP地址',
        help_text='提交评价时的IP地址'
    )
    
    class Meta:
        verbose_name = '考评记录'
        verbose_name_plural = '考评记录'
        ordering = ['-completion_time']
    
    def __str__(self):
        return f'{self.relation} - {self.total_score}分'
    
    def calculate_scores(self):
        """计算总分和原始分"""
        item_scores = self.evaluationitemrecord_set.all()
        self.raw_score = sum(score.score for score in item_scores)
        self.total_score = self.raw_score * self.relation.weight_factor
        self.save(update_fields=['raw_score', 'total_score'])


class EvaluationItemRecord(BaseModel):
    """
    评分项记录模型
    存储具体评分项的得分
    """
    record = models.ForeignKey(
        EvaluationRecord,
        on_delete=models.CASCADE,
        verbose_name='考评记录',
        help_text='所属的考评记录'
    )
    item = models.ForeignKey(
        EvaluationItem,
        on_delete=models.CASCADE,
        verbose_name='评分项',
        help_text='对应的评分项'
    )
    score = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='得分',
        help_text='该项目的得分'
    )
    tier = models.ForeignKey(
        ScoringTier,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='选择等级',
        help_text='如果是等级评分，选择的等级'
    )
    text_value = models.TextField(
        blank=True,
        verbose_name='文本评价',
        help_text='如果是文本评价，填写的内容'
    )
    
    class Meta:
        verbose_name = '评分项记录'
        verbose_name_plural = '评分项记录'
        unique_together = [['record', 'item']]
        ordering = ['record', 'item__sort_order']
    
    def __str__(self):
        return f'{self.record.relation} - {self.item.name}: {self.score}'
