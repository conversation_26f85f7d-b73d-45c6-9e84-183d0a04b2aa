# -*- coding: utf-8 -*-
"""
简化权重管理服务
提供预设权重方案，简化权重规则配置
"""

from django.db import transaction
from django.utils import timezone
from typing import Dict, List, Optional
import logging

from ..models import WeightingRule, EvaluationBatch
from organizations.models import Staff

logger = logging.getLogger(__name__)


class SimplifiedWeightingService:
    """简化权重管理服务"""
    
    # 预设权重方案
    PRESET_SCHEMES = {
        'balanced': {
            'name': '平衡模式',
            'description': '所有评价关系权重相等，适合民主化考评环境',
            'icon': '⚖️',
            'suitable_for': '扁平化组织、创新型团队',
            'rules': {
                'subordinate_to_superior': 1.0,
                'superior_to_subordinate': 1.0,
                'peer_to_peer': 1.0,
                'cross_department': 1.0
            },
            'advantages': ['公平公正', '减少层级偏见', '鼓励平等交流'],
            'disadvantages': ['可能忽略管理权威', '不适合严格层级制']
        },
        'hierarchical': {
            'name': '层级模式',
            'description': '体现管理层级权威，适合传统企业管理',
            'icon': '🏢',
            'suitable_for': '传统企业、政府机构、大型公司',
            'rules': {
                'subordinate_to_superior': 0.8,
                'superior_to_subordinate': 1.2,
                'peer_to_peer': 1.0,
                'cross_department': 0.9
            },
            'advantages': ['体现管理权威', '符合传统管理理念', '决策效率高'],
            'disadvantages': ['可能产生权力偏见', '下级意见权重较低']
        },
        'collaborative': {
            'name': '协作模式',
            'description': '强调团队协作和跨部门合作',
            'icon': '🤝',
            'suitable_for': '项目型组织、矩阵式管理、敏捷团队',
            'rules': {
                'subordinate_to_superior': 1.1,
                'superior_to_subordinate': 1.0,
                'peer_to_peer': 1.2,
                'cross_department': 1.1
            },
            'advantages': ['鼓励团队合作', '重视同级意见', '促进跨部门协作'],
            'disadvantages': ['管理权威相对弱化', '可能影响决策速度']
        },
        'performance_driven': {
            'name': '绩效导向',
            'description': '基于绩效表现调整权重，激励高绩效',
            'icon': '📈',
            'suitable_for': '销售团队、业绩导向型组织',
            'rules': {
                'subordinate_to_superior': 0.9,
                'superior_to_subordinate': 1.3,
                'peer_to_peer': 1.1,
                'cross_department': 1.0
            },
            'advantages': ['激励高绩效', '强化结果导向', '提升竞争力'],
            'disadvantages': ['可能忽视过程管理', '压力较大']
        }
    }
    
    def __init__(self, batch: EvaluationBatch, operator: Staff):
        """
        初始化简化权重服务
        
        Args:
            batch: 考评批次
            operator: 操作人员
        """
        self.batch = batch
        self.operator = operator
    
    @classmethod
    def get_scheme_info(cls, scheme_name: str) -> Optional[Dict]:
        """获取权重方案信息"""
        return cls.PRESET_SCHEMES.get(scheme_name)
    
    @classmethod
    def list_all_schemes(cls) -> Dict:
        """列出所有可用的权重方案"""
        return cls.PRESET_SCHEMES
    
    @transaction.atomic
    def apply_preset_scheme(self, scheme_name: str, custom_adjustments: Optional[Dict] = None) -> Dict:
        """
        应用预设权重方案
        
        Args:
            scheme_name: 方案名称
            custom_adjustments: 自定义调整 {'relation_type': weight_factor}
            
        Returns:
            应用结果
        """
        try:
            if scheme_name not in self.PRESET_SCHEMES:
                return {'success': False, 'error': f'未知的权重方案: {scheme_name}'}
            
            scheme = self.PRESET_SCHEMES[scheme_name]
            
            # 清除现有的系统生成权重规则
            self._clear_system_rules()
            
            # 应用新的权重规则
            rules_created = 0
            final_rules = scheme['rules'].copy()
            
            # 应用自定义调整
            if custom_adjustments:
                final_rules.update(custom_adjustments)
            
            for relation_type, weight_factor in final_rules.items():
                rule = WeightingRule.objects.create(
                    name=f"{scheme['name']}_{relation_type}",
                    description=f"系统生成的{scheme['name']}权重规则 - {relation_type}",
                    condition_type='relation_type',
                    relation_type=relation_type,
                    weight_factor=weight_factor,
                    priority=10,  # 系统规则优先级较低
                    is_active=True,
                    created_by=self.operator.username
                )
                rules_created += 1
                
                logger.info(f"创建权重规则: {rule.name}, 权重: {weight_factor}")
            
            # 记录应用历史
            self._log_scheme_application(scheme_name, rules_created)
            
            return {
                'success': True,
                'scheme_applied': scheme_name,
                'rules_created': rules_created,
                'scheme_info': scheme,
                'final_rules': final_rules
            }
            
        except Exception as e:
            logger.error(f"应用权重方案失败: {str(e)}")
            return {'success': False, 'error': f'应用失败: {str(e)}'}
    
    def preview_scheme_impact(self, scheme_name: str) -> Dict:
        """
        预览权重方案的影响
        
        Args:
            scheme_name: 方案名称
            
        Returns:
            影响预览数据
        """
        if scheme_name not in self.PRESET_SCHEMES:
            return {'error': '未知的权重方案'}
        
        scheme = self.PRESET_SCHEMES[scheme_name]
        
        # 计算当前批次的评价关系统计
        from ..models import EvaluationRelation
        relations = EvaluationRelation.objects.filter(
            batch=self.batch,
            deleted_at__isnull=True
        )
        
        # 统计各种关系类型的数量
        relation_stats = {}
        for relation in relations:
            rel_type = self._determine_relation_type(relation.evaluator, relation.evaluatee)
            relation_stats[rel_type] = relation_stats.get(rel_type, 0) + 1
        
        # 计算权重影响
        weight_impact = {}
        for rel_type, count in relation_stats.items():
            old_weight = 1.0  # 假设当前权重为1.0
            new_weight = scheme['rules'].get(rel_type, 1.0)
            impact = (new_weight - old_weight) * count
            weight_impact[rel_type] = {
                'count': count,
                'old_weight': old_weight,
                'new_weight': new_weight,
                'impact': impact
            }
        
        return {
            'scheme_info': scheme,
            'relation_stats': relation_stats,
            'weight_impact': weight_impact,
            'total_relations': sum(relation_stats.values())
        }
    
    def get_current_scheme(self) -> Optional[str]:
        """获取当前使用的权重方案"""
        # 分析当前权重规则，尝试匹配预设方案
        current_rules = WeightingRule.objects.filter(
            is_active=True,
            deleted_at__isnull=True
        ).values('relation_type', 'weight_factor')
        
        current_weights = {rule['relation_type']: float(rule['weight_factor']) for rule in current_rules}
        
        # 匹配预设方案
        for scheme_name, scheme in self.PRESET_SCHEMES.items():
            if self._weights_match(current_weights, scheme['rules']):
                return scheme_name
        
        return None  # 自定义权重配置
    
    def _clear_system_rules(self):
        """清除系统生成的权重规则"""
        WeightingRule.objects.filter(
            name__contains='系统生成',
            is_active=True
        ).update(
            is_active=False,
            updated_by=self.operator.username,
            updated_at=timezone.now()
        )
    
    def _determine_relation_type(self, evaluator: Staff, evaluatee: Staff) -> str:
        """确定评价关系类型"""
        if evaluator.department_id != evaluatee.department_id:
            return 'cross_department'
        
        if not evaluator.position or not evaluatee.position:
            return 'peer_to_peer'
        
        evaluator_level = evaluator.position.level
        evaluatee_level = evaluatee.position.level
        
        if evaluator_level < evaluatee_level:
            return 'subordinate_to_superior'
        elif evaluator_level > evaluatee_level:
            return 'superior_to_subordinate'
        else:
            return 'peer_to_peer'
    
    def _weights_match(self, weights1: Dict, weights2: Dict, tolerance: float = 0.1) -> bool:
        """检查两个权重配置是否匹配"""
        for key in weights2:
            if abs(weights1.get(key, 1.0) - weights2[key]) > tolerance:
                return False
        return True
    
    def _log_scheme_application(self, scheme_name: str, rules_created: int):
        """记录方案应用日志"""
        from common.models import AuditLog
        
        AuditLog.objects.create(
            user=self.operator.username,
            action='apply_weight_scheme',
            target_model='evaluationbatch',
            target_id=self.batch.id,
            description=f'应用权重方案: {scheme_name}',
            extra_data={
                'batch_name': self.batch.name,
                'scheme_name': scheme_name,
                'rules_created': rules_created,
                'operator': self.operator.name
            }
        )


class WeightingWizardService:
    """权重配置向导服务"""
    
    @staticmethod
    def generate_wizard_steps(batch: EvaluationBatch) -> List[Dict]:
        """生成向导步骤"""
        return [
            {
                'step': 1,
                'title': '选择权重方案',
                'description': '选择适合您组织的权重配置方案',
                'type': 'scheme_selection'
            },
            {
                'step': 2,
                'title': '预览影响',
                'description': '查看方案对当前考评批次的影响',
                'type': 'impact_preview'
            },
            {
                'step': 3,
                'title': '微调权重（可选）',
                'description': '根据需要对权重进行细微调整',
                'type': 'fine_tuning'
            },
            {
                'step': 4,
                'title': '确认应用',
                'description': '确认配置并应用到考评批次',
                'type': 'confirmation'
            }
        ]
    
    @staticmethod
    def validate_custom_weights(weights: Dict) -> Dict:
        """验证自定义权重配置"""
        errors = []
        
        for relation_type, weight in weights.items():
            try:
                weight_float = float(weight)
                if weight_float < 0.1 or weight_float > 5.0:
                    errors.append(f'{relation_type}: 权重必须在0.1-5.0之间')
            except (ValueError, TypeError):
                errors.append(f'{relation_type}: 权重必须是有效数字')
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
