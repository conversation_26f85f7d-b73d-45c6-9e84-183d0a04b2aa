# -*- coding: utf-8 -*-
"""
简化分配策略服务
提供预设分配策略，简化智能分配配置
"""

from django.db import transaction
from django.db.models import Q
from typing import Dict, List, Optional, Tuple
import logging

from ..models import EvaluationBatch, EvaluationRelation
from organizations.models import Staff, Department, Position

logger = logging.getLogger(__name__)


class AssignmentStrategyService:
    """分配策略服务"""
    
    # 预设分配策略
    STRATEGIES = {
        'simple': {
            'name': '简单分配',
            'description': '每人评价直接上级和同级同事，适合小团队',
            'icon': '🎯',
            'complexity': '简单',
            'avg_evaluators': '3-5人',
            'suitable_for': '小团队、扁平化组织、快速评估',
            'rules': {
                'include_superior': True,
                'include_peers': True,
                'include_subordinates': False,
                'cross_department': False,
                'include_gm_office': False,
                'max_evaluators_per_person': 5,
                'min_evaluators_per_person': 2,
                'peer_selection_method': 'same_level',  # same_level, random, all
                'weight_scheme': 'balanced'
            },
            'advantages': ['配置简单', '评价负担轻', '适合快速反馈'],
            'disadvantages': ['评价维度有限', '缺少下级视角']
        },
        'comprehensive': {
            'name': '全面分配',
            'description': '360度评价，包含上级、同级、下级，适合正式考核',
            'icon': '🔄',
            'complexity': '复杂',
            'avg_evaluators': '8-12人',
            'suitable_for': '正式考核、年度评估、重要岗位评价',
            'rules': {
                'include_superior': True,
                'include_peers': True,
                'include_subordinates': True,
                'cross_department': True,
                'include_gm_office': True,
                'max_evaluators_per_person': 12,
                'min_evaluators_per_person': 5,
                'peer_selection_method': 'all',
                'weight_scheme': 'hierarchical'
            },
            'advantages': ['评价全面', '多角度反馈', '权威性强'],
            'disadvantages': ['评价负担重', '配置复杂', '耗时较长']
        },
        'peer_focused': {
            'name': '同级互评',
            'description': '主要由同级同事进行评价，适合协作型团队',
            'icon': '🤝',
            'complexity': '中等',
            'avg_evaluators': '6-8人',
            'suitable_for': '项目团队、协作型组织、同级评估',
            'rules': {
                'include_superior': False,
                'include_peers': True,
                'include_subordinates': False,
                'cross_department': True,
                'include_gm_office': False,
                'max_evaluators_per_person': 8,
                'min_evaluators_per_person': 3,
                'peer_selection_method': 'all',
                'weight_scheme': 'collaborative'
            },
            'advantages': ['同级视角', '减少权力偏见', '促进协作'],
            'disadvantages': ['缺少管理视角', '权威性相对较低']
        },
        'management_focused': {
            'name': '管理导向',
            'description': '重点关注管理层评价，适合层级化组织',
            'icon': '👔',
            'complexity': '中等',
            'avg_evaluators': '4-6人',
            'suitable_for': '传统企业、层级化组织、管理评估',
            'rules': {
                'include_superior': True,
                'include_peers': False,
                'include_subordinates': True,
                'cross_department': False,
                'include_gm_office': True,
                'max_evaluators_per_person': 6,
                'min_evaluators_per_person': 2,
                'peer_selection_method': 'same_level',
                'weight_scheme': 'hierarchical'
            },
            'advantages': ['突出管理权威', '层级清晰', '决策效率高'],
            'disadvantages': ['同级交流少', '可能存在权力偏见']
        },
        'custom': {
            'name': '自定义策略',
            'description': '完全自定义分配规则，适合特殊需求',
            'icon': '⚙️',
            'complexity': '高级',
            'avg_evaluators': '可配置',
            'suitable_for': '特殊组织结构、复杂评估需求',
            'rules': {
                # 自定义规则将在配置时设定
            },
            'advantages': ['完全灵活', '适应性强', '满足特殊需求'],
            'disadvantages': ['配置复杂', '需要专业知识', '容易出错']
        }
    }
    
    def __init__(self, batch: EvaluationBatch, operator: Staff):
        """
        初始化分配策略服务
        
        Args:
            batch: 考评批次
            operator: 操作人员
        """
        self.batch = batch
        self.operator = operator
    
    @classmethod
    def get_strategy_info(cls, strategy_name: str) -> Optional[Dict]:
        """获取策略信息"""
        return cls.STRATEGIES.get(strategy_name)
    
    @classmethod
    def list_all_strategies(cls) -> Dict:
        """列出所有可用的分配策略"""
        return cls.STRATEGIES
    
    @transaction.atomic
    def execute_strategy(self, strategy_name: str, custom_rules: Optional[Dict] = None) -> Dict:
        """
        执行分配策略
        
        Args:
            strategy_name: 策略名称
            custom_rules: 自定义规则（用于覆盖默认规则）
            
        Returns:
            执行结果
        """
        try:
            if strategy_name not in self.STRATEGIES:
                return {'success': False, 'error': f'未知的分配策略: {strategy_name}'}
            
            strategy = self.STRATEGIES[strategy_name]
            rules = strategy['rules'].copy()
            
            # 应用自定义规则
            if custom_rules:
                rules.update(custom_rules)
            
            # 清除现有关系
            self._clear_existing_relations()
            
            # 获取参与人员
            participants = self._get_eligible_participants()
            
            # 执行分配
            relations_created = 0
            errors = []
            assignment_summary = {}
            
            for evaluatee in participants:
                try:
                    evaluators = self._find_evaluators_for_strategy(evaluatee, rules)
                    evaluatee_relations = 0
                    
                    for evaluator, relation_type in evaluators:
                        if self._create_evaluation_relation(evaluator, evaluatee, relation_type):
                            relations_created += 1
                            evaluatee_relations += 1
                    
                    assignment_summary[evaluatee.name] = {
                        'evaluators_count': evaluatee_relations,
                        'department': evaluatee.department.name if evaluatee.department else '未分配'
                    }
                    
                except Exception as e:
                    error_msg = f"为 {evaluatee.name} 分配评价者时出错: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)
            
            # 应用权重方案
            if 'weight_scheme' in rules:
                self._apply_weight_scheme(rules['weight_scheme'])
            
            # 记录分配结果
            self._log_strategy_execution(strategy_name, relations_created, len(errors))
            
            return {
                'success': True,
                'strategy_applied': strategy_name,
                'relations_created': relations_created,
                'participants_count': len(participants),
                'errors': errors,
                'assignment_summary': assignment_summary,
                'strategy_info': strategy
            }
            
        except Exception as e:
            logger.error(f"执行分配策略失败: {str(e)}")
            return {'success': False, 'error': f'执行失败: {str(e)}'}
    
    def preview_strategy_impact(self, strategy_name: str, custom_rules: Optional[Dict] = None) -> Dict:
        """
        预览分配策略的影响
        
        Args:
            strategy_name: 策略名称
            custom_rules: 自定义规则
            
        Returns:
            影响预览数据
        """
        if strategy_name not in self.STRATEGIES:
            return {'error': '未知的分配策略'}
        
        strategy = self.STRATEGIES[strategy_name]
        rules = strategy['rules'].copy()
        
        if custom_rules:
            rules.update(custom_rules)
        
        # 获取参与人员
        participants = self._get_eligible_participants()
        
        # 模拟分配过程
        preview_data = {
            'strategy_info': strategy,
            'participants_count': len(participants),
            'estimated_relations': 0,
            'department_distribution': {},
            'evaluator_load': {},
            'coverage_analysis': {}
        }
        
        total_relations = 0
        evaluator_counts = {}
        
        for evaluatee in participants:
            evaluators = self._find_evaluators_for_strategy(evaluatee, rules)
            evaluators_count = len(evaluators)
            total_relations += evaluators_count
            
            # 统计部门分布
            dept_name = evaluatee.department.name if evaluatee.department else '未分配'
            if dept_name not in preview_data['department_distribution']:
                preview_data['department_distribution'][dept_name] = {
                    'evaluatees': 0,
                    'avg_evaluators': 0
                }
            preview_data['department_distribution'][dept_name]['evaluatees'] += 1
            preview_data['department_distribution'][dept_name]['avg_evaluators'] += evaluators_count
            
            # 统计评价者负担
            for evaluator, _ in evaluators:
                evaluator_name = evaluator.name
                evaluator_counts[evaluator_name] = evaluator_counts.get(evaluator_name, 0) + 1
        
        # 计算平均值
        for dept_data in preview_data['department_distribution'].values():
            if dept_data['evaluatees'] > 0:
                dept_data['avg_evaluators'] = round(dept_data['avg_evaluators'] / dept_data['evaluatees'], 1)
        
        preview_data['estimated_relations'] = total_relations
        preview_data['evaluator_load'] = evaluator_counts
        preview_data['avg_evaluators_per_person'] = round(total_relations / len(participants), 1) if participants else 0
        
        return preview_data
    
    def _find_evaluators_for_strategy(self, evaluatee: Staff, rules: Dict) -> List[Tuple[Staff, str]]:
        """根据策略规则为指定员工找到评价者"""
        evaluators = []
        
        # 获取基础信息
        evaluatee_dept = evaluatee.department
        evaluatee_position = evaluatee.position
        
        if not evaluatee_dept or not evaluatee_position:
            logger.warning(f"员工 {evaluatee.name} 缺少部门或职位信息")
            return evaluators
        
        # 上级评价
        if rules.get('include_superior', False):
            superiors = self._find_superiors(evaluatee)
            evaluators.extend([(sup, 'superior_to_subordinate') for sup in superiors])
        
        # 同级评价
        if rules.get('include_peers', False):
            peers = self._find_peers(evaluatee, rules.get('peer_selection_method', 'same_level'))
            evaluators.extend([(peer, 'peer_to_peer') for peer in peers])
        
        # 下级评价
        if rules.get('include_subordinates', False):
            subordinates = self._find_subordinates(evaluatee)
            evaluators.extend([(sub, 'subordinate_to_superior') for sub in subordinates])
        
        # 跨部门评价
        if rules.get('cross_department', False):
            cross_dept_evaluators = self._find_cross_department_evaluators(evaluatee)
            evaluators.extend([(eval, 'cross_department') for eval in cross_dept_evaluators])
        
        # 总经理室评价
        if rules.get('include_gm_office', False):
            gm_office_staff = self._find_gm_office_staff(evaluatee)
            evaluators.extend([(gm, 'superior_to_subordinate') for gm in gm_office_staff])
        
        # 去重并限制数量
        unique_evaluators = []
        seen_ids = set()
        for evaluator, relation_type in evaluators:
            if evaluator.id not in seen_ids and evaluator.id != evaluatee.id:
                unique_evaluators.append((evaluator, relation_type))
                seen_ids.add(evaluator.id)
        
        # 应用数量限制
        max_evaluators = rules.get('max_evaluators_per_person', 10)
        min_evaluators = rules.get('min_evaluators_per_person', 2)
        
        if len(unique_evaluators) > max_evaluators:
            unique_evaluators = unique_evaluators[:max_evaluators]
        
        if len(unique_evaluators) < min_evaluators:
            logger.warning(f"员工 {evaluatee.name} 的评价者数量 ({len(unique_evaluators)}) 少于最小要求 ({min_evaluators})")
        
        return unique_evaluators
    
    def _find_superiors(self, staff: Staff) -> List[Staff]:
        """查找上级"""
        if not staff.position:
            return []
        
        # 查找同部门更高级别的员工
        superiors = Staff.objects.filter(
            department=staff.department,
            position__level__gt=staff.position.level,
            deleted_at__isnull=True
        ).order_by('-position__level')[:2]  # 最多2个直接上级
        
        return list(superiors)
    
    def _find_peers(self, staff: Staff, selection_method: str) -> List[Staff]:
        """查找同级"""
        if not staff.position:
            return []
        
        base_query = Staff.objects.filter(
            department=staff.department,
            position__level=staff.position.level,
            deleted_at__isnull=True
        ).exclude(id=staff.id)
        
        if selection_method == 'same_level':
            return list(base_query[:4])  # 最多4个同级
        elif selection_method == 'all':
            return list(base_query)
        elif selection_method == 'random':
            return list(base_query.order_by('?')[:3])  # 随机3个
        
        return []
    
    def _find_subordinates(self, staff: Staff) -> List[Staff]:
        """查找下级"""
        if not staff.position:
            return []
        
        subordinates = Staff.objects.filter(
            department=staff.department,
            position__level__lt=staff.position.level,
            deleted_at__isnull=True
        ).order_by('position__level')[:3]  # 最多3个下级
        
        return list(subordinates)
    
    def _find_cross_department_evaluators(self, staff: Staff) -> List[Staff]:
        """查找跨部门评价者"""
        # 查找其他部门的同级或相近级别员工
        if not staff.position:
            return []
        
        cross_dept_staff = Staff.objects.filter(
            position__level__in=[staff.position.level - 1, staff.position.level, staff.position.level + 1],
            deleted_at__isnull=True
        ).exclude(
            department=staff.department
        ).exclude(
            id=staff.id
        )[:2]  # 最多2个跨部门评价者
        
        return list(cross_dept_staff)
    
    def _find_gm_office_staff(self, staff: Staff) -> List[Staff]:
        """查找总经理室人员"""
        gm_office = Department.objects.filter(name__contains='总经理').first()
        if not gm_office:
            return []
        
        gm_staff = Staff.objects.filter(
            department=gm_office,
            deleted_at__isnull=True
        ).exclude(id=staff.id)[:1]  # 最多1个总经理室人员
        
        return list(gm_staff)
    
    def _create_evaluation_relation(self, evaluator: Staff, evaluatee: Staff, relation_type: str) -> bool:
        """创建考评关系"""
        try:
            # 获取该关系类型的首选模板
            template = self.batch.get_preferred_template(relation_type)
            
            # 如果没有找到合适的模板，使用默认模板（向后兼容）
            if not template and hasattr(self.batch, 'default_template'):
                template = self.batch.default_template
            
            # 如果还是没有模板，记录错误
            if not template:
                logger.error(f"无法为关系类型 {relation_type} 找到合适的模板: {evaluator.name} -> {evaluatee.name}")
                return False
            
            EvaluationRelation.objects.create(
                batch=self.batch,
                evaluator=evaluator,
                evaluatee=evaluatee,
                template=template,
                weight_factor=1.0,  # 默认权重，后续会根据权重方案调整
                status='pending',
                assignment_method='auto',
                created_by=self.operator.username
            )
            logger.info(f"成功创建考评关系: {evaluator.name} -> {evaluatee.name}, 模板: {template.name}, 关系: {relation_type}")
            return True
        except Exception as e:
            logger.error(f"创建考评关系失败: {evaluator.name} -> {evaluatee.name}, 错误: {str(e)}")
            return False
    
    def _apply_weight_scheme(self, scheme_name: str):
        """应用权重方案"""
        from .simplified_weighting import SimplifiedWeightingService
        
        weighting_service = SimplifiedWeightingService(self.batch, self.operator)
        result = weighting_service.apply_preset_scheme(scheme_name)
        
        if result['success']:
            logger.info(f"成功应用权重方案: {scheme_name}")
        else:
            logger.error(f"应用权重方案失败: {result.get('error')}")
    
    def _get_eligible_participants(self) -> List[Staff]:
        """获取符合条件的参与人员"""
        return list(Staff.objects.filter(
            deleted_at__isnull=True,
            department__isnull=False,
            position__isnull=False
        ).select_related('department', 'position'))
    
    def _clear_existing_relations(self):
        """清除现有考评关系"""
        EvaluationRelation.objects.filter(
            batch=self.batch,
            deleted_at__isnull=True
        ).update(
            deleted_at=timezone.now(),
            updated_by=self.operator.username
        )
    
    def _log_strategy_execution(self, strategy_name: str, relations_created: int, error_count: int):
        """记录策略执行日志"""
        from common.models import AuditLog
        
        AuditLog.objects.create(
            user=self.operator.username,
            action='execute_assignment_strategy',
            target_model='evaluationbatch',
            target_id=self.batch.id,
            description=f'执行分配策略: {strategy_name}',
            extra_data={
                'batch_name': self.batch.name,
                'strategy_name': strategy_name,
                'relations_created': relations_created,
                'error_count': error_count,
                'operator': self.operator.name
            }
        )
