# -*- coding: utf-8 -*-
"""
考评历史管理URL配置
"""

from django.urls import path
from evaluations.views.history_views import (
    EvaluationHistoryListView,
    EvaluationHistoryDetailView,
    EvaluationComparisonView,
    EvaluationArchiveView,
    EvaluationHistoryAPIView,
)

app_name = 'evaluation_history'

urlpatterns = [
    # 考评历史列表
    path('', EvaluationHistoryListView.as_view(), name='list'),
    
    # 考评历史详情
    path('<int:pk>/', EvaluationHistoryDetailView.as_view(), name='detail'),
    
    # 考评对比分析
    path('comparison/', EvaluationComparisonView.as_view(), name='comparison'),
    
    # 考评归档管理
    path('archive/', EvaluationArchiveView.as_view(), name='archive'),
    
    # API接口
    path('api/data/', EvaluationHistoryAPIView.as_view(), name='api_data'),
]