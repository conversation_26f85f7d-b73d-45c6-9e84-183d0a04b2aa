# -*- coding: utf-8 -*-
"""
考评应用视图
包含考评模板、批次、关系管理和匿名评分功能
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse, HttpResponse, Http404, HttpResponseRedirect
from django.contrib import messages
from django.views.generic import View, ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy, reverse
from django.db import transaction
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from django.utils import timezone
from django.forms import inlineformset_factory
import json
import logging

from .models import (
    EvaluationTemplate, EvaluationItem, ScoringTier, WeightingRule,
    EvaluationBatch, EvaluationRelation, EvaluationRecord, EvaluationItemRecord, BatchTemplate
)
from .forms import EvaluationBatchForm, BatchTemplateConfigForm
from .services_original import EvaluationSubmissionService, EvaluationProgressService
from organizations.models import Staff, Department, Position
from organizations.middleware import require_admin_permission, require_anonymous_login
from common.models import AuditLog
from reports.models import EvaluationProgress

logger = logging.getLogger(__name__)


# 管理端视图类
class TemplateListView(ListView):
    """考评模板列表视图"""
    model = EvaluationTemplate
    template_name = 'admin/template/list.html'
    context_object_name = 'templates'
    paginate_by = 20
    
    def get_queryset(self):
        """获取模板列表数据"""
        queryset = EvaluationTemplate.objects.filter(deleted_at__isnull=True).order_by('-created_at')
        
        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(description__icontains=search)
            )
        
        # 启用状态筛选
        show_disabled = self.request.GET.get('show_disabled', 'false')
        if show_disabled.lower() != 'true':
            queryset = queryset.filter(is_active=True)
            
        return queryset
    
    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)
        context['show_disabled'] = self.request.GET.get('show_disabled', 'false')
        context['search_query'] = self.request.GET.get('search', '')
        return context


class TemplateCreateView(CreateView):
    """考评模板创建视图"""
    model = EvaluationTemplate
    template_name = 'admin/template/create.html'
    fields = ['name', 'description', 'template_type', 'is_default', 'is_active', 'sort_order']
    success_url = reverse_lazy('evaluations:admin:template_list')
    
    def form_valid(self, form):
        """表单验证成功后的处理"""
        try:
            with transaction.atomic():
                # 调试：打印POST数据
                logging.info(f'POST数据: {dict(self.request.POST)}')
                
                # 保存模板基本信息
                form.instance.created_by = self.request.current_staff
                template = form.save()
                
                # 处理评分项数据
                items_count = self.create_evaluation_items(template)
                
                messages.success(self.request, f'考评模板 "{template.name}" 创建成功，包含 {items_count} 个评分项')
                return redirect(self.success_url)
                
        except Exception as e:
            logging.error(f'创建考评模板失败: {str(e)}')
            messages.error(self.request, f'创建失败: {str(e)}')
            return self.form_invalid(form)
    
    def create_evaluation_items(self, template):
        """创建评分项"""
        # 评分模式映射（前端到后端）
        scoring_mode_mapping = {
            'numeric': 'score',
            'tier': 'level', 
            'text': 'text'
        }
        
        # 解析评分项数据
        items_data = self.extract_items_data()
        logging.info(f'解析到的评分项数据: {items_data}')
        
        items_count = 0
        for item_data in items_data:
            if item_data.get('name'):  # 只有名称不为空才创建
                # 映射评分模式
                scoring_mode = scoring_mode_mapping.get(
                    item_data.get('scoring_mode', 'numeric'), 
                    'score'
                )
                
                # 创建评分项
                evaluation_item = EvaluationItem.objects.create(
                    template=template,
                    name=item_data['name'],
                    description=item_data.get('description', ''),
                    scoring_mode=scoring_mode,
                    max_score=int(item_data.get('max_score', 10)),
                    weight=float(item_data.get('weight', 1.0)),
                    is_required=item_data.get('is_required', True),
                    is_active=True,
                    sort_order=item_data.get('sort_order', 0)
                )
                items_count += 1
                
                # 如果是等级评分，创建等级选项
                if scoring_mode == 'level' and 'tiers' in item_data:
                    self.create_scoring_tiers(evaluation_item, item_data['tiers'])
        
        return items_count
    
    def create_scoring_tiers(self, evaluation_item, tiers_data):
        """创建评分等级"""
        for tier_data in tiers_data:
            if tier_data.get('name'):  # 只有名称不为空才创建
                ScoringTier.objects.create(
                    evaluation_item=evaluation_item,
                    name=tier_data['name'],
                    score=int(tier_data.get('score', 10)),
                    description=tier_data.get('description', ''),
                    sort_order=tier_data.get('sort_order', 0)
                )
    
    def extract_items_data(self):
        """从POST数据中提取评分项数据"""
        items_data = []
        post_data = self.request.POST
        
        # 找出所有评分项的索引
        item_indices = set()
        for key in post_data.keys():
            if key.startswith('items[') and '][' in key:
                # 提取索引，如 items[1][name] -> 1
                try:
                    index = key.split('[')[1].split(']')[0]
                    item_indices.add(index)
                except (IndexError, ValueError):
                    continue
        
        # 为每个索引提取数据
        for index in item_indices:
            item_data = {}
            
            # 提取基本字段
            for field in ['name', 'description', 'scoring_mode', 'max_score', 'weight', 'sort_order']:
                key = f'items[{index}][{field}]'
                if key in post_data:
                    item_data[field] = post_data[key]
            
            # 提取等级数据（如果有）
            tiers_data = []
            tier_indices = set()
            
            # 找出该评分项的所有等级索引
            for key in post_data.keys():
                if key.startswith(f'items[{index}][tiers][') and '][' in key:
                    try:
                        tier_index = key.split('[')[3].split(']')[0]
                        tier_indices.add(tier_index)
                    except (IndexError, ValueError):
                        continue
            
            # 提取每个等级的数据
            for tier_index in tier_indices:
                tier_data = {}
                for field in ['name', 'score', 'description']:
                    key = f'items[{index}][tiers][{tier_index}][{field}]'
                    if key in post_data:
                        tier_data[field] = post_data[key]
                
                if tier_data.get('name'):  # 只有名称不为空才添加
                    tier_data['sort_order'] = len(tiers_data)
                    tiers_data.append(tier_data)
            
            if tiers_data:
                item_data['tiers'] = tiers_data
            
            if item_data.get('name'):  # 只有名称不为空才添加
                item_data['sort_order'] = len(items_data)
                items_data.append(item_data)
        
        return items_data


class BatchListView(ListView):
    """考评批次列表视图"""
    model = EvaluationBatch
    template_name = 'admin/batch/list.html'
    context_object_name = 'batches'
    paginate_by = 20
    
    def get_queryset(self):
        """获取批次列表数据"""
        queryset = EvaluationBatch.objects.filter(deleted_at__isnull=True).order_by('-created_at')
        
        # 状态筛选
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        # 启用状态筛选
        show_disabled = self.request.GET.get('show_disabled', 'false')
        if show_disabled.lower() != 'true':
            queryset = queryset.filter(is_active=True)
            
        return queryset
    
    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)
        context['show_disabled'] = self.request.GET.get('show_disabled', 'false')
        context['current_status'] = self.request.GET.get('status', '')
        return context


class BatchCreateView(CreateView):
    """考评批次创建视图"""
    model = EvaluationBatch
    form_class = EvaluationBatchForm
    template_name = 'admin/batch/create.html'
    success_url = reverse_lazy('evaluations:admin:batch_list')

    def form_valid(self, form):
        """表单验证成功后的处理"""
        form.instance.created_by = self.request.current_staff
        messages.success(self.request, f'考评批次 "{form.instance.name}" 创建成功！')
        return super().form_valid(form)

    def form_invalid(self, form):
        """表单验证失败时的处理"""
        messages.error(self.request, '批次创建失败，请检查输入信息')
        return super().form_invalid(form)


class ProgressListView(ListView):
    """考评进度列表视图"""
    model = EvaluationProgress
    template_name = 'admin/progress/list.html'
    context_object_name = 'progress_list'
    paginate_by = 20
    
    def get_queryset(self):
        """获取进度列表数据"""
        queryset = EvaluationProgress.objects.filter(
            deleted_at__isnull=True,
            batch__status__in=['active', 'completed']
        ).select_related('batch', 'batch__default_template').order_by('-updated_at')
        
        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(batch__name__icontains=search)
            
        return queryset
    
    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)
        
        # 计算总体统计
        progress_list = context['progress_list']
        context['total_batches'] = len(progress_list)
        context['active_batches'] = len([p for p in progress_list if p.batch.status == 'active'])
        context['avg_completion'] = sum([p.completion_rate for p in progress_list]) / len(progress_list) if progress_list else 0
        
        return context


# 匿名端视图类
class AnonymousHomeView(View):
    """匿名端首页视图"""
    template_name = 'anonymous/home.html'
    
    @require_anonymous_login
    def get(self, request):
        """显示匿名端首页"""
        staff = request.current_staff
        
        # 获取待完成的考评任务
        pending_tasks = self._get_pending_tasks(staff)
        
        # 获取已完成的考评记录
        completed_tasks = self._get_completed_tasks(staff)
        
        context = {
            'staff': staff,
            'pending_tasks': pending_tasks,
            'completed_tasks': completed_tasks,
            'pending_count': len(pending_tasks),
            'completed_count': len(completed_tasks),
        }
        
        return render(request, self.template_name, context)
        
    def _get_pending_tasks(self, staff):
        """获取待完成的考评任务"""
        try:
            return EvaluationRelation.objects.filter(
                evaluator=staff,
                status='pending',
                batch__status='active',
                deleted_at__isnull=True
            ).select_related('evaluatee', 'batch', 'template').order_by('batch__end_date')
        except Exception as e:
            logger.error(f"获取待完成任务失败: {e}")
            return []
            
    def _get_completed_tasks(self, staff):
        """获取已完成的考评记录"""
        try:
            return EvaluationRelation.objects.filter(
                evaluator=staff,
                is_assigned=True,
                deleted_at__isnull=True
            ).select_related('evaluatee', 'batch', 'template').order_by('-updated_at')[:5]
        except Exception as e:
            logger.error(f"获取已完成任务失败: {e}")
            return []


class AnonymousTaskListView(ListView):
    """匿名端任务列表视图"""
    template_name = 'anonymous/task_list.html'
    context_object_name = 'tasks'
    paginate_by = 10
    
    @require_anonymous_login
    def get_queryset(self):
        """获取当前用户的考评任务"""
        staff = self.request.current_staff
        return EvaluationRelation.objects.filter(
            evaluator=staff,
            batch__status='active',
            deleted_at__isnull=True
        ).select_related('evaluatee', 'batch', 'template').order_by('status', 'batch__end_date')


class AnonymousEvaluateView(View):
    """匿名端评分视图"""
    template_name = 'anonymous/evaluate.html'
    
    @require_anonymous_login
    def get(self, request, relation_id):
        """显示评分页面"""
        try:
            # 获取考评关系
            relation = get_object_or_404(
                EvaluationRelation,
                id=relation_id,
                evaluator=request.current_staff,
                deleted_at__isnull=True
            )
            
            # 检查是否可以评分
            if relation.status == 'completed':
                messages.warning(request, '该考评已完成，无法重复评分')
                return redirect('evaluations:anonymous:task_list')
                
            if relation.batch.status != 'active':
                messages.error(request, '该考评批次未激活或已结束')
                return redirect('evaluations:anonymous:task_list')
                
            # 获取考评模板和评分项
            template = relation.template
            evaluation_items = EvaluationItem.objects.filter(
                template=template, deleted_at__isnull=True
            ).order_by('sort_order')
            
            # 获取已存在的评分记录（如果有）
            existing_record = EvaluationRecord.objects.filter(
                relation=relation, deleted_at__isnull=True
            ).first()
            
            context = {
                'relation': relation,
                'template': template,
                'evaluation_items': evaluation_items,
                'existing_record': existing_record,
                'evaluatee': relation.evaluatee,
            }
            
            return render(request, self.template_name, context)
            
        except Exception as e:
            logger.error(f"获取评分页面失败: {e}")
            messages.error(request, '获取评分页面失败，请重试')
            return redirect('evaluations:anonymous:task_list')


class AnonymousSubmitView(View):
    """匿名端提交评分视图"""
    
    @require_anonymous_login
    def post(self, request, relation_id):
        """处理评分提交"""
        try:
            # 获取考评关系
            relation = get_object_or_404(
                EvaluationRelation,
                id=relation_id,
                evaluator=request.current_staff,
                deleted_at__isnull=True
            )
            
            # 处理评分数据
            evaluation_data = self._process_evaluation_data(request)
            is_draft = request.POST.get('save_draft') == 'true'
            
            # 使用提交服务处理
            submission_service = EvaluationSubmissionService(relation, request.current_staff)
            
            if is_draft:
                # 保存草稿
                result = submission_service.save_draft(evaluation_data)
            else:
                # 提交评分
                result = submission_service.submit_evaluation(evaluation_data)
            
            # 根据请求类型返回响应
            if request.headers.get('Content-Type') == 'application/json' or 'HTTP_X_REQUESTED_WITH' in request.META:
                # Ajax请求，返回JSON
                return JsonResponse(result)
            else:
                # 普通表单提交
                if result['success']:
                    if is_draft:
                        messages.success(request, '草稿保存成功')
                        return redirect('evaluations:anonymous:evaluate', relation_id=relation_id)
                    else:
                        messages.success(request, '评分提交成功！')
                        return redirect('evaluations:anonymous:task_list')
                else:
                    messages.error(request, result.get('error', '操作失败，请重试'))
                    return redirect('evaluations:anonymous:evaluate', relation_id=relation_id)
                
        except Exception as e:
            logger.error(f"处理评分失败: {e}")
            error_response = {'success': False, 'error': f'处理失败: {str(e)}'}
            
            if request.headers.get('Content-Type') == 'application/json' or 'HTTP_X_REQUESTED_WITH' in request.META:
                return JsonResponse(error_response)
            else:
                messages.error(request, error_response['error'])
                return redirect('evaluations:anonymous:evaluate', relation_id=relation_id)
            
    def _process_evaluation_data(self, request):
        """
        处理评分数据
        从POST数据中提取评分项和总体评价
        """
        evaluation_data = {
            'items': {},
            'comment': request.POST.get('comment', '').strip()
        }
        
        # 处理评分项数据
        for key, value in request.POST.items():
            if key.startswith('items[') and '][' in key:
                # 解析评分项数据格式: items[123][score] 或 items[123][tier] 或 items[123][text]
                try:
                    # 提取item_id和field_type
                    start = key.find('[') + 1
                    end = key.find(']', start)
                    item_id = key[start:end]
                    
                    field_start = key.find('[', end) + 1
                    field_end = key.find(']', field_start)
                    field_type = key[field_start:field_end]
                    
                    # 初始化评分项数据
                    if item_id not in evaluation_data['items']:
                        evaluation_data['items'][item_id] = {}
                    
                    # 存储字段值
                    evaluation_data['items'][item_id][field_type] = value
                    
                except (ValueError, IndexError) as e:
                    logger.warning(f"解析评分项数据失败: {key} = {value}, 错误: {e}")
                    continue
        
        return evaluation_data


class AnonymousResultsView(View):
    """匿名端查看评分结果视图"""
    
    @require_anonymous_login
    def get(self, request):
        """获取评分结果详情"""
        try:
            task_id = request.GET.get('task_id')
            if not task_id:
                return JsonResponse({'success': False, 'error': '缺少任务ID'})
            
            # 获取考评关系
            relation = get_object_or_404(
                EvaluationRelation,
                id=task_id,
                evaluator=request.current_staff,
                is_assigned=True,
                deleted_at__isnull=True
            )
            
            # 获取评分记录
            record = EvaluationRecord.objects.filter(
                relation=relation,
                deleted_at__isnull=True
            ).first()
            
            if not record:
                return JsonResponse({'success': False, 'error': '未找到评分记录'})
            
            # 获取评分项记录
            item_records = EvaluationItemRecord.objects.filter(
                evaluation_record=record,
                deleted_at__isnull=True
            ).select_related('evaluation_item')
            
            # 构建结果HTML
            result_html = f"""
            <div class="space-y-4">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="font-medium text-gray-900">评价对象：{relation.evaluatee.name}</h4>
                    <span class="text-sm text-gray-500">提交时间：{record.created_at.strftime('%Y-%m-%d %H:%M')}</span>
                </div>
                
                <div class="space-y-3">
            """
            
            for item_record in item_records:
                item = item_record.evaluation_item
                result_html += f"""
                    <div class="border-b border-gray-200 pb-3">
                        <div class="flex items-center justify-between mb-1">
                            <span class="font-medium text-sm">{item.name}</span>
                            <span class="text-blue-600 font-semibold">{item_record.score}分</span>
                        </div>
                """
                
                if item_record.text_content:
                    result_html += f'<p class="text-sm text-gray-600">{item_record.text_content}</p>'
                
                result_html += '</div>'
            
            result_html += f"""
                </div>
                
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex items-center justify-between mb-2">
                        <span class="font-medium">总分：</span>
                        <span class="text-lg font-bold text-blue-600">{record.total_score}分</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="font-medium">加权分：</span>
                        <span class="text-lg font-bold text-green-600">{record.weighted_score}分</span>
                    </div>
                </div>
            """
            
            if record.comment:
                result_html += f"""
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <h5 class="font-medium mb-2">总体评价：</h5>
                        <p class="text-sm text-gray-700">{record.comment}</p>
                    </div>
                """
                
            result_html += '</div>'
            
            return JsonResponse({
                'success': True,
                'html': result_html
            })
            
        except Exception as e:
            logger.error(f"获取评分结果失败: {e}")
            return JsonResponse({'success': False, 'error': '获取评分结果失败'})


class AnonymousDraftView(View):
    """匿名端草稿数据视图"""
    
    @require_anonymous_login
    def get(self, request, relation_id):
        """获取草稿数据"""
        try:
            # 获取考评关系
            relation = get_object_or_404(
                EvaluationRelation,
                id=relation_id,
                evaluator=request.current_staff,
                deleted_at__isnull=True
            )
            
            # 获取草稿记录
            record = EvaluationRecord.objects.filter(
                relation=relation,
                deleted_at__isnull=True
            ).first()
            
            if not record:
                return JsonResponse({'success': False, 'message': '无草稿数据'})
            
            # 获取评分项记录
            item_records = EvaluationItemRecord.objects.filter(
                evaluation_record=record,
                deleted_at__isnull=True
            ).select_related('evaluation_item')
            
            # 构建草稿数据
            draft_data = {
                'comment': record.comment,
                'items': {}
            }
            
            for item_record in item_records:
                item_id = str(item_record.evaluation_item.id)
                draft_data['items'][item_id] = {
                    'score': str(item_record.score),
                    'text': item_record.text_content or '',
                }
                
                if item_record.tier_selected:
                    draft_data['items'][item_id]['tier'] = item_record.tier_selected
            
            return JsonResponse({
                'success': True,
                'data': draft_data,
                'total_score': record.total_score,
                'weighted_score': record.weighted_score
            })
            
        except Exception as e:
            logger.error(f"获取草稿数据失败: {e}")
            return JsonResponse({'success': False, 'error': '获取草稿数据失败'})


# 其他占位视图类（后续实现详细功能）
class TemplateDetailView(DetailView):
    model = EvaluationTemplate
    template_name = 'admin/template/detail.html'

class TemplateUpdateView(UpdateView):
    model = EvaluationTemplate
    template_name = 'admin/template/update.html'
    fields = ['name', 'description', 'template_type', 'is_default', 'is_active', 'sort_order']
    success_url = reverse_lazy('evaluations:admin:template_list')

    def form_valid(self, form):
        """表单验证成功后的处理"""
        form.instance.updated_by = self.request.current_staff
        messages.success(self.request, f'考评模板 "{form.instance.name}" 修改成功！')
        return super().form_valid(form)

    def form_invalid(self, form):
        """表单验证失败时的处理"""
        messages.error(self.request, '模板修改失败，请检查输入信息')
        return super().form_invalid(form)

class TemplateDeleteView(DeleteView):
    """考评模板删除视图"""
    model = EvaluationTemplate
    template_name = 'admin/template/delete_confirm.html'
    success_url = reverse_lazy('evaluations:admin:template_list')

    def get_object(self, queryset=None):
        """获取要删除的对象"""
        obj = super().get_object(queryset)
        # 检查模板是否可以删除（没有被批次使用）
        if obj.evaluationbatch_set.exists():
            messages.error(self.request, f'模板 "{obj.name}" 正在被考评批次使用，无法删除')
            raise Http404("模板正在使用中，无法删除")
        return obj

    def delete(self, request, *args, **kwargs):
        """执行删除操作"""
        self.object = self.get_object()
        success_url = self.get_success_url()

        try:
            with transaction.atomic():
                # 软删除模板（设置deleted_at字段）
                self.object.deleted_at = timezone.now()
                self.object.save()

                messages.success(request, f'考评模板 "{self.object.name}" 删除成功')

        except Exception as e:
            logging.error(f'删除模板失败: {str(e)}')
            messages.error(request, f'删除失败: {str(e)}')

        return HttpResponseRedirect(success_url)


class TemplateCopyView(View):
    """考评模板复制视图"""

    def post(self, request, pk):
        """复制模板"""
        try:
            with transaction.atomic():
                # 获取原始模板
                original_template = get_object_or_404(EvaluationTemplate, pk=pk, deleted_at__isnull=True)

                # 创建模板副本
                new_template = EvaluationTemplate.objects.create(
                    name=f"{original_template.name} (副本)",
                    description=original_template.description,
                    template_type=original_template.template_type,
                    is_default=False,  # 副本不能是默认模板
                    is_active=True,
                    sort_order=original_template.sort_order,
                    created_by=request.current_staff
                )

                # 复制评分项
                items_count = 0
                for original_item in original_template.evaluationitem_set.all():
                    new_item = EvaluationItem.objects.create(
                        template=new_template,
                        name=original_item.name,
                        description=original_item.description,
                        scoring_mode=original_item.scoring_mode,
                        max_score=original_item.max_score,
                        weight=original_item.weight,
                        is_required=original_item.is_required,
                        is_active=original_item.is_active,
                        sort_order=original_item.sort_order
                    )
                    items_count += 1

                    # 复制评分等级（如果有）
                    for original_tier in original_item.scoringtier_set.all():
                        ScoringTier.objects.create(
                            evaluation_item=new_item,
                            name=original_tier.name,
                            score=original_tier.score,
                            description=original_tier.description,
                            sort_order=original_tier.sort_order
                        )

                messages.success(request, f'模板复制成功！新模板 "{new_template.name}" 已创建，包含 {items_count} 个评分项')
                return redirect('evaluations:admin:template_detail', pk=new_template.pk)

        except Exception as e:
            logging.error(f'复制模板失败: {str(e)}')
            messages.error(request, f'复制失败: {str(e)}')
            return redirect('evaluations:admin:template_list')


class EvaluationItemListView(ListView):
    """评分项列表视图"""
    model = EvaluationItem
    template_name = 'admin/template/item_list.html'
    context_object_name = 'items'
    paginate_by = 20

    def get_queryset(self):
        """获取指定模板的评分项"""
        self.template = get_object_or_404(EvaluationTemplate, pk=self.kwargs['template_id'])
        return EvaluationItem.objects.filter(
            template=self.template,
            deleted_at__isnull=True
        ).order_by('sort_order', 'created_at')

    def get_context_data(self, **kwargs):
        """添加模板信息到上下文"""
        context = super().get_context_data(**kwargs)
        context['template'] = self.template
        return context


class EvaluationItemCreateView(CreateView):
    """评分项创建视图"""
    model = EvaluationItem
    template_name = 'admin/template/item_create.html'
    fields = ['name', 'description', 'scoring_mode', 'max_score', 'weight', 'is_required', 'sort_order']

    def get_template_object(self):
        """获取模板对象"""
        return get_object_or_404(EvaluationTemplate, pk=self.kwargs['template_id'])

    def get_context_data(self, **kwargs):
        """添加模板信息到上下文"""
        context = super().get_context_data(**kwargs)
        context['template'] = self.get_template_object()
        return context

    def form_valid(self, form):
        """表单验证成功后的处理"""
        form.instance.template = self.get_template_object()
        messages.success(self.request, f'评分项 "{form.instance.name}" 创建成功')
        return super().form_valid(form)

    def get_success_url(self):
        """返回评分项列表"""
        return reverse('evaluations:admin:item_list', kwargs={'template_id': self.kwargs['template_id']})


class EvaluationItemUpdateView(UpdateView):
    """评分项编辑视图"""
    model = EvaluationItem
    template_name = 'admin/template/item_update.html'
    fields = ['name', 'description', 'scoring_mode', 'max_score', 'weight', 'is_required', 'sort_order']

    def get_template_object(self):
        """获取模板对象"""
        return get_object_or_404(EvaluationTemplate, pk=self.kwargs['template_id'])

    def get_context_data(self, **kwargs):
        """添加模板信息到上下文"""
        context = super().get_context_data(**kwargs)
        context['template'] = self.get_template_object()
        return context

    def form_valid(self, form):
        """表单验证成功后的处理"""
        messages.success(self.request, f'评分项 "{form.instance.name}" 修改成功')
        return super().form_valid(form)

    def get_success_url(self):
        """返回评分项列表"""
        return reverse('evaluations:admin:item_list', kwargs={'template_id': self.kwargs['template_id']})


class EvaluationItemDeleteView(DeleteView):
    """评分项删除视图"""
    model = EvaluationItem
    template_name = 'admin/template/item_delete_confirm.html'

    def get_template_object(self):
        """获取模板对象"""
        return get_object_or_404(EvaluationTemplate, pk=self.kwargs['template_id'])

    def get_context_data(self, **kwargs):
        """添加模板信息到上下文"""
        context = super().get_context_data(**kwargs)
        context['template'] = self.get_template_object()
        return context

    def delete(self, request, *args, **kwargs):
        """执行删除操作"""
        self.object = self.get_object()
        success_url = self.get_success_url()

        try:
            with transaction.atomic():
                # 软删除评分项
                self.object.deleted_at = timezone.now()
                self.object.save()

                messages.success(request, f'评分项 "{self.object.name}" 删除成功')

        except Exception as e:
            logging.error(f'删除评分项失败: {str(e)}')
            messages.error(request, f'删除失败: {str(e)}')

        return HttpResponseRedirect(success_url)

    def get_success_url(self):
        """返回评分项列表"""
        return reverse('evaluations:admin:item_list', kwargs={'template_id': self.kwargs['template_id']})


class WeightingRuleListView(ListView):
    """权重规则列表视图"""
    model = WeightingRule
    template_name = 'admin/rule/list.html'
    context_object_name = 'rules'
    paginate_by = 20
    
    def get_queryset(self):
        """获取权重规则列表数据"""
        queryset = WeightingRule.objects.filter(deleted_at__isnull=True).order_by('-priority', '-created_at')
        
        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(description__icontains=search)
            )
            
        return queryset


class WeightingRuleCreateView(CreateView):
    """权重规则创建视图"""
    model = WeightingRule
    template_name = 'admin/rule/create.html'
    fields = ['name', 'description', 'condition_type', 'condition_value', 'relation_type', 'weight_factor', 'priority', 'is_active']
    success_url = reverse_lazy('evaluations:admin:rule_list')
    
    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)
        from organizations.models import Department
        context['departments'] = Department.objects.filter(deleted_at__isnull=True).order_by('name')
        return context
    
    def form_valid(self, form):
        """表单验证成功后的处理"""
        form.instance.created_by = self.request.current_staff.username
        messages.success(self.request, '权重规则创建成功')
        return super().form_valid(form)


class WeightingRuleUpdateView(UpdateView):
    """权重规则更新视图"""
    model = WeightingRule
    template_name = 'admin/rule/update.html'
    fields = ['name', 'description', 'condition_type', 'condition_value', 'relation_type', 'weight_factor', 'priority', 'is_active']
    success_url = reverse_lazy('evaluations:admin:rule_list')
    
    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)
        from organizations.models import Department
        context['departments'] = Department.objects.filter(deleted_at__isnull=True).order_by('name')
        return context
    
    def form_valid(self, form):
        """表单验证成功后的处理"""
        form.instance.updated_by = self.request.current_staff.username
        messages.success(self.request, '权重规则更新成功')
        return super().form_valid(form)


class WeightingRuleDeleteView(DeleteView):
    """权重规则删除视图"""
    model = WeightingRule
    success_url = reverse_lazy('evaluations:admin:rule_list')
    
    def delete(self, request, *args, **kwargs):
        """软删除处理"""
        self.object = self.get_object()
        self.object.deleted_at = timezone.now()
        self.object.updated_by = request.current_staff.username
        self.object.save()
        
        messages.success(request, '权重规则删除成功')
        return redirect(self.success_url)


class WeightingRuleToggleView(View):
    """权重规则启用/禁用视图"""
    
    def post(self, request, pk):
        """切换规则状态"""
        try:
            rule = get_object_or_404(WeightingRule, pk=pk, deleted_at__isnull=True)
            
            # 解析请求数据
            import json
            data = json.loads(request.body)
            is_active = data.get('is_active', not rule.is_active)
            
            # 更新状态
            rule.is_active = is_active
            rule.updated_by = request.current_staff.username
            rule.save()
            
            # 记录审计日志
            AuditLog.objects.create(
                user=request.current_staff.username,
                action='toggle_rule',
                target_model='weightingrule',
                target_id=rule.id,
                description=f'{"启用" if is_active else "禁用"}权重规则: {rule.name}',
                extra_data={'is_active': is_active}
            )
            
            return JsonResponse({'success': True})
            
        except Exception as e:
            logger.error(f"切换规则状态失败: {e}")
            return JsonResponse({'success': False, 'error': str(e)})


class WeightingRuleCopyView(View):
    """权重规则复制视图"""
    
    def post(self, request, pk):
        """复制规则"""
        try:
            original_rule = get_object_or_404(WeightingRule, pk=pk, deleted_at__isnull=True)
            
            # 创建副本
            new_rule = WeightingRule.objects.create(
                name=f"{original_rule.name} (副本)",
                description=original_rule.description,
                condition_type=original_rule.condition_type,
                condition_value=original_rule.condition_value,
                relation_type=original_rule.relation_type,
                weight_factor=original_rule.weight_factor,
                priority=original_rule.priority,
                is_active=False,  # 副本默认禁用
                created_by=request.current_staff.username
            )
            
            # 记录审计日志
            AuditLog.objects.create(
                user=request.current_staff.username,
                action='copy_rule',
                target_model='weightingrule',
                target_id=new_rule.id,
                description=f'复制权重规则: {original_rule.name} -> {new_rule.name}',
                extra_data={'original_id': original_rule.id}
            )
            
            return JsonResponse({'success': True})
            
        except Exception as e:
            logger.error(f"复制规则失败: {e}")
            return JsonResponse({'success': False, 'error': str(e)})

class BatchDetailView(DetailView):
    """批次详情视图"""
    model = EvaluationBatch
    template_name = 'admin/batch/detail.html'
    context_object_name = 'batch'
    
    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)
        batch = self.object
        
        # 获取批次配置的模板
        batch_templates = BatchTemplate.objects.filter(
            batch=batch
        ).select_related('template').order_by('sort_order', 'template__name')
        
        context['batch_templates'] = batch_templates
        
        return context

class BatchUpdateView(UpdateView):
    model = EvaluationBatch
    template_name = 'admin/batch/update.html'
    fields = ['name', 'description', 'default_template', 'start_date', 'end_date']
    success_url = reverse_lazy('evaluations:admin:batch_list')

class BatchDeleteView(DeleteView):
    model = EvaluationBatch
    success_url = reverse_lazy('evaluations:admin:batch_list')

class BatchActivateView(View):
    """批次激活视图"""
    
    @require_admin_permission
    def post(self, request, pk):
        try:
            batch = get_object_or_404(EvaluationBatch, pk=pk)
            
            # 检查批次状态
            if batch.status != 'draft':
                messages.error(request, f'只有草稿状态的批次才能激活，当前状态：{batch.get_status_display()}')
                return redirect('evaluations:admin:batch_list')
            
            # 检查是否有考评关系
            relations_count = batch.evaluationrelation_set.count()
            if relations_count == 0:
                messages.error(request, '批次还没有分配考评关系，无法激活')
                return redirect('evaluations:admin:batch_list')
            
            # 激活批次
            with transaction.atomic():
                batch.status = 'active'
                batch.save(update_fields=['status', 'updated_at'])
                
                # 记录操作日志
                AuditLog.objects.create(
                    action='batch_activate',
                    target_model='EvaluationBatch',
                    target_id=batch.id,
                    user=request.current_staff.username if hasattr(request, 'current_staff') else 'admin',
                    description=f'激活考评批次: {batch.name}',
                    ip_address=request.META.get('REMOTE_ADDR')
                )
            
            messages.success(request, f'考评批次【{batch.name}】激活成功，系统将自动发送通知给参与者')
            logger.info(f"批次激活: {batch.name} (ID: {batch.id})")
            
        except Exception as e:
            logger.error(f"批次激活失败: {str(e)}")
            messages.error(request, f'批次激活失败: {str(e)}')
        
        return redirect('evaluations:admin:batch_list')

class BatchCompleteView(View):
    """批次完成视图"""
    
    @require_admin_permission  
    def post(self, request, pk):
        try:
            batch = get_object_or_404(EvaluationBatch, pk=pk)
            
            # 检查批次状态
            if batch.status != 'active':
                messages.error(request, f'只有进行中的批次才能完成，当前状态：{batch.get_status_display()}')
                return redirect('evaluations:admin:batch_list')
            
            # 获取完成统计
            total_relations = batch.evaluationrelation_set.count()
            completed_relations = batch.evaluationrelation_set.filter(
                evaluationrecord__isnull=False
            ).count()
            completion_rate = (completed_relations / total_relations * 100) if total_relations > 0 else 0
            
            # 完成批次
            with transaction.atomic():
                batch.status = 'completed'
                batch.save(update_fields=['status', 'updated_at'])
                
                # 记录操作日志
                AuditLog.objects.create(
                    action='batch_complete',
                    target_model='EvaluationBatch',
                    target_id=batch.id,
                    user=request.current_staff.username if hasattr(request, 'current_staff') else 'admin',
                    description=f'完成考评批次: {batch.name}, 完成率: {completion_rate:.1f}%',
                    ip_address=request.META.get('REMOTE_ADDR')
                )
            
            messages.success(request, f'考评批次【{batch.name}】已完成，完成率：{completion_rate:.1f}%')
            logger.info(f"批次完成: {batch.name} (ID: {batch.id}), 完成率: {completion_rate:.1f}%")
            
        except Exception as e:
            logger.error(f"批次完成失败: {str(e)}")
            messages.error(request, f'批次完成失败: {str(e)}')
        
        return redirect('evaluations:admin:batch_list')

class BatchAssignView(View):
    def get(self, request, pk):
        return render(request, 'admin/batch/assign.html')

class BatchRelationListView(ListView):
    model = EvaluationRelation
    template_name = 'admin/relation/list.html'

class RelationUpdateView(UpdateView):
    model = EvaluationRelation
    template_name = 'admin/relation/update.html'
    fields = ['batch', 'evaluator', 'evaluatee', 'template', 'weight_factor', 'assignment_method']

class RelationDeleteView(DeleteView):
    model = EvaluationRelation
    success_url = reverse_lazy('evaluations:admin:batch_list')

class ProgressDetailView(DetailView):
    """考评进度详情视图"""
    model = EvaluationBatch
    template_name = 'admin/progress/detail.html'
    context_object_name = 'object'
    
    def get_context_data(self, **kwargs):
        """添加进度详情的额外上下文数据"""
        context = super().get_context_data(**kwargs)
        batch = self.object
        
        try:
            # 获取或创建进度记录
            progress, created = EvaluationProgress.objects.get_or_create(
                batch=batch,
                defaults={
                    'total_relations': 0,
                    'completed_relations': 0,
                    'completion_rate': 0.0
                }
            )
            
            # 计算实时进度数据
            total_relations = EvaluationRelation.objects.filter(
                batch=batch, deleted_at__isnull=True
            ).count()
            
            completed_relations = EvaluationRelation.objects.filter(
                batch=batch, is_assigned=True, deleted_at__isnull=True
            ).count()
            
            # 更新进度记录
            if total_relations > 0:
                completion_rate = (completed_relations / total_relations) * 100
                progress.total_relations = total_relations
                progress.completed_relations = completed_relations
                progress.remaining_relations = total_relations - completed_relations
                progress.completion_rate = completion_rate
                progress.save()
            
            context['progress'] = progress
            
            # 草稿状态任务数量
            draft_count = EvaluationRecord.objects.filter(
                relation__batch=batch,
                relation__status='pending',
                deleted_at__isnull=True
            ).count()
            context['draft_count'] = draft_count
            
            # 各部门完成情况统计
            context['department_stats'] = self._get_department_stats(batch)
            
            # 参与人员完成情况
            context['participants'] = self._get_participants_stats(batch)
            
            # 所有部门列表（用于筛选）
            context['departments'] = Department.objects.filter(
                deleted_at__isnull=True
            ).order_by('name')
            
            # 最近活动记录
            context['recent_activities'] = self._get_recent_activities(batch)
            
        except Exception as e:
            logger.error(f"获取进度详情数据失败: {e}")
            # 提供默认数据
            context['progress'] = {
                'total_relations': 0,
                'completed_relations': 0,
                'remaining_relations': 0,
                'completion_rate': 0.0
            }
            context['draft_count'] = 0
            context['department_stats'] = []
            context['participants'] = []
            context['departments'] = []
            context['recent_activities'] = []
        
        return context
    
    def _get_department_stats(self, batch):
        """获取各部门完成情况统计"""
        try:
            from django.db.models import Count, Q
            from organizations.models import Department
            
            stats = []
            departments = Department.objects.filter(deleted_at__isnull=True)
            
            for dept in departments:
                # 该部门参与此批次的评价关系
                dept_relations = EvaluationRelation.objects.filter(
                    batch=batch,
                    evaluator__department=dept,
                    deleted_at__isnull=True
                )
                
                total = dept_relations.count()
                if total == 0:
                    continue
                    
                completed = dept_relations.filter(is_assigned=True).count()
                completion_rate = (completed / total) * 100 if total > 0 else 0
                
                # 参与人数
                participants = dept_relations.values('evaluator').distinct().count()
                
                stats.append({
                    'department': dept.name,
                    'total': total,
                    'completed': completed,
                    'completion_rate': completion_rate,
                    'participants': participants
                })
            
            return sorted(stats, key=lambda x: x['completion_rate'], reverse=True)
            
        except Exception as e:
            logger.error(f"获取部门统计失败: {e}")
            return []
    
    def _get_participants_stats(self, batch):
        """获取参与人员完成情况"""
        try:
            from organizations.models import Staff
            
            participants = []
            
            # 获取此批次的所有评价者
            evaluators = Staff.objects.filter(
                evaluator_relations__batch=batch,
                evaluator_relations__deleted_at__isnull=True,
                deleted_at__isnull=True
            ).distinct()
            
            for evaluator in evaluators:
                # 该评价者在此批次的任务
                user_relations = EvaluationRelation.objects.filter(
                    batch=batch,
                    evaluator=evaluator,
                    deleted_at__isnull=True
                )
                
                total_tasks = user_relations.count()
                completed_tasks = user_relations.filter(is_assigned=True).count()
                completion_rate = (completed_tasks / total_tasks) * 100 if total_tasks > 0 else 0
                
                # 最后活动时间
                last_activity = None
                latest_record = EvaluationRecord.objects.filter(
                    relation__evaluator=evaluator,
                    relation__batch=batch,
                    deleted_at__isnull=True
                ).order_by('-updated_at').first()
                
                if latest_record:
                    last_activity = latest_record.updated_at
                
                participants.append({
                    'id': evaluator.id,
                    'name': evaluator.name,
                    'department': evaluator.department.name if evaluator.department else '未分配',
                    'position': evaluator.position.name if evaluator.position else '未分配',
                    'total_tasks': total_tasks,
                    'completed_tasks': completed_tasks,
                    'completion_rate': completion_rate,
                    'last_activity': last_activity
                })
            
            return sorted(participants, key=lambda x: x['completion_rate'], reverse=True)
            
        except Exception as e:
            logger.error(f"获取参与人员统计失败: {e}")
            return []
    
    def _get_recent_activities(self, batch):
        """获取最近活动记录"""
        try:
            activities = []
            
            # 获取最近的评分记录
            recent_records = EvaluationRecord.objects.filter(
                relation__batch=batch,
                deleted_at__isnull=True
            ).select_related(
                'relation__evaluator', 'relation__evaluatee'
            ).order_by('-updated_at')[:20]
            
            for record in recent_records:
                activity_type = 'completed' if record.relation.status == 'completed' else 'draft'
                
                activities.append({
                    'type': activity_type,
                    'user': record.relation.evaluator.name,
                    'description': f"{'完成了对' if activity_type == 'completed' else '保存了对'} {record.relation.evaluatee.name} 的评价",
                    'created_at': record.updated_at
                })
            
            # 获取最近的批次操作日志
            batch_logs = AuditLog.objects.filter(
                target_model='evaluationbatch',
                target_id=batch.id
            ).order_by('-created_at')[:5]
            
            for log in batch_logs:
                activities.append({
                    'type': log.action,
                    'user': log.user,
                    'description': log.description,
                    'created_at': log.created_at
                })
            
            # 按时间排序
            activities.sort(key=lambda x: x['created_at'], reverse=True)
            return activities[:15]  # 只返回最近15条
            
        except Exception as e:
            logger.error(f"获取活动记录失败: {e}")
            return []


class ParticipantTasksView(View):
    """参与人员任务详情视图"""
    
    @require_admin_permission
    def get(self, request, batch_id):
        """获取参与人员的任务详情"""
        try:
            participant_id = request.GET.get('participant_id')
            if not participant_id:
                return JsonResponse({'success': False, 'error': '缺少参与人员ID'})
            
            batch = get_object_or_404(EvaluationBatch, pk=batch_id, deleted_at__isnull=True)
            participant = get_object_or_404(Staff, pk=participant_id, deleted_at__isnull=True)
            
            # 获取该参与人员在此批次的所有任务
            tasks = EvaluationRelation.objects.filter(
                batch=batch,
                evaluator=participant,
                deleted_at__isnull=True
            ).select_related('evaluatee', 'template').order_by('status', '-updated_at')
            
            # 构建任务详情HTML
            tasks_html = f"""
            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="bg-blue-50 rounded-lg p-4">
                        <div class="flex items-center">
                            <i data-lucide="clipboard-list" class="w-5 h-5 text-blue-600 mr-2"></i>
                            <div>
                                <p class="text-sm text-blue-600">总任务数</p>
                                <p class="text-xl font-bold text-blue-900">{tasks.count()}</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4">
                        <div class="flex items-center">
                            <i data-lucide="check-circle" class="w-5 h-5 text-green-600 mr-2"></i>
                            <div>
                                <p class="text-sm text-green-600">已完成</p>
                                <p class="text-xl font-bold text-green-900">{tasks.filter(is_assigned=True).count()}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">被评价者</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">评价模板</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">状态</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">更新时间</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
            """
            
            for task in tasks:
                status_color = 'green' if task.status == 'completed' else 'yellow'
                status_text = '已完成' if task.status == 'completed' else '待完成'
                
                tasks_html += f"""
                            <tr>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="text-sm font-medium text-gray-900">{task.evaluatee.name}</div>
                                    </div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {task.template.name if task.template else '默认模板'}
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-{status_color}-100 text-{status_color}-800">
                                        {status_text}
                                    </span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {task.updated_at.strftime('%Y-%m-%d %H:%M') if task.updated_at else '未更新'}
                                </td>
                            </tr>
                """
            
            tasks_html += """
                        </tbody>
                    </table>
                </div>
            </div>
            """
            
            return JsonResponse({
                'success': True,
                'participant_name': participant.name,
                'html': tasks_html
            })
            
        except Exception as e:
            logger.error(f"获取参与人员任务详情失败: {e}")
            return JsonResponse({'success': False, 'error': '获取任务详情失败'})


class SendIndividualReminderView(View):
    """发送个人提醒视图"""
    
    @require_admin_permission
    def post(self, request, batch_id):
        """向指定参与人员发送提醒"""
        try:
            import json
            data = json.loads(request.body)
            participant_id = data.get('participant_id')
            
            if not participant_id:
                return JsonResponse({'success': False, 'error': '缺少参与人员ID'})
            
            batch = get_object_or_404(EvaluationBatch, pk=batch_id, deleted_at__isnull=True)
            participant = get_object_or_404(Staff, pk=participant_id, deleted_at__isnull=True)
            
            # 获取该参与人员的未完成任务
            pending_tasks = EvaluationRelation.objects.filter(
                batch=batch,
                evaluator=participant,
                status='pending',
                deleted_at__isnull=True
            ).count()
            
            if pending_tasks == 0:
                return JsonResponse({'success': False, 'error': '该人员没有待完成的任务'})
            
            # 记录提醒操作到审计日志
            AuditLog.objects.create(
                user=request.current_staff.username,
                action='send_reminder',
                target_model='evaluationrelation',
                target_id=participant.id,
                description=f'向 {participant.name} 发送考评提醒，剩余{pending_tasks}个任务',
                extra_data={
                    'batch_id': batch.id,
                    'batch_name': batch.name,
                    'pending_tasks': pending_tasks
                }
            )
            
            # 这里可以集成钉钉API或邮件系统发送实际提醒
            # 暂时只记录日志
            logger.info(f"向用户 {participant.name} 发送考评提醒，批次：{batch.name}")
            
            return JsonResponse({
                'success': True,
                'message': f'已向 {participant.name} 发送提醒'
            })
            
        except Exception as e:
            logger.error(f"发送个人提醒失败: {e}")
            return JsonResponse({'success': False, 'error': '发送提醒失败，请重试'})


class ExportBatchDetailView(View):
    """导出批次详细报告视图"""
    
    @require_admin_permission
    def post(self, request, batch_id):
        """导出批次详细报告"""
        try:
            batch = get_object_or_404(EvaluationBatch, pk=batch_id, deleted_at__isnull=True)
            
            # 记录导出操作
            AuditLog.objects.create(
                user=request.current_staff.username,
                action='export_report',
                target_model='evaluationbatch',
                target_id=batch.id,
                description=f'导出批次详细报告: {batch.name}',
                extra_data={'export_type': 'batch_detail'}
            )
            
            # 这里可以集成Excel或PDF生成功能
            # 暂时返回成功响应
            messages.success(request, f'批次 {batch.name} 的详细报告导出请求已提交')
            return redirect('evaluations:admin:progress_detail', batch_id=batch_id)
            
        except Exception as e:
            logger.error(f"导出批次报告失败: {e}")
            messages.error(request, '导出报告失败，请重试')
            return redirect('evaluations:admin:progress_detail', batch_id=batch_id)


# 考评关系管理视图类
class RelationListView(ListView):
    """
    考评关系列表视图
    显示所有考评关系，支持筛选、搜索和批量操作
    """
    model = EvaluationRelation
    template_name = 'admin/relation/list.html'
    context_object_name = 'relations'
    paginate_by = 20

    @require_admin_permission 
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)

    def get_queryset(self):
        """获取考评关系列表数据"""
        try:
            queryset = EvaluationRelation.objects.filter(
                deleted_at__isnull=True
            ).select_related(
                'batch', 'evaluator', 'evaluatee', 'template'
            ).order_by('-created_at')

            # 批次筛选
            batch_id = self.request.GET.get('batch')
            if batch_id:
                queryset = queryset.filter(batch_id=batch_id)

            # 部门筛选
            department_id = self.request.GET.get('department')
            if department_id:
                queryset = queryset.filter(
                    Q(evaluator__department_id=department_id) |
                    Q(evaluatee__department_id=department_id)
                )

            # 状态筛选
            assigned = self.request.GET.get('assigned')
            if assigned == 'true':
                queryset = queryset.filter(is_assigned=True)
            elif assigned == 'false':
                queryset = queryset.filter(is_assigned=False)

            # 搜索功能
            search = self.request.GET.get('search')
            if search:
                queryset = queryset.filter(
                    Q(evaluator__name__icontains=search) |
                    Q(evaluatee__name__icontains=search) |
                    Q(batch__name__icontains=search)
                )

            return queryset

        except Exception as e:
            logger.error(f"获取考评关系列表失败: {e}")
            return EvaluationRelation.objects.none()

    def get_context_data(self, **kwargs):
        """获取模板上下文数据"""
        context = super().get_context_data(**kwargs)
        
        try:
            # 获取批次选项
            context['batches'] = EvaluationBatch.objects.filter(
                deleted_at__isnull=True
            ).order_by('-created_at')
            
            # 获取部门选项
            context['departments'] = Department.objects.filter(
                deleted_at__isnull=True
            ).order_by('name')
            
            # 统计数据
            total_relations = self.get_queryset().count()
            assigned_relations = self.get_queryset().filter(is_assigned=True).count()
            pending_relations = total_relations - assigned_relations
            
            context['stats'] = {
                'total_relations': total_relations,
                'assigned_relations': assigned_relations,
                'pending_relations': pending_relations,
                'assignment_rate': round(assigned_relations / total_relations * 100, 1) if total_relations > 0 else 0
            }
            
            # 当前筛选条件
            context['current_filters'] = {
                'batch': self.request.GET.get('batch', ''),
                'department': self.request.GET.get('department', ''),
                'assigned': self.request.GET.get('assigned', ''),
                'search': self.request.GET.get('search', '')
            }
            
        except Exception as e:
            logger.error(f"获取关系管理上下文数据失败: {e}")
            
        return context


class RelationCreateView(CreateView):
    """
    考评关系创建视图
    支持手动创建新的考评关系
    """
    model = EvaluationRelation
    template_name = 'admin/relation/form.html'
    fields = ['batch', 'evaluator', 'evaluatee', 'template', 'weight_factor', 'assignment_method']
    success_url = reverse_lazy('evaluations:admin:relation_list')

    @require_admin_permission
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)

    def get_context_data(self, **kwargs):
        """获取表单上下文数据"""
        context = super().get_context_data(**kwargs)
        context['action'] = 'create'
        context['title'] = '新建考评关系'
        
        # 添加部门列表用于筛选
        context['departments'] = Department.objects.filter(
            deleted_at__isnull=True
        ).order_by('name')
        
        return context

    def form_valid(self, form):
        """表单验证成功后的处理"""
        try:
            # 检查关系是否已存在
            existing = EvaluationRelation.objects.filter(
                batch=form.cleaned_data['batch'],
                evaluator=form.cleaned_data['evaluator'],
                evaluatee=form.cleaned_data['evaluatee'],
                deleted_at__isnull=True
            ).first()

            if existing:
                messages.error(self.request, '该考评关系已存在，无法重复创建')
                return self.form_invalid(form)

            # 检查自评关系
            if form.cleaned_data['evaluator'] == form.cleaned_data['evaluatee']:
                messages.error(self.request, '评价者和被评价者不能是同一人')
                return self.form_invalid(form)

            # 设置创建人和默认值
            form.instance.created_by = self.request.current_staff
            form.instance.is_assigned = True

            with transaction.atomic():
                response = super().form_valid(form)

                # 记录审计日志
                AuditLog.objects.create(
                    user=self.request.current_staff.username,
                    action='create',
                    target_model='evaluationrelation',
                    target_id=self.object.id,
                    description=f'手动创建考评关系: {form.cleaned_data["evaluator"].name} → {form.cleaned_data["evaluatee"].name}',
                    extra_data={
                        'batch_id': form.cleaned_data['batch'].id,
                        'batch_name': form.cleaned_data['batch'].name,
                        'weight_factor': float(form.cleaned_data['weight_factor'])
                    }
                )

                messages.success(self.request, '考评关系创建成功')
                return response

        except Exception as e:
            logger.error(f"创建考评关系失败: {e}")
            messages.error(self.request, '创建考评关系失败，请重试')
            return self.form_invalid(form)


class RelationUpdateView(UpdateView):
    """
    考评关系更新视图
    支持修改现有的考评关系
    """
    model = EvaluationRelation
    template_name = 'admin/relation/form.html'
    fields = ['batch', 'evaluator', 'evaluatee', 'template', 'weight_factor', 'assignment_method']
    success_url = reverse_lazy('evaluations:admin:relation_list')

    @require_admin_permission
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)

    def get_object(self, queryset=None):
        """获取要编辑的对象"""
        return get_object_or_404(
            EvaluationRelation,
            pk=self.kwargs['pk'],
            deleted_at__isnull=True
        )

    def get_context_data(self, **kwargs):
        """获取表单上下文数据"""
        context = super().get_context_data(**kwargs)
        context['action'] = 'update'
        context['title'] = '编辑考评关系'
        
        # 添加部门列表用于筛选
        context['departments'] = Department.objects.filter(
            deleted_at__isnull=True
        ).order_by('name')
        
        return context

    def form_valid(self, form):
        """表单验证成功后的处理"""
        try:
            # 检查关系是否已存在（排除当前对象）
            existing = EvaluationRelation.objects.filter(
                batch=form.cleaned_data['batch'],
                evaluator=form.cleaned_data['evaluator'],
                evaluatee=form.cleaned_data['evaluatee'],
                deleted_at__isnull=True
            ).exclude(pk=self.object.pk).first()

            if existing:
                messages.error(self.request, '该考评关系已存在，无法重复创建')
                return self.form_invalid(form)

            # 检查自评关系
            if form.cleaned_data['evaluator'] == form.cleaned_data['evaluatee']:
                messages.error(self.request, '评价者和被评价者不能是同一人')
                return self.form_invalid(form)

            # 设置更新人
            form.instance.updated_by = self.request.current_staff

            with transaction.atomic():
                # 记录原始数据
                original_data = {
                    'evaluator': self.object.evaluator.name,
                    'evaluatee': self.object.evaluatee.name,
                    'weight_factor': float(self.object.weight_factor)
                }

                response = super().form_valid(form)

                # 记录审计日志
                AuditLog.objects.create(
                    user=self.request.current_staff.username,
                    action='update',
                    target_model='evaluationrelation',
                    target_id=self.object.id,
                    description=f'修改考评关系: {form.cleaned_data["evaluator"].name} → {form.cleaned_data["evaluatee"].name}',
                    extra_data={
                        'original': original_data,
                        'updated': {
                            'evaluator': form.cleaned_data['evaluator'].name,
                            'evaluatee': form.cleaned_data['evaluatee'].name,
                            'weight_factor': float(form.cleaned_data['weight_factor'])
                        }
                    }
                )

                messages.success(self.request, '考评关系修改成功')
                return response

        except Exception as e:
            logger.error(f"修改考评关系失败: {e}")
            messages.error(self.request, '修改考评关系失败，请重试')
            return self.form_invalid(form)


class RelationDeleteView(View):
    """
    考评关系删除视图
    支持软删除考评关系
    """
    
    @require_admin_permission
    def post(self, request, pk):
        """处理删除请求"""
        try:
            relation = get_object_or_404(
                EvaluationRelation,
                pk=pk,
                deleted_at__isnull=True
            )

            # 检查是否已有评分记录
            has_records = EvaluationRecord.objects.filter(
                relation=relation,
                deleted_at__isnull=True
            ).exists()

            if has_records:
                return JsonResponse({
                    'success': False,
                    'error': '该考评关系已有评分记录，无法删除'
                })

            with transaction.atomic():
                # 软删除
                relation.deleted_at = timezone.now()
                relation.deleted_by = request.current_staff
                relation.save()

                # 记录审计日志
                AuditLog.objects.create(
                    user=request.current_staff.username,
                    action='delete',
                    target_model='evaluationrelation',
                    target_id=relation.id,
                    description=f'删除考评关系: {relation.evaluator.name} → {relation.evaluatee.name}',
                    extra_data={
                        'batch_id': relation.batch.id,
                        'batch_name': relation.batch.name,
                        'reason': '手动删除'
                    }
                )

                return JsonResponse({
                    'success': True,
                    'message': '考评关系删除成功'
                })

        except Exception as e:
            logger.error(f"删除考评关系失败: {e}")
            return JsonResponse({
                'success': False,
                'error': '删除失败，请重试'
            })


class RelationBatchActionView(View):
    """
    考评关系批量操作视图
    支持批量删除、批量修改权重等操作
    """
    
    @require_admin_permission
    def post(self, request):
        """处理批量操作请求"""
        try:
            action = request.POST.get('action')
            relation_ids = request.POST.getlist('relation_ids')

            if not action or not relation_ids:
                return JsonResponse({
                    'success': False,
                    'error': '缺少操作类型或选择项'
                })

            # 获取要操作的关系
            relations = EvaluationRelation.objects.filter(
                id__in=relation_ids,
                deleted_at__isnull=True
            )

            if not relations.exists():
                return JsonResponse({
                    'success': False,
                    'error': '没有找到有效的考评关系'
                })

            with transaction.atomic():
                if action == 'delete':
                    # 批量删除
                    deleted_count = 0
                    for relation in relations:
                        # 检查是否已有评分记录
                        has_records = EvaluationRecord.objects.filter(
                            relation=relation,
                            deleted_at__isnull=True
                        ).exists()

                        if not has_records:
                            relation.deleted_at = timezone.now()
                            relation.deleted_by = request.current_staff
                            relation.save()
                            deleted_count += 1

                    # 记录审计日志
                    AuditLog.objects.create(
                        user=request.current_staff.username,
                        action='batch_delete',
                        target_model='evaluationrelation',
                        description=f'批量删除考评关系: {deleted_count}条',
                        extra_data={
                            'deleted_count': deleted_count,
                            'total_selected': len(relation_ids)
                        }
                    )

                    return JsonResponse({
                        'success': True,
                        'message': f'成功删除 {deleted_count} 条考评关系'
                    })

                elif action == 'update_weight':
                    # 批量修改权重
                    new_weight = request.POST.get('new_weight')
                    if not new_weight:
                        return JsonResponse({
                            'success': False,
                            'error': '缺少新权重值'
                        })

                    try:
                        weight_value = float(new_weight)
                        if weight_value <= 0 or weight_value > 5:
                            return JsonResponse({
                                'success': False,
                                'error': '权重值必须在0-5之间'
                            })
                    except ValueError:
                        return JsonResponse({
                            'success': False,
                            'error': '权重值格式不正确'
                        })

                    updated_count = relations.update(
                        weight_factor=weight_value,
                        updated_by=request.current_staff,
                        updated_at=timezone.now()
                    )

                    # 记录审计日志
                    AuditLog.objects.create(
                        user=request.current_staff.username,
                        action='batch_update',
                        target_model='evaluationrelation',
                        description=f'批量修改考评关系权重: {updated_count}条',
                        extra_data={
                            'updated_count': updated_count,
                            'new_weight': weight_value
                        }
                    )

                    return JsonResponse({
                        'success': True,
                        'message': f'成功修改 {updated_count} 条考评关系的权重'
                    })

                else:
                    return JsonResponse({
                        'success': False,
                        'error': '不支持的操作类型'
                    })

        except Exception as e:
            logger.error(f"批量操作失败: {e}")
            return JsonResponse({
                'success': False,
                'error': '批量操作失败，请重试'
            })


class ProgressExportView(View):
    """考评进度导出视图"""
    
    @require_admin_permission
    def post(self, request):
        """导出考评进度数据"""
        try:
            # 获取筛选参数
            batch_ids = request.POST.getlist('batch_ids', [])
            export_format = request.POST.get('format', 'excel')
            
            if not batch_ids:
                return JsonResponse({
                    'success': False,
                    'error': '请选择要导出的批次'
                })
            
            # 获取批次数据
            batches = EvaluationBatch.objects.filter(
                id__in=batch_ids,
                deleted_at__isnull=True
            ).select_related('default_template')
            
            if not batches.exists():
                return JsonResponse({
                    'success': False,
                    'error': '没有找到有效的批次数据'
                })
            
            # 构建导出数据
            export_data = []
            for batch in batches:
                # 获取批次统计数据
                total_relations = EvaluationRelation.objects.filter(
                    batch=batch,
                    deleted_at__isnull=True
                ).count()
                
                completed_relations = EvaluationRecord.objects.filter(
                    relation__batch=batch,
                    deleted_at__isnull=True
                ).count()
                
                completion_rate = 0
                if total_relations > 0:
                    completion_rate = round((completed_relations / total_relations) * 100, 2)
                
                # 获取参与人员统计
                participants = EvaluationRelation.objects.filter(
                    batch=batch,
                    deleted_at__isnull=True
                ).values('evaluator').distinct().count()
                
                export_data.append({
                    'batch_name': batch.name,
                    'batch_status': batch.get_status_display(),
                    'start_date': batch.start_date.strftime('%Y-%m-%d') if batch.start_date else '',
                    'end_date': batch.end_date.strftime('%Y-%m-%d') if batch.end_date else '',
                    'total_relations': total_relations,
                    'completed_relations': completed_relations,
                    'completion_rate': f'{completion_rate}%',
                    'participants': participants,
                    'template_name': batch.default_template.name if batch.default_template else '',
                    'created_at': batch.created_at.strftime('%Y-%m-%d %H:%M:%S')
                })
            
            if export_format == 'excel':
                # 使用Excel导出
                from common.excel_utils import ExcelProcessor
                processor = ExcelProcessor(request.current_staff.id)
                
                # 定义字段映射
                field_mapping = {
                    'batch_name': '批次名称',
                    'batch_status': '批次状态',
                    'start_date': '开始日期',
                    'end_date': '结束日期',
                    'total_relations': '总考评关系数',
                    'completed_relations': '已完成关系数',
                    'completion_rate': '完成率',
                    'participants': '参与人数',
                    'template_name': '使用模板',
                    'created_at': '创建时间'
                }
                
                # 将字典数据转换为对象形式以便导出
                from types import SimpleNamespace
                formatted_data = [SimpleNamespace(**item) for item in export_data]
                
                # 生成文件名
                from django.utils import timezone
                filename = f'考评进度统计_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
                
                # 记录审计日志
                from common.models import AuditLog
                AuditLog.objects.create(
                    user=request.current_staff.username,
                    action='export',
                    target_model='EvaluationProgress',
                    description=f'导出考评进度统计: {len(batch_ids)}个批次',
                    ip_address=request.META.get('REMOTE_ADDR'),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )
                
                return processor.export_data(formatted_data, 'progress_statistics', field_mapping, filename)
            
            else:
                # JSON格式导出
                return JsonResponse({
                    'success': True,
                    'data': export_data
                })
                
        except Exception as e:
            logger.error(f"导出考评进度失败: {e}")
            return JsonResponse({
                'success': False,
                'error': f'导出失败: {str(e)}'
            })


class BatchTemplateConfigView(View):
    """批次模板配置视图"""
    
    def get(self, request, batch_id):
        """显示批次模板配置页面"""
        batch = get_object_or_404(EvaluationBatch, id=batch_id, deleted_at__isnull=True)
        form = BatchTemplateConfigForm(batch=batch)
        
        # 获取当前批次的模板配置
        current_templates = BatchTemplate.objects.filter(batch=batch).select_related('template')
        
        context = {
            'batch': batch,
            'form': form,
            'current_templates': current_templates,
            'page_title': f'配置批次模板 - {batch.name}',
        }
        
        return render(request, 'admin/batch/template_config.html', context)
    
    def post(self, request, batch_id):
        """保存批次模板配置"""
        batch = get_object_or_404(EvaluationBatch, id=batch_id, deleted_at__isnull=True)
        form = BatchTemplateConfigForm(batch=batch, data=request.POST)
        
        if form.is_valid():
            try:
                with transaction.atomic():
                    form.save(batch)
                    
                    # 记录审计日志
                    AuditLog.objects.create(
                        user=request.current_staff.username if hasattr(request.current_staff, 'username') else str(request.current_staff),
                        action='update',
                        target_model='EvaluationBatch',
                        target_id=batch.id,
                        description=f'配置批次模板: {batch.name}',
                        ip_address=request.META.get('REMOTE_ADDR'),
                        user_agent=request.META.get('HTTP_USER_AGENT', '')
                    )
                    
                    messages.success(request, f'批次 "{batch.name}" 的模板配置已成功保存！')
                    return redirect('evaluations:admin:batch_list')
                    
            except Exception as e:
                logger.error(f"保存批次模板配置失败: {e}")
                messages.error(request, f'保存失败: {str(e)}')
        
        # 表单验证失败，重新显示页面
        current_templates = BatchTemplate.objects.filter(batch=batch).select_related('template')
        
        context = {
            'batch': batch,
            'form': form,
            'current_templates': current_templates,
            'page_title': f'配置批次模板 - {batch.name}',
        }
        
        return render(request, 'admin/batch/template_config.html', context)
