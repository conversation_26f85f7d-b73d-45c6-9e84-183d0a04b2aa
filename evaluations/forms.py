# -*- coding: utf-8 -*-
"""
考评应用表单定义
包含考评批次、模板等相关表单
"""

from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import EvaluationBatch, EvaluationTemplate, BatchTemplate


class DateTimePickerWidget(forms.DateTimeInput):
    """自定义日期时间选择器组件"""
    
    def __init__(self, attrs=None, format=None):
        default_attrs = {
            'class': 'w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200',
            'type': 'datetime-local'
        }
        if attrs:
            default_attrs.update(attrs)
        super().__init__(attrs=default_attrs, format=format)


class EvaluationBatchForm(forms.ModelForm):
    """考评批次表单"""
    
    class Meta:
        model = EvaluationBatch
        fields = ['name', 'description', 'default_template', 'start_date', 'end_date']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200',
                'placeholder': '请输入批次名称，如：2024年第一季度绩效考评'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none',
                'rows': 4,
                'placeholder': '请描述本次考评的目的、范围和要求...'
            }),
            'default_template': forms.Select(attrs={
                'class': 'w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 appearance-none bg-white'
            }),
            'start_date': DateTimePickerWidget(),
            'end_date': DateTimePickerWidget(),
        }
        labels = {
            'name': '批次名称',
            'description': '批次描述',
            'default_template': '默认评价模板',
            'start_date': '开始时间',
            'end_date': '结束时间',
        }
        help_texts = {
            'name': '为本次考评批次起一个清晰的名称',
            'description': '详细描述本次考评的目的、范围和特殊要求',
            'default_template': '选择本批次默认使用的考评模板',
            'start_date': '考评活动的开始时间',
            'end_date': '考评活动的结束时间',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 设置默认模板的查询集（只显示活跃的模板）
        self.fields['default_template'].queryset = EvaluationTemplate.objects.filter(
            is_active=True,
            deleted_at__isnull=True
        ).order_by('name')
        
        # 设置默认时间（如果是新建）
        if not self.instance.pk:
            now = timezone.now()
            # 默认开始时间为明天
            default_start = now.replace(hour=9, minute=0, second=0, microsecond=0) + timezone.timedelta(days=1)
            # 默认结束时间为一周后
            default_end = default_start + timezone.timedelta(days=7)
            default_end = default_end.replace(hour=18, minute=0, second=0, microsecond=0)
            
            self.fields['start_date'].initial = default_start
            self.fields['end_date'].initial = default_end

    def clean(self):
        """表单整体验证"""
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        
        if start_date and end_date:
            # 检查结束时间是否晚于开始时间
            if end_date <= start_date:
                raise ValidationError({
                    'end_date': '结束时间必须晚于开始时间'
                })
            
            # 检查时间间隔是否合理（至少1小时）
            time_diff = end_date - start_date
            if time_diff.total_seconds() < 3600:  # 1小时
                raise ValidationError({
                    'end_date': '考评时间间隔至少需要1小时'
                })
            
            # 检查是否设置了过去的时间
            now = timezone.now()
            if start_date < now:
                raise ValidationError({
                    'start_date': '开始时间不能早于当前时间'
                })
        
        return cleaned_data

    def clean_name(self):
        """批次名称验证"""
        name = self.cleaned_data.get('name')
        if name:
            name = name.strip()
            if len(name) < 2:
                raise ValidationError('批次名称至少需要2个字符')
            if len(name) > 100:
                raise ValidationError('批次名称不能超过100个字符')
            
            # 检查名称是否重复（排除当前实例）
            existing = EvaluationBatch.objects.filter(
                name=name,
                deleted_at__isnull=True
            )
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError('该批次名称已存在，请使用其他名称')
        
        return name


class BatchTemplateConfigForm(forms.Form):
    """批次模板配置表单"""
    
    # 可用模板多选框
    available_templates = forms.ModelMultipleChoiceField(
        queryset=EvaluationTemplate.objects.filter(is_active=True, deleted_at__isnull=True),
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'template-checkbox'
        }),
        label='可用模板',
        help_text='选择本批次可以使用的考评模板',
        required=False
    )
    
    def __init__(self, batch=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.batch = batch
        
        # 如果是编辑现有批次，预填充已选择的模板
        if batch:
            selected_templates = batch.templates.all()
            self.fields['available_templates'].initial = selected_templates
            
        # 为每个关系类型创建首选模板选择字段
        relation_types = [
            ('subordinate_to_superior', '下级评上级'),
            ('superior_to_subordinate', '上级评下级'),
            ('peer_to_peer', '同级互评'),
            ('cross_department', '跨部门评价'),
            ('all', '通用模板'),
        ]
        
        for relation_code, relation_name in relation_types:
            field_name = f'preferred_{relation_code}'
            self.fields[field_name] = forms.ModelChoiceField(
                queryset=EvaluationTemplate.objects.filter(is_active=True, deleted_at__isnull=True),
                widget=forms.Select(attrs={
                    'class': 'w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                }),
                label=f'{relation_name}首选模板',
                help_text=f'选择{relation_name}关系的首选模板',
                required=False,
                empty_label='未设置'
            )
            
            # 如果是编辑现有批次，预填充首选模板
            if batch:
                try:
                    preferred = BatchTemplate.objects.get(
                        batch=batch,
                        relation_type=relation_code,
                        is_preferred=True
                    )
                    self.fields[field_name].initial = preferred.template
                except BatchTemplate.DoesNotExist:
                    pass
    
    def save(self, batch):
        """保存批次模板配置"""
        if not batch:
            return
            
        # 清除现有的批次模板关联
        BatchTemplate.objects.filter(batch=batch).delete()
        
        # 获取选择的模板
        selected_templates = self.cleaned_data.get('available_templates', [])
        
        # 获取各关系类型的首选模板
        relation_types = [
            'subordinate_to_superior',
            'superior_to_subordinate', 
            'peer_to_peer',
            'cross_department',
            'all'
        ]
        
        preferred_templates = {}
        for relation_type in relation_types:
            field_name = f'preferred_{relation_type}'
            preferred_template = self.cleaned_data.get(field_name)
            if preferred_template:
                preferred_templates[relation_type] = preferred_template
        
        # 创建批次模板关联
        for sort_order, template in enumerate(selected_templates):
            # 检查是否为某个关系类型的首选模板
            for relation_type, preferred_template in preferred_templates.items():
                if template == preferred_template:
                    BatchTemplate.objects.create(
                        batch=batch,
                        template=template,
                        relation_type=relation_type,
                        is_preferred=True,
                        sort_order=sort_order
                    )
                    break
            else:
                # 不是首选模板，创建通用关联
                BatchTemplate.objects.create(
                    batch=batch,
                    template=template,
                    relation_type='all',
                    is_preferred=False,
                    sort_order=sort_order
                )
