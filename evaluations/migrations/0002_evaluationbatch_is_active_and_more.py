# Generated by Django 5.2.3 on 2025-07-31 07:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('evaluations', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='evaluationbatch',
            name='is_active',
            field=models.BooleanField(default=True, help_text='批次是否启用，禁用后不会在列表中显示', verbose_name='是否启用'),
        ),
        migrations.AlterField(
            model_name='evaluationbatch',
            name='default_template',
            field=models.ForeignKey(help_text='该批次默认使用的考评模板', on_delete=django.db.models.deletion.CASCADE, related_name='default_batches', to='evaluations.evaluationtemplate', verbose_name='默认模板'),
        ),
        migrations.CreateModel(
            name='BatchTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('relation_type', models.CharField(choices=[('subordinate_to_superior', '下级评上级'), ('superior_to_subordinate', '上级评下级'), ('peer_to_peer', '同级互评'), ('cross_department', '跨部门评价'), ('all', '通用模板')], default='all', help_text='该模板适用的评价关系类型', max_length=30, verbose_name='适用关系类型')),
                ('is_preferred', models.BooleanField(default=False, help_text='是否为该关系类型的首选模板', verbose_name='是否首选')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='模板在批次中的显示顺序', verbose_name='排序')),
                ('batch', models.ForeignKey(help_text='关联的考评批次', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationbatch', verbose_name='所属批次')),
                ('template', models.ForeignKey(help_text='该批次可使用的模板', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationtemplate', verbose_name='可用模板')),
            ],
            options={
                'verbose_name': '批次模板',
                'verbose_name_plural': '批次模板',
                'ordering': ['sort_order', 'template__name'],
                'unique_together': {('batch', 'template')},
            },
        ),
        migrations.AddField(
            model_name='evaluationbatch',
            name='templates',
            field=models.ManyToManyField(help_text='该批次可使用的考评模板集合', related_name='available_batches', through='evaluations.BatchTemplate', to='evaluations.evaluationtemplate', verbose_name='可用模板'),
        ),
    ]
