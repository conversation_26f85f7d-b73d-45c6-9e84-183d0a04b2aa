<!DOCTYPE html>
<html>
<head>
    <title>批量删除调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .debug-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .debug-result { background: #f5f5f5; padding: 10px; border-radius: 3px; margin: 5px 0; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>匿名编号批量删除调试工具</h1>
    
    <div class="debug-section">
        <div class="debug-title">1. 检查页面元素</div>
        <button onclick="checkPageElements()">检查页面元素</button>
        <div id="elementsResult" class="debug-result"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">2. 检查选中的员工</div>
        <button onclick="checkSelectedStaff()">检查选中员工</button>
        <div id="selectedResult" class="debug-result"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">3. 模拟批量删除函数调用</div>
        <button onclick="simulateBatchDelete()">模拟函数调用</button>
        <div id="simulateResult" class="debug-result"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">4. 检查JavaScript错误</div>
        <button onclick="checkJSErrors()">检查JS错误</button>
        <div id="errorsResult" class="debug-result"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">5. 测试网络请求</div>
        <button onclick="testNetworkRequest()">测试网络请求</button>
        <div id="networkResult" class="debug-result"></div>
    </div>

    <script>
        // 存储JavaScript错误
        let jsErrors = [];
        window.addEventListener('error', function(e) {
            jsErrors.push({
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                error: e.error
            });
        });

        function checkPageElements() {
            const result = document.getElementById('elementsResult');
            let html = '<h4>页面元素检查结果：</h4>';
            
            // 检查关键元素
            const elements = [
                { id: 'batchDeleteAnonymousBtn', name: '批量删除按钮' },
                { id: 'selectAllCodes', name: '全选复选框' },
                { class: 'code-checkbox', name: '员工复选框' },
                { id: 'batchAnonymousDeleteModal', name: '删除确认模态框' },
                { id: 'selectedAnonymousCount', name: '选中计数显示' }
            ];
            
            elements.forEach(elem => {
                let element;
                if (elem.id) {
                    element = document.getElementById(elem.id);
                } else if (elem.class) {
                    element = document.querySelector('.' + elem.class);
                }
                
                if (element) {
                    html += `<div class="success">✓ ${elem.name}: 存在</div>`;
                    if (elem.id === 'batchDeleteAnonymousBtn') {
                        html += `<div>  - 是否禁用: ${element.disabled}</div>`;
                        html += `<div>  - 点击事件: ${element.onclick ? '有' : '无'}</div>`;
                    }
                } else {
                    html += `<div class="error">✗ ${elem.name}: 不存在</div>`;
                }
            });
            
            result.innerHTML = html;
        }
        
        function checkSelectedStaff() {
            const result = document.getElementById('selectedResult');
            const checkboxes = document.querySelectorAll('.code-checkbox');
            const selectedCheckboxes = document.querySelectorAll('.code-checkbox:checked');
            
            let html = '<h4>员工选择状态：</h4>';
            html += `<div>总员工数: ${checkboxes.length}</div>`;
            html += `<div>已选择员工数: ${selectedCheckboxes.length}</div>`;
            
            if (selectedCheckboxes.length > 0) {
                html += '<h5>已选择员工详情：</h5>';
                selectedCheckboxes.forEach((checkbox, index) => {
                    const row = checkbox.closest('tr');
                    if (row) {
                        const staffName = row.querySelector('td:nth-child(2) .font-medium')?.textContent || '未知';
                        const hasNewCode = row.querySelector('td:nth-child(4) .bg-green-100');
                        html += `<div>${index + 1}. ${staffName} - ${hasNewCode ? '有新编号' : '无新编号'}</div>`;
                    }
                });
            }
            
            result.innerHTML = html;
        }
        
        function simulateBatchDelete() {
            const result = document.getElementById('simulateResult');
            let html = '<h4>模拟批量删除函数调用：</h4>';
            
            try {
                // 检查函数是否存在
                if (typeof showBatchDeleteAnonymousConfirm === 'function') {
                    html += '<div class="success">✓ showBatchDeleteAnonymousConfirm 函数存在</div>';
                    
                    // 模拟调用
                    html += '<div>尝试调用函数...</div>';
                    showBatchDeleteAnonymousConfirm();
                    html += '<div class="success">✓ 函数调用成功</div>';
                } else {
                    html += '<div class="error">✗ showBatchDeleteAnonymousConfirm 函数不存在</div>';
                }
            } catch (error) {
                html += `<div class="error">✗ 函数调用出错: ${error.message}</div>`;
            }
            
            result.innerHTML = html;
        }
        
        function checkJSErrors() {
            const result = document.getElementById('errorsResult');
            let html = '<h4>JavaScript错误列表：</h4>';
            
            if (jsErrors.length === 0) {
                html += '<div class="success">✓ 暂无JavaScript错误</div>';
            } else {
                jsErrors.forEach((error, index) => {
                    html += `<div class="error">错误 ${index + 1}: ${error.message}</div>`;
                    html += `<div>文件: ${error.filename}:${error.lineno}:${error.colno}</div>`;
                });
            }
            
            // 检查控制台错误
            html += '<div class="warning">请同时检查浏览器控制台(F12)中的错误信息</div>';
            
            result.innerHTML = html;
        }
        
        function testNetworkRequest() {
            const result = document.getElementById('networkResult');
            let html = '<h4>网络请求测试：</h4>';
            
            // 检查CSRF token
            const csrfToken = getCookie('csrftoken');
            if (csrfToken) {
                html += '<div class="success">✓ CSRF Token 存在</div>';
            } else {
                html += '<div class="error">✗ CSRF Token 不存在</div>';
            }
            
            // 测试简单请求
            html += '<div>测试网络连接...</div>';
            
            fetch(window.location.pathname, {
                method: 'HEAD'
            })
            .then(response => {
                html += `<div class="success">✓ 网络连接正常 (状态码: ${response.status})</div>`;
                result.innerHTML = html;
            })
            .catch(error => {
                html += `<div class="error">✗ 网络连接失败: ${error.message}</div>`;
                result.innerHTML = html;
            });
            
            result.innerHTML = html;
        }
        
        // 获取Cookie的工具函数
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // 页面加载完成后自动执行基础检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkPageElements();
                checkSelectedStaff();
            }, 1000);
        });
    </script>
</body>
</html>