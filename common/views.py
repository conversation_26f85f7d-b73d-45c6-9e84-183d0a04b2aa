from django.shortcuts import render, redirect
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from common.security.jwt_auth import JW<PERSON>uthentication, TokenManager, extract_token_from_header
from common.security.decorators import require_auth
from django.contrib.auth.hashers import check_password
from datetime import datetime
import json
import logging

logger = logging.getLogger(__name__)


class AdminRequiredMixin:
    """
    自定义管理员认证Mixin
    替代Django的LoginRequiredMixin，使用我们的认证系统
    """
    def dispatch(self, request, *args, **kwargs):
        # 检查用户是否已通过我们的认证中间件
        if not hasattr(request, 'current_staff') or not request.current_staff:
            messages.error(request, '请先登录')
            return redirect('organizations:admin:login')
        
        # 检查管理员权限
        if not request.current_staff.is_manager:
            messages.error(request, '您没有访问权限')
            return redirect('organizations:admin:login')
        
        return super().dispatch(request, *args, **kwargs)

@csrf_exempt
@require_http_methods(["POST"])
@require_auth
def logout_view(request):
    """
    用户退出登录API
    
    功能：
    1. 撤销当前用户的JWT token
    2. 清理客户端存储的认证信息
    3. 记录退出日志
    
    请求格式：
    POST /api/logout/
    Headers: Authorization: Bearer <token>
    
    返回格式：
    {
        "success": true,
        "message": "退出登录成功",
        "data": {
            "revoked_tokens": 1
        }
    }
    """
    try:
        # 从请求头获取token
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            return JsonResponse({
                'success': False,
                'message': '缺少认证信息',
                'code': 'MISSING_AUTH'
            }, status=401)
        
        # 提取token
        from common.security.jwt_auth import extract_token_from_header
        token = extract_token_from_header(auth_header)
        if not token:
            return JsonResponse({
                'success': False,
                'message': '认证信息格式错误',
                'code': 'INVALID_AUTH_FORMAT'
            }, status=401)
        
        # 验证token并获取用户信息
        payload = JWTAuthentication.verify_token(token)
        if not payload:
            return JsonResponse({
                'success': False,
                'message': 'Token已失效',
                'code': 'INVALID_TOKEN'
            }, status=401)
        
        staff_id = payload.get('staff_id')
        token_id = payload.get('token_id')
        username = payload.get('username')
        
        # 撤销当前token
        JWTAuthentication.revoke_token(staff_id, token_id)
        
        # 记录退出日志
        logger.info(f"用户 {username}({staff_id}) 已退出登录，撤销token: {token_id}")
        
        # 清理session中的用户信息（如果有）
        if hasattr(request, 'session'):
            request.session.flush()
        
        return JsonResponse({
            'success': True,
            'message': '退出登录成功',
            'data': {
                'revoked_tokens': 1,
                'username': username,
                'logout_time': request.current_time.isoformat() if hasattr(request, 'current_time') else None
            }
        })
        
    except Exception as e:
        logger.error(f"退出登录失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': '退出登录失败',
            'code': 'LOGOUT_ERROR'
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
@require_auth
def logout_all_devices(request):
    """
    从所有设备登出（撤销用户的所有token）
    
    功能：
    1. 撤销用户的所有活跃token
    2. 强制所有设备重新登录
    3. 发送安全提醒
    
    请求格式：
    POST /api/logout/all/
    Headers: Authorization: Bearer <token>
    
    返回格式：
    {
        "success": true,
        "message": "已从所有设备退出登录",
        "data": {
            "revoked_tokens": "all",
            "security_notice": "您的账户已从所有设备登出"
        }
    }
    """
    try:
        # 从请求头获取token
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            return JsonResponse({
                'success': False,
                'message': '缺少认证信息',
                'code': 'MISSING_AUTH'
            }, status=401)
        
        # 提取token
        from common.security.jwt_auth import extract_token_from_header
        token = extract_token_from_header(auth_header)
        if not token:
            return JsonResponse({
                'success': False,
                'message': '认证信息格式错误',
                'code': 'INVALID_AUTH_FORMAT'
            }, status=401)
        
        # 验证token并获取用户信息
        payload = JWTAuthentication.verify_token(token)
        if not payload:
            return JsonResponse({
                'success': False,
                'message': 'Token已失效',
                'code': 'INVALID_TOKEN'
            }, status=401)
        
        staff_id = payload.get('staff_id')
        username = payload.get('username')
        
        # 撤销用户的所有token
        TokenManager.revoke_all_user_tokens(staff_id)
        
        # 记录安全日志
        logger.warning(f"用户 {username}({staff_id}) 已选择从所有设备退出登录")
        
        # 清理session
        if hasattr(request, 'session'):
            request.session.flush()
        
        return JsonResponse({
            'success': True,
            'message': '已从所有设备退出登录',
            'data': {
                'revoked_tokens': 'all',
                'security_notice': '您的账户已从所有设备登出，请重新登录',
                'logout_time': request.current_time.isoformat() if hasattr(request, 'current_time') else None
            }
        })
        
    except Exception as e:
        logger.error(f"从所有设备登出失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': '操作失败',
            'code': 'LOGOUT_ERROR'
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def login_view(request):
    """
    用户登录API - JWT认证
    
    功能：
    1. 验证用户凭据
    2. 生成JWT tokens
    3. 返回用户信息
    
    请求格式：
    POST /api/login/
    {
        "username": "admin",
        "password": "123456"
    }
    
    返回格式：
    {
        "success": true,
        "tokens": {
            "access_token": "...",
            "refresh_token": "...",
            "expires_in": 28800
        },
        "user": {
            "id": 1,
            "username": "admin",
            "name": "管理员",
            "role": "super_admin"
        }
    }
    """
    try:
        # 解析请求数据
        if request.content_type == 'application/json':
            data = json.loads(request.body)
        else:
            data = request.POST
            
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        
        if not username or not password:
            return JsonResponse({
                'success': False,
                'message': '用户名和密码不能为空',
                'code': 'MISSING_CREDENTIALS'
            }, status=400)
        
        # 查找用户
        from organizations.models import Staff
        try:
            staff = Staff.objects.get(
                username=username,
                is_active=True,
                deleted_at__isnull=True
            )
        except Staff.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': '用户名或密码错误',
                'code': 'INVALID_CREDENTIALS'
            }, status=401)
        
        # 验证密码
        if not check_password(password, staff.password):
            return JsonResponse({
                'success': False,
                'message': '用户名或密码错误',
                'code': 'INVALID_CREDENTIALS'
            }, status=401)
        
        # 生成JWT tokens
        tokens = JWTAuthentication.generate_tokens(staff)
        
        # 记录登录日志
        from organizations.models import StaffLoginLog
        StaffLoginLog.objects.create(
            staff=staff,
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')[:200],
            login_type='JWT',
            created_by=staff.username
        )
        
        logger.info(f"用户 {username} JWT登录成功")
        
        return JsonResponse({
            'success': True,
            'message': '登录成功',
            'tokens': tokens,
            'user': {
                'id': staff.id,
                'username': staff.username,
                'name': staff.name,
                'role': staff.role,
                'role_display': staff.get_role_display(),
                'department_id': staff.department_id,
                'department_name': staff.department.name if staff.department else None,
                'is_manager': staff.is_manager,
                'employee_no': staff.employee_no
            }
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': '请求格式错误',
            'code': 'INVALID_FORMAT'
        }, status=400)
    except Exception as e:
        logger.error(f"登录失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': '登录失败，请稍后重试',
            'code': 'LOGIN_ERROR'
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def refresh_token_view(request):
    """
    刷新JWT token API
    
    请求格式：
    POST /api/token/refresh/
    {
        "refresh_token": "..."
    }
    
    返回格式：
    {
        "success": true,
        "tokens": {
            "access_token": "...",
            "expires_in": 28800
        }
    }
    """
    try:
        data = json.loads(request.body)
        refresh_token = data.get('refresh_token')
        
        if not refresh_token:
            return JsonResponse({
                'success': False,
                'message': '缺少刷新token',
                'code': 'MISSING_REFRESH_TOKEN'
            }, status=400)
        
        # 使用refresh token获取新的access token
        tokens = JWTAuthentication.refresh_access_token(refresh_token)
        
        return JsonResponse({
            'success': True,
            'tokens': tokens,
            'message': 'Token刷新成功'
        })
        
    except Exception as e:
        logger.error(f"Token刷新失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': 'Token刷新失败，请重新登录',
            'code': 'REFRESH_ERROR'
        }, status=401)

def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

@require_auth
def get_token_status(request):
    """获取令牌状态"""
    return JsonResponse({
        'status': 'active',
        'user': request.user.username if hasattr(request, 'user') else None
    })

# 测试视图
def jwt_test_view(request):
    """JWT功能测试页面"""
    return render(request, 'common/jwt_test.html')

# 添加URL配置需要的视图列表
__all__ = ['AdminRequiredMixin', 'login_view', 'refresh_token_view', 'logout_view', 'logout_all_devices', 'get_token_status', 'jwt_test_view']
