# 数据库迁移说明

## 当前进度
✅ 已完成模型修改：
1. EvaluationBatch 添加 is_active 字段
2. 新增 BatchTemplate 中间表模型
3. EvaluationBatch 添加 templates 多对多关系
4. 添加便捷方法：get_available_templates(), get_preferred_template()

## 需要执行的迁移命令
**请在Windows系统中执行以下命令：**

```bash
# 进入项目目录
cd /mnt/d/code/newmachinecode/UniversalStaffEvaluation3

# 生成迁移文件
python manage.py makemigrations evaluations

# 执行迁移
python manage.py migrate
```

## 迁移说明
- 添加的 is_active 字段默认值为 True，不会影响现有数据
- BatchTemplate 表是新建的，不会影响现有数据
- templates 字段通过中间表实现，向后兼容

## 下一步
迁移完成后，继续实施阶段二的界面修改。