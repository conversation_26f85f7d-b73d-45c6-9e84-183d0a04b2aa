// 批量删除调试和修复脚本
// 在浏览器控制台中运行这些代码来排查问题

console.log('=== 匿名编号批量删除调试开始 ===');

// 1. 检查页面基本元素
function debugPageElements() {
    console.log('1. 检查页面元素:');
    
    const batchBtn = document.getElementById('batchDeleteAnonymousBtn');
    const selectAll = document.getElementById('selectAllCodes');
    const checkboxes = document.querySelectorAll('.code-checkbox');
    const modal = document.getElementById('batchAnonymousDeleteModal');
    
    console.log('批量删除按钮:', batchBtn ? '存在' : '不存在', batchBtn);
    console.log('全选复选框:', selectAll ? '存在' : '不存在', selectAll);
    console.log('员工复选框数量:', checkboxes.length);
    console.log('删除模态框:', modal ? '存在' : '不存在', modal);
    
    if (batchBtn) {
        console.log('批量删除按钮状态:');
        console.log('  - disabled:', batchBtn.disabled);
        console.log('  - onclick:', batchBtn.onclick);
        console.log('  - 类名:', batchBtn.className);
    }
}

// 2. 检查选中状态
function debugSelectedState() {
    console.log('2. 检查选中状态:');
    
    const allCheckboxes = document.querySelectorAll('.code-checkbox');
    const selectedCheckboxes = document.querySelectorAll('.code-checkbox:checked');
    
    console.log('总复选框数:', allCheckboxes.length);
    console.log('已选中数:', selectedCheckboxes.length);
    
    selectedCheckboxes.forEach((checkbox, index) => {
        const row = checkbox.closest('tr');
        if (row) {
            const staffName = row.querySelector('td:nth-child(2) .font-medium')?.textContent || '未知';
            const hasNewCode = row.querySelector('td:nth-child(4) .bg-green-100');
            console.log(`  选中 ${index + 1}: ${staffName}, 有新编号: ${hasNewCode ? '是' : '否'}`);
        }
    });
}

// 3. 手动触发批量删除
function debugTriggerBatchDelete() {
    console.log('3. 手动触发批量删除:');
    
    try {
        if (typeof showBatchDeleteAnonymousConfirm === 'function') {
            console.log('showBatchDeleteAnonymousConfirm 函数存在，尝试调用...');
            showBatchDeleteAnonymousConfirm();
            console.log('函数调用完成');
        } else {
            console.error('showBatchDeleteAnonymousConfirm 函数不存在！');
        }
    } catch (error) {
        console.error('调用批量删除函数时出错:', error);
    }
}

// 4. 检查事件监听器
function debugEventListeners() {
    console.log('4. 检查事件监听器:');
    
    const batchBtn = document.getElementById('batchDeleteAnonymousBtn');
    if (batchBtn) {
        console.log('批量删除按钮的事件监听器:');
        const listeners = getEventListeners(batchBtn);
        console.log(listeners);
    }
    
    // 检查复选框事件
    const checkboxes = document.querySelectorAll('.code-checkbox');
    console.log('复选框事件监听器数量:', checkboxes.length);
}

// 5. 修复函数 - 重新绑定事件
function fixBatchDeleteEvents() {
    console.log('5. 尝试修复批量删除功能:');
    
    // 重新定义选中计数更新函数
    window.updateSelectedAnonymousCount = function() {
        const selectedCheckboxes = document.querySelectorAll('.code-checkbox:checked');
        const count = selectedCheckboxes.length;
        
        console.log('更新选中计数:', count);
        
        // 更新显示
        const countElement = document.getElementById('selectedAnonymousCount');
        if (countElement) {
            countElement.textContent = `已选择 ${count} 个员工`;
        }
        
        // 更新按钮状态
        const batchDeleteBtn = document.getElementById('batchDeleteAnonymousBtn');
        if (batchDeleteBtn) {
            if (count > 0) {
                batchDeleteBtn.disabled = false;
                batchDeleteBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                console.log('启用批量删除按钮');
            } else {
                batchDeleteBtn.disabled = true;
                batchDeleteBtn.classList.add('opacity-50', 'cursor-not-allowed');
                console.log('禁用批量删除按钮');
            }
        }
    };
    
    // 重新绑定复选框事件
    const checkboxes = document.querySelectorAll('.code-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.removeEventListener('change', updateSelectedAnonymousCount);
        checkbox.addEventListener('change', updateSelectedAnonymousCount);
    });
    
    // 重新绑定全选事件
    const selectAll = document.getElementById('selectAllCodes');
    if (selectAll) {
        selectAll.removeEventListener('change', function() {});
        selectAll.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.code-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedAnonymousCount();
        });
    }
    
    // 重新绑定批量删除按钮事件
    const batchDeleteBtn = document.getElementById('batchDeleteAnonymousBtn');
    if (batchDeleteBtn) {
        batchDeleteBtn.onclick = function() {
            console.log('批量删除按钮被点击');
            showBatchDeleteAnonymousConfirm();
        };
    }
    
    console.log('事件重新绑定完成');
}

// 6. 测试网络连接
function debugNetworkConnection() {
    console.log('6. 测试网络连接:');
    
    // 检查CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    const csrfToken = getCookie('csrftoken');
    console.log('CSRF Token:', csrfToken ? '存在' : '不存在');
    
    // 测试请求
    fetch(window.location.pathname, { method: 'HEAD' })
        .then(response => {
            console.log('网络连接测试成功, 状态码:', response.status);
        })
        .catch(error => {
            console.error('网络连接测试失败:', error);
        });
}

// 执行所有调试步骤
function runFullDebug() {
    debugPageElements();
    debugSelectedState();
    debugEventListeners();
    debugNetworkConnection();
    
    console.log('\n=== 如果上述检查没有发现问题，尝试运行修复函数 ===');
    console.log('请运行: fixBatchDeleteEvents()');
    
    console.log('\n=== 然后手动测试批量删除 ===');
    console.log('请运行: debugTriggerBatchDelete()');
}

// 自动运行调试
runFullDebug();

console.log('=== 调试脚本加载完成 ===');
console.log('可用的调试函数:');
console.log('- debugPageElements() - 检查页面元素');
console.log('- debugSelectedState() - 检查选中状态');
console.log('- debugTriggerBatchDelete() - 手动触发批量删除');
console.log('- fixBatchDeleteEvents() - 修复事件绑定');
console.log('- runFullDebug() - 运行完整调试');