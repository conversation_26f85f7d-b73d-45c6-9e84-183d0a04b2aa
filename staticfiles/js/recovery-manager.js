/**
 * 数据恢复管理器
 * 用于恢复软删除的数据
 */
class RecoveryManager {
    constructor() {
        this.createModal();
        this.bindEvents();
    }

    /**
     * 创建恢复确认模态框
     */
    createModal() {
        const modalHtml = `
            <div id="recoveryConfirmModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center">
                                <i data-lucide="rotate-ccw" class="w-6 h-6 text-green-500 mr-3"></i>
                                <h3 class="text-lg font-medium text-gray-900">确认恢复</h3>
                            </div>
                        </div>
                        <div class="px-6 py-4">
                            <p class="text-gray-600" id="recoveryMessage">您确定要恢复此项吗？</p>
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">恢复原因</label>
                                <textarea id="recoveryReason" 
                                         class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                         rows="3"
                                         placeholder="请输入恢复原因..."></textarea>
                            </div>
                        </div>
                        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                            <button type="button" class="btn btn-secondary btn-sm" id="cancelRecovery">取消</button>
                            <button type="button" class="btn btn-success btn-sm" id="confirmRecovery">
                                <i data-lucide="rotate-ccw" class="w-4 h-4 mr-2"></i>
                                <span>恢复</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 将模态框添加到页面
        if (!document.getElementById('recoveryConfirmModal')) {
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 取消恢复
        document.addEventListener('click', (e) => {
            if (e.target.id === 'cancelRecovery' || e.target.closest('#cancelRecovery')) {
                this.hideModal();
            }
        });

        // 点击遮罩层关闭
        document.addEventListener('click', (e) => {
            if (e.target.id === 'recoveryConfirmModal') {
                this.hideModal();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !document.getElementById('recoveryConfirmModal').classList.contains('hidden')) {
                this.hideModal();
            }
        });
    }

    /**
     * 显示恢复确认对话框
     * @param {Object} options 恢复选项
     */
    confirmRecover(options) {
        const {
            title = '确认恢复',
            message = '您确定要恢复此项吗？',
            url,
            method = 'POST',
            onSuccess,
            onError,
            data = {}
        } = options;

        // 设置消息
        document.getElementById('recoveryMessage').textContent = message;
        document.getElementById('recoveryReason').value = '';
        
        // 显示模态框
        this.showModal();

        // 绑定确认恢复事件
        const confirmBtn = document.getElementById('confirmRecovery');
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        newConfirmBtn.addEventListener('click', async () => {
            const reason = document.getElementById('recoveryReason').value.trim();
            if (!reason) {
                this.showToast('请输入恢复原因', 'error');
                return;
            }

            await this.executeRecover({
                url,
                method,
                data: { ...data, reason },
                onSuccess,
                onError
            });
        });
    }

    /**
     * 执行恢复操作
     */
    async executeRecover({ url, method, data, onSuccess, onError }) {
        const confirmBtn = document.getElementById('confirmRecovery');
        const confirmSpan = confirmBtn.querySelector('span');
        const originalText = confirmSpan.textContent;

        try {
            // 显示加载状态
            confirmBtn.disabled = true;
            confirmSpan.textContent = '恢复中...';

            // 准备请求参数
            const requestOptions = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify(data)
            };

            // 添加认证头
            const token = localStorage.getItem('access_token');
            if (token) {
                requestOptions.headers['Authorization'] = `Bearer ${token}`;
            }

            // 发送请求
            const response = await fetch(url, requestOptions);
            const result = await response.json();

            if (response.ok && result.success !== false) {
                // 恢复成功
                this.hideModal();
                this.showToast('恢复成功', 'success');
                
                if (onSuccess) {
                    onSuccess(result);
                }
            } else {
                throw new Error(result.error || result.message || '恢复失败');
            }

        } catch (error) {
            console.error('恢复失败:', error);
            this.showToast('恢复失败: ' + error.message, 'error');
            
            if (onError) {
                onError(error);
            }
        } finally {
            // 恢复按钮状态
            confirmBtn.disabled = false;
            confirmSpan.textContent = originalText;
        }
    }

    /**
     * 批量恢复
     */
    confirmBatchRecover(options) {
        const {
            title = '确认批量恢复',
            message,
            items = [],
            url,
            onSuccess,
            onError
        } = options;

        const itemCount = items.length;
        const finalMessage = message || `您确定要恢复选中的 ${itemCount} 项吗？`;

        this.confirmRecover({
            title,
            message: finalMessage,
            url,
            method: 'POST',
            data: { items },
            onSuccess,
            onError
        });
    }

    /**
     * 显示模态框
     */
    showModal() {
        const modal = document.getElementById('recoveryConfirmModal');
        modal.classList.remove('hidden');
        
        // 聚焦到原因输入框
        setTimeout(() => {
            document.getElementById('recoveryReason').focus();
        }, 100);
        
        // 重新初始化图标
        if (window.lucide) {
            lucide.createIcons({ nameAttr: 'data-lucide' });
        }
    }

    /**
     * 隐藏模态框
     */
    hideModal() {
        document.getElementById('recoveryConfirmModal').classList.add('hidden');
    }

    /**
     * 获取CSRF令牌
     */
    getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return '';
    }

    /**
     * 显示提示消息
     */
    showToast(message, type = 'info') {
        // 创建提示元素
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 
            'bg-blue-500'
        } text-white`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// 创建全局恢复管理器实例
window.recoveryManager = new RecoveryManager();

// 导出便捷方法
window.confirmRecover = (options) => window.recoveryManager.confirmRecover(options);
window.confirmBatchRecover = (options) => window.recoveryManager.confirmBatchRecover(options);