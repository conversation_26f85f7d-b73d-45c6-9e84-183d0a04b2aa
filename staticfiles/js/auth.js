/**
 * 认证相关功能
 * 包含登录、退出、token管理等
 */

class AuthManager {
    constructor() {
        this.tokenKey = 'access_token';
        this.refreshTokenKey = 'refresh_token';
        this.userInfoKey = 'user_info';
        this.apiBaseUrl = '/api';
    }

    /**
     * 获取存储的token
     * @returns {string|null}
     */
    getToken() {
        return localStorage.getItem(this.tokenKey);
    }

    /**
     * 获取存储的刷新token
     * @returns {string|null}
     */
    getRefreshToken() {
        return localStorage.getItem(this.refreshTokenKey);
    }

    /**
     * 存储token信息
     * @param {object} tokens - token信息
     */
    setTokens(tokens) {
        if (tokens.access_token) {
            localStorage.setItem(this.tokenKey, tokens.access_token);
        }
        if (tokens.refresh_token) {
            localStorage.setItem(this.refreshTokenKey, tokens.refresh_token);
        }
    }

    /**
     * 存储用户信息
     * @param {object} userInfo - 用户信息
     */
    setUserInfo(userInfo) {
        localStorage.setItem(this.userInfoKey, JSON.stringify(userInfo));
    }

    /**
     * 获取用户信息
     * @returns {object|null}
     */
    getUserInfo() {
        const userInfo = localStorage.getItem(this.userInfoKey);
        return userInfo ? JSON.parse(userInfo) : null;
    }

    /**
     * 检查是否已登录
     * @returns {boolean}
     */
    isLoggedIn() {
        return !!this.getToken();
    }

    /**
     * 获取认证头
     * @returns {object}
     */
    getAuthHeaders() {
        const token = this.getToken();
        return token ? { 'Authorization': `Bearer ${token}` } : {};
    }

    /**
     * 用户退出登录
     * @param {boolean} allDevices - 是否从所有设备退出
     * @returns {Promise}
     */
    async logout(allDevices = false) {
        try {
            // 显示加载状态
            this.showLoading();

            const endpoint = allDevices ? '/logout/all/' : '/logout/';
            const response = await fetch(`${this.apiBaseUrl}${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getAuthHeaders()
                }
            });

            const data = await response.json();

            if (data.success) {
                // 清理本地存储
                this.clearLocalStorage();
                
                // 显示成功消息
                this.showMessage('success', data.message);
                
                // 延迟跳转，让用户看到消息
                setTimeout(() => {
                    window.location.href = '/login/';
                }, 1000);
                
                return data;
            } else {
                // 即使API调用失败，也清理本地存储并跳转到登录页
                this.clearLocalStorage();
                this.showMessage('error', data.message || '退出失败');
                setTimeout(() => {
                    window.location.href = '/login/';
                }, 1000);
                return data;
            }

        } catch (error) {
            console.error('退出登录失败:', error);
            // 网络错误时也清理本地存储
            this.clearLocalStorage();
            this.showMessage('error', '网络错误，请重新登录');
            setTimeout(() => {
                window.location.href = '/login/';
            }, 1000);
            return { success: false, message: '网络错误' };
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 清理本地存储
     */
    clearLocalStorage() {
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.refreshTokenKey);
        localStorage.removeItem(this.userInfoKey);
        
        // 清理其他可能存储的认证相关数据
        sessionStorage.clear();
    }

    /**
     * 检查token状态
     * @returns {Promise}
     */
    async checkTokenStatus() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/token/status/`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...this.getAuthHeaders()
                }
            });

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('检查token状态失败:', error);
            return { success: false, message: '网络错误' };
        }
    }

    /**
     * 自动刷新token（如果需要）
     * @returns {Promise}
     */
    async refreshTokenIfNeeded() {
        const status = await this.checkTokenStatus();
        if (status.success && status.data) {
            const expiresIn = status.data.expires_in;
            // 如果token将在5分钟内过期，则刷新
            if (expiresIn && expiresIn < 300) {
                return await this.refreshToken();
            }
        }
        return { success: true };
    }

    /**
     * 刷新token
     * @returns {Promise}
     */
    async refreshToken() {
        const refreshToken = this.getRefreshToken();
        if (!refreshToken) {
            return { success: false, message: '缺少刷新token' };
        }

        try {
            const response = await fetch('/api/token/refresh/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    refresh_token: refreshToken
                })
            });

            const data = await response.json();
            if (data.success) {
                this.setTokens(data.tokens);
                return { success: true, tokens: data.tokens };
            } else {
                // 刷新失败，需要重新登录
                this.clearLocalStorage();
                window.location.href = '/login/';
                return { success: false, message: '需要重新登录' };
            }
        } catch (error) {
            console.error('刷新token失败:', error);
            return { success: false, message: '网络错误' };
        }
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        // 创建或显示加载指示器
        let loader = document.getElementById('auth-loader');
        if (!loader) {
            loader = document.createElement('div');
            loader.id = 'auth-loader';
            loader.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            loader.innerHTML = `
                <div class="bg-white rounded-lg p-6 shadow-lg">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p class="text-center mt-2 text-gray-600">处理中...</p>
                </div>
            `;
            document.body.appendChild(loader);
        }
        loader.style.display = 'flex';
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loader = document.getElementById('auth-loader');
        if (loader) {
            loader.style.display = 'none';
        }
    }

    /**
     * 显示消息提示
     * @param {string} type - 消息类型 (success, error, warning)
     * @param {string} message - 消息内容
     */
    showMessage(type, message) {
        // 创建消息提示元素
        const messageEl = document.createElement('div');
        messageEl.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-yellow-500 text-white'
        }`;
        messageEl.textContent = message;
        
        document.body.appendChild(messageEl);
        
        // 自动移除
        setTimeout(() => {
            messageEl.style.transform = 'translateX(100%)';
            setTimeout(() => messageEl.remove(), 300);
        }, 3000);
    }

    /**
     * 初始化退出按钮事件
     */
    initLogoutButtons() {
        // 普通退出
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (confirm('确定要退出登录吗？')) {
                    this.logout();
                }
            });
        }

        // 从所有设备退出
        const logoutAllBtn = document.getElementById('logout-all-btn');
        if (logoutAllBtn) {
            logoutAllBtn.addEventListener('click', (e) => {
                e.preventDefault();
                if (confirm('确定要从所有设备退出登录吗？')) {
                    this.logout(true);
                }
            });
        }
    }
}

// 创建全局实例
window.authManager = new AuthManager();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    authManager.initLogoutButtons();
    
    // 定期刷新token（每10分钟检查一次）
    setInterval(() => {
        if (authManager.isLoggedIn()) {
            authManager.refreshTokenIfNeeded();
        }
    }, 10 * 60 * 1000);
});

// 导出供模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}