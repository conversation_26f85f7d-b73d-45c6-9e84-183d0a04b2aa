/**
 * 企业员工评价系统 - ECharts 主题配置
 * 基于设计系统的图表主题定制
 * 版本: 1.0.0
 */

// 获取 CSS 自定义变量值
function getCSSVariable(name) {
    return getComputedStyle(document.documentElement).getPropertyValue(name).trim();
}

// 企业评价系统主题配置
const corporateTheme = {
    // 主色板 - 基于设计系统的颜色
    color: [
        getCSSVariable('--primary-500') || '#3b82f6',
        getCSSVariable('--success-500') || '#10b981',
        getCSSVariable('--warning-500') || '#f59e0b',
        getCSSVariable('--error-500') || '#ef4444',
        getCSSVariable('--info-500') || '#06b6d4',
        getCSSVariable('--primary-400') || '#60a5fa',
        getCSSVariable('--success-400') || '#34d399',
        getCSSVariable('--warning-400') || '#fbbf24'
    ],

    // 背景色
    backgroundColor: 'transparent',

    // 文本样式
    textStyle: {
        fontFamily: getCSSVariable('--font-family-sans') || 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        color: getCSSVariable('--gray-700') || '#374151',
        fontSize: 12
    },

    // 标题样式
    title: {
        textStyle: {
            color: getCSSVariable('--gray-900') || '#111827',
            fontWeight: getCSSVariable('--font-semibold') || '600',
            fontSize: 16
        },
        subtextStyle: {
            color: getCSSVariable('--gray-600') || '#4b5563',
            fontSize: 12
        }
    },

    // 图例样式
    legend: {
        textStyle: {
            color: getCSSVariable('--gray-700') || '#374151',
            fontSize: 12
        },
        itemGap: 12,
        itemWidth: 14,
        itemHeight: 14
    },

    // 网格样式
    grid: {
        borderWidth: 0,
        backgroundColor: 'transparent',
        borderColor: 'transparent'
    },

    // 坐标轴样式
    categoryAxis: {
        axisLine: {
            show: true,
            lineStyle: {
                color: getCSSVariable('--gray-300') || '#d1d5db',
                width: 1
            }
        },
        axisTick: {
            show: false
        },
        axisLabel: {
            color: getCSSVariable('--gray-600') || '#4b5563',
            fontSize: 11
        },
        splitLine: {
            show: false
        }
    },

    valueAxis: {
        axisLine: {
            show: false
        },
        axisTick: {
            show: false
        },
        axisLabel: {
            color: getCSSVariable('--gray-600') || '#4b5563',
            fontSize: 11
        },
        splitLine: {
            show: true,
            lineStyle: {
                color: getCSSVariable('--gray-200') || '#e5e7eb',
                width: 1,
                type: 'solid'
            }
        }
    },

    // 柱状图样式
    bar: {
        itemStyle: {
            borderRadius: [4, 4, 0, 0],
            shadowBlur: 0
        }
    },

    // 线图样式
    line: {
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
            width: 3
        },
        itemStyle: {
            borderWidth: 2,
            shadowBlur: 0
        },
        areaStyle: {
            opacity: 0.1
        }
    },

    // 饼图样式
    pie: {
        itemStyle: {
            borderWidth: 2,
            borderColor: '#ffffff',
            shadowBlur: 0
        },
        label: {
            color: getCSSVariable('--gray-700') || '#374151',
            fontSize: 11
        },
        labelLine: {
            lineStyle: {
                color: getCSSVariable('--gray-400') || '#9ca3af'
            }
        }
    },

    // 散点图样式
    scatter: {
        itemStyle: {
            shadowBlur: 0,
            opacity: 0.8
        }
    },

    // 工具提示样式
    tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: getCSSVariable('--gray-300') || '#d1d5db',
        borderWidth: 1,
        textStyle: {
            color: getCSSVariable('--gray-900') || '#111827',
            fontSize: 12
        },
        extraCssText: `
            border-radius: ${getCSSVariable('--radius-md') || '6px'};
            box-shadow: ${getCSSVariable('--shadow-lg') || '0 10px 15px -3px rgba(0, 0, 0, 0.1)'};
            backdrop-filter: blur(8px);
        `
    },

    // 数据区域缩放组件样式
    dataZoom: {
        backgroundColor: getCSSVariable('--gray-100') || '#f3f4f6',
        dataBackgroundColor: getCSSVariable('--gray-200') || '#e5e7eb',
        selectedDataBackgroundColor: getCSSVariable('--primary-200') || '#bfdbfe',
        handleColor: getCSSVariable('--primary-500') || '#3b82f6',
        handleSize: 8,
        textStyle: {
            color: getCSSVariable('--gray-600') || '#4b5563'
        }
    },

    // 时间轴样式
    timeline: {
        lineStyle: {
            color: getCSSVariable('--gray-300') || '#d1d5db'
        },
        itemStyle: {
            color: getCSSVariable('--primary-500') || '#3b82f6'
        },
        controlStyle: {
            color: getCSSVariable('--primary-500') || '#3b82f6'
        },
        checkpointStyle: {
            color: getCSSVariable('--primary-600') || '#2563eb'
        },
        label: {
            color: getCSSVariable('--gray-600') || '#4b5563'
        }
    }
};

// 数据可视化颜色配置
const visualColors = {
    // 评分等级颜色映射
    scoreColors: {
        excellent: getCSSVariable('--success-500') || '#10b981',  // 优秀 (90-100)
        good: getCSSVariable('--info-500') || '#06b6d4',          // 良好 (80-89)
        average: getCSSVariable('--warning-500') || '#f59e0b',     // 一般 (70-79)
        poor: getCSSVariable('--error-500') || '#ef4444'          // 较差 (60-69)
    },

    // 部门颜色映射（可扩展）
    departmentColors: [
        getCSSVariable('--primary-500') || '#3b82f6',
        getCSSVariable('--success-500') || '#10b981',
        getCSSVariable('--warning-500') || '#f59e0b',
        getCSSVariable('--info-500') || '#06b6d4',
        getCSSVariable('--primary-400') || '#60a5fa',
        getCSSVariable('--success-400') || '#34d399'
    ],

    // 状态颜色映射
    statusColors: {
        completed: getCSSVariable('--success-500') || '#10b981',
        pending: getCSSVariable('--warning-500') || '#f59e0b',
        overdue: getCSSVariable('--error-500') || '#ef4444',
        draft: getCSSVariable('--gray-400') || '#9ca3af'
    }
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { corporateTheme, visualColors };
} else {
    window.corporateTheme = corporateTheme;
    window.visualColors = visualColors;
}

// 注册主题到 ECharts（如果 ECharts 已加载）
if (typeof echarts !== 'undefined') {
    echarts.registerTheme('corporate', corporateTheme);
}