/**
 * 性能优化工具库
 * 提供加载状态管理、数据缓存、防抖节流等功能
 */

class PerformanceOptimizer {
    constructor() {
        this.cache = new Map();
        this.loadingStates = new Map();
        this.init();
    }

    init() {
        this.setupGlobalLoadingIndicator();
        this.setupFormOptimizations();
        this.setupAjaxOptimizations();
    }

    /**
     * 设置全局加载指示器
     */
    setupGlobalLoadingIndicator() {
        // 创建全局加载遮罩
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'globalLoadingOverlay';
        loadingOverlay.className = 'global-loading-overlay hidden';
        loadingOverlay.innerHTML = `
            <div class="global-loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">加载中...</div>
            </div>
        `;
        document.body.appendChild(loadingOverlay);

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .global-loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.9);
                z-index: 9999;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .global-loading-overlay.hidden {
                display: none;
            }
            
            .global-loading-content {
                text-align: center;
                padding: 2rem;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            
            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f4f6;
                border-top: 4px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 1rem;
            }
            
            .loading-text {
                color: #6b7280;
                font-size: 14px;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            /* 按钮加载状态 */
            .btn-loading {
                position: relative;
                pointer-events: none;
            }
            
            .btn-loading::after {
                content: '';
                position: absolute;
                width: 16px;
                height: 16px;
                border: 2px solid transparent;
                border-top: 2px solid currentColor;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
            
            .btn-loading .btn-text {
                opacity: 0;
            }
            
            /* 表格加载状态 */
            .table-loading {
                position: relative;
            }
            
            .table-loading::after {
                content: '加载中...';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 显示全局加载状态
     */
    showGlobalLoading(text = '加载中...') {
        const overlay = document.getElementById('globalLoadingOverlay');
        const textElement = overlay.querySelector('.loading-text');
        if (textElement) {
            textElement.textContent = text;
        }
        overlay.classList.remove('hidden');
    }

    /**
     * 隐藏全局加载状态
     */
    hideGlobalLoading() {
        const overlay = document.getElementById('globalLoadingOverlay');
        overlay.classList.add('hidden');
    }

    /**
     * 显示按钮加载状态
     */
    showButtonLoading(buttonElement) {
        if (!buttonElement) return;
        
        buttonElement.classList.add('btn-loading');
        const textElement = buttonElement.querySelector('.btn-text') || buttonElement;
        if (!buttonElement.querySelector('.btn-text')) {
            const text = buttonElement.textContent;
            buttonElement.innerHTML = `<span class="btn-text">${text}</span>`;
        }
        buttonElement.disabled = true;
    }

    /**
     * 隐藏按钮加载状态
     */
    hideButtonLoading(buttonElement) {
        if (!buttonElement) return;
        
        buttonElement.classList.remove('btn-loading');
        buttonElement.disabled = false;
    }

    /**
     * 显示表格加载状态
     */
    showTableLoading(tableElement) {
        if (!tableElement) return;
        tableElement.classList.add('table-loading');
    }

    /**
     * 隐藏表格加载状态
     */
    hideTableLoading(tableElement) {
        if (!tableElement) return;
        tableElement.classList.remove('table-loading');
    }

    /**
     * 数据缓存管理
     */
    setCache(key, data, ttl = 5 * 60 * 1000) { // 默认5分钟过期
        const expireTime = Date.now() + ttl;
        this.cache.set(key, { data, expireTime });
    }

    getCache(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;
        
        if (Date.now() > cached.expireTime) {
            this.cache.delete(key);
            return null;
        }
        
        return cached.data;
    }

    clearCache(key = null) {
        if (key) {
            this.cache.delete(key);
        } else {
            this.cache.clear();
        }
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 优化Ajax请求
     */
    async optimizedFetch(url, options = {}) {
        const cacheKey = `${url}_${JSON.stringify(options)}`;
        
        // 检查缓存
        if (options.useCache !== false) {
            const cached = this.getCache(cacheKey);
            if (cached) {
                return cached;
            }
        }

        // 检查是否正在请求
        if (this.loadingStates.get(cacheKey)) {
            return this.loadingStates.get(cacheKey);
        }

        // 创建请求Promise
        const fetchPromise = fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
                ...options.headers
            },
            ...options
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // 缓存成功的响应
            if (options.useCache !== false && data.success) {
                this.setCache(cacheKey, data);
            }
            return data;
        })
        .finally(() => {
            this.loadingStates.delete(cacheKey);
        });

        // 存储正在进行的请求
        this.loadingStates.set(cacheKey, fetchPromise);
        
        return fetchPromise;
    }

    /**
     * 设置表单优化
     */
    setupFormOptimizations() {
        // 表单实时验证防抖
        document.addEventListener('input', this.debounce((e) => {
            if (e.target.matches('input[data-validate], textarea[data-validate]')) {
                this.validateField(e.target);
            }
        }, 300));

        // 表单提交优化
        document.addEventListener('submit', (e) => {
            if (e.target.matches('form[data-optimize]')) {
                this.optimizeFormSubmission(e);
            }
        });
    }

    /**
     * 字段验证
     */
    validateField(field) {
        const validationType = field.getAttribute('data-validate');
        let isValid = true;
        let message = '';

        switch (validationType) {
            case 'required':
                isValid = field.value.trim() !== '';
                message = isValid ? '' : '此字段不能为空';
                break;
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                isValid = emailRegex.test(field.value);
                message = isValid ? '' : '请输入有效的邮箱地址';
                break;
            case 'phone':
                const phoneRegex = /^1[3-9]\d{9}$/;
                isValid = phoneRegex.test(field.value);
                message = isValid ? '' : '请输入有效的手机号码';
                break;
        }

        this.showFieldValidation(field, isValid, message);
    }

    /**
     * 显示字段验证结果
     */
    showFieldValidation(field, isValid, message) {
        // 移除之前的验证样式
        field.classList.remove('border-red-500', 'border-green-500');
        
        // 移除之前的错误消息
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        if (!isValid && message) {
            field.classList.add('border-red-500');
            const errorElement = document.createElement('div');
            errorElement.className = 'field-error text-red-500 text-sm mt-1';
            errorElement.textContent = message;
            field.parentNode.appendChild(errorElement);
        } else if (field.value.trim() !== '') {
            field.classList.add('border-green-500');
        }
    }

    /**
     * 优化表单提交
     */
    async optimizeFormSubmission(e) {
        e.preventDefault();
        const form = e.target;
        const submitButton = form.querySelector('button[type="submit"]');
        
        // 显示加载状态
        this.showButtonLoading(submitButton);
        
        try {
            const formData = new FormData(form);
            const url = form.action || window.location.href;
            
            const response = await this.optimizedFetch(url, {
                method: 'POST',
                body: formData,
                useCache: false
            });

            if (response.success) {
                this.showNotification('操作成功', 'success');
                // 可以添加重定向逻辑
                if (response.redirect_url) {
                    setTimeout(() => {
                        window.location.href = response.redirect_url;
                    }, 1000);
                }
            } else {
                this.showNotification(response.message || '操作失败', 'error');
            }
        } catch (error) {
            console.error('Form submission error:', error);
            this.showNotification('网络错误，请稍后重试', 'error');
        } finally {
            this.hideButtonLoading(submitButton);
        }
    }

    /**
     * 设置Ajax优化
     */
    setupAjaxOptimizations() {
        // 拦截所有fetch请求，添加加载状态
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            const [url, options = {}] = args;
            
            // 如果是API请求，显示加载状态
            if (url.includes('/api/')) {
                this.showGlobalLoading();
            }
            
            try {
                const response = await originalFetch(...args);
                return response;
            } finally {
                if (url.includes('/api/')) {
                    this.hideGlobalLoading();
                }
            }
        };
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // 添加样式
        const style = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 24px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;
        
        const colors = {
            success: 'background-color: #10b981;',
            error: 'background-color: #ef4444;',
            warning: 'background-color: #f59e0b;',
            info: 'background-color: #3b82f6;'
        };
        
        notification.style.cssText = style + colors[type];
        
        document.body.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    /**
     * 图片懒加载
     */
    setupLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }
}

// 全局实例
window.performanceOptimizer = new PerformanceOptimizer();

// 页面加载完成后初始化懒加载
document.addEventListener('DOMContentLoaded', () => {
    window.performanceOptimizer.setupLazyLoading();
});

// 导出工具函数
window.showLoading = (text) => window.performanceOptimizer.showGlobalLoading(text);
window.hideLoading = () => window.performanceOptimizer.hideGlobalLoading();
window.showNotification = (message, type) => window.performanceOptimizer.showNotification(message, type);
window.optimizedFetch = (url, options) => window.performanceOptimizer.optimizedFetch(url, options);