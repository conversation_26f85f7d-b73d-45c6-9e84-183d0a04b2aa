/**
 * 增强型多选框交互工具
 * 提供更好的用户体验，包括扩展点击区域、键盘导航、批量选择等功能
 */

class EnhancedCheckbox {
    constructor(options = {}) {
        this.options = {
            // 是否启用扩展点击区域
            expandClickArea: true,
            // 是否启用键盘导航
            keyboardNavigation: true,
            // 是否启用批量选择
            batchSelection: true,
            // 是否启用行高亮
            rowHighlight: true,
            // 选择器配置
            selectors: {
                checkbox: '.checkbox, .checkbox-table',
                tableRow: 'tr',
                checkboxCell: 'td:first-child, th:first-child',
                selectAll: '#selectAll, [data-select-all]',
                batchActions: '[data-batch-actions]',
                selectionCounter: '[data-selection-counter]'
            },
            ...options
        };
        
        this.selectedItems = new Set();
        this.lastSelectedIndex = -1;
        this.init();
    }

    init() {
        this.setupExpandedClickAreas();
        this.setupKeyboardNavigation();
        this.setupBatchSelection();
        this.setupRowHighlight();
        this.setupSelectAllFunctionality();
        this.updateUI();
    }

    /**
     * 设置扩展点击区域
     */
    setupExpandedClickAreas() {
        if (!this.options.expandClickArea) return;

        const checkboxCells = document.querySelectorAll(this.options.selectors.checkboxCell);
        
        checkboxCells.forEach(cell => {
            // 添加样式类
            cell.classList.add('checkbox-cell');
            
            // 绑定点击事件
            cell.addEventListener('click', (e) => {
                // 如果直接点击的是checkbox，不处理（避免重复触发）
                if (e.target.matches(this.options.selectors.checkbox)) return;
                
                const checkbox = cell.querySelector(this.options.selectors.checkbox);
                if (checkbox && !checkbox.disabled) {
                    checkbox.checked = !checkbox.checked;
                    this.handleCheckboxChange(checkbox, e);
                }
            });
        });
    }

    /**
     * 设置键盘导航
     */
    setupKeyboardNavigation() {
        if (!this.options.keyboardNavigation) return;

        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + A 全选
            if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
                const checkboxes = document.querySelectorAll(this.options.selectors.checkbox);
                if (checkboxes.length > 0 && this.isInTableContext(e.target)) {
                    e.preventDefault();
                    this.selectAll();
                }
            }
            
            // Escape 取消所有选择
            if (e.key === 'Escape') {
                this.clearSelection();
            }
        });
    }

    /**
     * 设置批量选择（Shift点击）
     */
    setupBatchSelection() {
        if (!this.options.batchSelection) return;

        const checkboxes = document.querySelectorAll(this.options.selectors.checkbox);
        
        checkboxes.forEach((checkbox, index) => {
            checkbox.addEventListener('change', (e) => {
                this.handleCheckboxChange(checkbox, e, index);
            });
        });
    }

    /**
     * 处理多选框变化事件
     */
    handleCheckboxChange(checkbox, event, index = -1) {
        const isShiftClick = event.shiftKey && this.lastSelectedIndex !== -1 && index !== -1;
        
        if (isShiftClick) {
            // Shift点击批量选择
            this.handleShiftSelection(index);
        } else {
            // 单个选择
            this.handleSingleSelection(checkbox);
            this.lastSelectedIndex = index;
        }
        
        this.updateUI();
        this.triggerSelectionEvent();
    }

    /**
     * 处理Shift批量选择
     */
    handleShiftSelection(currentIndex) {
        const checkboxes = Array.from(document.querySelectorAll(this.options.selectors.checkbox));
        const start = Math.min(this.lastSelectedIndex, currentIndex);
        const end = Math.max(this.lastSelectedIndex, currentIndex);
        
        for (let i = start; i <= end; i++) {
            if (checkboxes[i] && !checkboxes[i].disabled) {
                checkboxes[i].checked = true;
                this.selectedItems.add(checkboxes[i].value || i.toString());
            }
        }
    }

    /**
     * 处理单个选择
     */
    handleSingleSelection(checkbox) {
        const value = checkbox.value || checkbox.dataset.value;
        
        if (checkbox.checked) {
            this.selectedItems.add(value);
        } else {
            this.selectedItems.delete(value);
        }
    }

    /**
     * 设置行高亮
     */
    setupRowHighlight() {
        if (!this.options.rowHighlight) return;

        const checkboxes = document.querySelectorAll(this.options.selectors.checkbox);
        
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                const row = checkbox.closest(this.options.selectors.tableRow);
                if (row) {
                    if (checkbox.checked) {
                        row.classList.add('table-row-selected');
                    } else {
                        row.classList.remove('table-row-selected');
                    }
                }
            });
        });
    }

    /**
     * 设置全选功能
     */
    setupSelectAllFunctionality() {
        const selectAllCheckbox = document.querySelector(this.options.selectors.selectAll);
        if (!selectAllCheckbox) return;

        selectAllCheckbox.addEventListener('change', (e) => {
            if (e.target.checked) {
                this.selectAll();
            } else {
                this.clearSelection();
            }
        });
    }

    /**
     * 全选
     */
    selectAll() {
        const checkboxes = document.querySelectorAll(this.options.selectors.checkbox);
        
        checkboxes.forEach(checkbox => {
            if (!checkbox.disabled) {
                checkbox.checked = true;
                const value = checkbox.value || checkbox.dataset.value;
                this.selectedItems.add(value);
                
                // 更新行高亮
                const row = checkbox.closest(this.options.selectors.tableRow);
                if (row) {
                    row.classList.add('table-row-selected');
                }
            }
        });
        
        this.updateUI();
        this.triggerSelectionEvent();
    }

    /**
     * 清除所有选择
     */
    clearSelection() {
        const checkboxes = document.querySelectorAll(this.options.selectors.checkbox);
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            
            // 移除行高亮
            const row = checkbox.closest(this.options.selectors.tableRow);
            if (row) {
                row.classList.remove('table-row-selected');
            }
        });
        
        this.selectedItems.clear();
        this.updateUI();
        this.triggerSelectionEvent();
    }

    /**
     * 更新UI状态
     */
    updateUI() {
        this.updateSelectAllState();
        this.updateSelectionCounter();
        this.updateBatchActions();
    }

    /**
     * 更新全选状态
     */
    updateSelectAllState() {
        const selectAllCheckbox = document.querySelector(this.options.selectors.selectAll);
        if (!selectAllCheckbox) return;

        const checkboxes = document.querySelectorAll(this.options.selectors.checkbox);
        const checkedCount = Array.from(checkboxes).filter(cb => cb.checked && !cb.disabled).length;
        const totalCount = Array.from(checkboxes).filter(cb => !cb.disabled).length;

        if (checkedCount === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (checkedCount === totalCount) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    /**
     * 更新选择计数器
     */
    updateSelectionCounter() {
        const counter = document.querySelector(this.options.selectors.selectionCounter);
        if (counter) {
            counter.textContent = this.selectedItems.size;
        }
    }

    /**
     * 更新批量操作按钮状态
     */
    updateBatchActions() {
        const batchActions = document.querySelector(this.options.selectors.batchActions);
        if (batchActions) {
            if (this.selectedItems.size > 0) {
                batchActions.classList.remove('hidden');
            } else {
                batchActions.classList.add('hidden');
            }
        }
    }

    /**
     * 触发选择变化事件
     */
    triggerSelectionEvent() {
        const event = new CustomEvent('selectionChanged', {
            detail: {
                selectedItems: Array.from(this.selectedItems),
                count: this.selectedItems.size
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 检查是否在表格上下文中
     */
    isInTableContext(element) {
        return element.closest('table') !== null;
    }

    /**
     * 获取选中的项目
     */
    getSelectedItems() {
        return Array.from(this.selectedItems);
    }

    /**
     * 获取选中数量
     */
    getSelectedCount() {
        return this.selectedItems.size;
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否存在多选框
    const checkboxes = document.querySelectorAll('.checkbox, .checkbox-table');
    if (checkboxes.length > 0) {
        window.enhancedCheckbox = new EnhancedCheckbox();
    }
});

// 导出类供其他脚本使用
window.EnhancedCheckbox = EnhancedCheckbox;
