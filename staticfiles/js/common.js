/**
 * 企业考评评分系统 - 通用JavaScript工具库
 */

// 全局配置
const CONFIG = {
    CSRF_TOKEN: null,
    API_BASE_URL: '',
    MESSAGES: {
        CONFIRM_DELETE: '确定要删除这个项目吗？此操作无法撤销。',
        SAVE_SUCCESS: '保存成功',
        SAVE_ERROR: '保存失败，请重试',
        NETWORK_ERROR: '网络连接错误，请检查网络连接',
        VALIDATION_ERROR: '表单验证失败，请检查输入内容',
        LOADING: '加载中...',
        PROCESSING: '处理中...'
    }
};

/**
 * 工具函数库
 */
const Utils = {
    /**
     * 获取CSRF Token
     */
    getCsrfToken() {
        if (CONFIG.CSRF_TOKEN) {
            return CONFIG.CSRF_TOKEN;
        }
        
        const token = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                     document.querySelector('meta[name=csrf-token]')?.content ||
                     this.getCookie('csrftoken');
        
        CONFIG.CSRF_TOKEN = token;
        return token;
    },

    /**
     * 获取Cookie值
     */
    getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    },

    /**
     * 设置Cookie
     */
    setCookie(name, value, days = 7) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
    },

    /**
     * 格式化日期
     */
    formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '';
        
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hour = String(d.getHours()).padStart(2, '0');
        const minute = String(d.getMinutes()).padStart(2, '0');
        const second = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hour)
            .replace('mm', minute)
            .replace('ss', second);
    },

    /**
     * 防抖函数
     */
    debounce(func, wait, immediate = false) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    },

    /**
     * 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * 深拷贝对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    /**
     * 生成唯一ID
     */
    generateId(prefix = 'id') {
        return prefix + '_' + Math.random().toString(36).substr(2, 9) + Date.now();
    },

    /**
     * 验证表单
     */
    validateForm(form) {
        const errors = [];
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                errors.push(`${field.name || field.id || 'Unknown field'} 是必填项`);
                field.classList.add('border-red-500');
            } else {
                field.classList.remove('border-red-500');
            }
        });

        // 验证邮箱格式
        const emailFields = form.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            if (field.value && !this.isValidEmail(field.value)) {
                errors.push('邮箱格式不正确');
                field.classList.add('border-red-500');
            }
        });

        // 验证手机号格式
        const phoneFields = form.querySelectorAll('input[type="tel"]');
        phoneFields.forEach(field => {
            if (field.value && !this.isValidPhone(field.value)) {
                errors.push('手机号格式不正确');
                field.classList.add('border-red-500');
            }
        });

        return errors;
    },

    /**
     * 验证邮箱格式
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    /**
     * 验证手机号格式
     */
    isValidPhone(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    },

    /**
     * 验证分数范围
     */
    isValidScore(score, min = 0, max = 100) {
        const numScore = parseFloat(score);
        return !isNaN(numScore) && numScore >= min && numScore <= max;
    }
};

/**
 * AJAX请求工具
 */
const Ajax = {
    /**
     * 发送GET请求
     */
    get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        return fetch(fullUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': Utils.getCsrfToken()
            },
            credentials: 'same-origin'
        }).then(this.handleResponse);
    },

    /**
     * 发送POST请求
     */
    post(url, data = {}) {
        return fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': Utils.getCsrfToken()
            },
            credentials: 'same-origin',
            body: JSON.stringify(data)
        }).then(this.handleResponse);
    },

    /**
     * 发送PUT请求
     */
    put(url, data = {}) {
        return fetch(url, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': Utils.getCsrfToken()
            },
            credentials: 'same-origin',
            body: JSON.stringify(data)
        }).then(this.handleResponse);
    },

    /**
     * 发送DELETE请求
     */
    delete(url) {
        return fetch(url, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': Utils.getCsrfToken()
            },
            credentials: 'same-origin'
        }).then(this.handleResponse);
    },

    /**
     * 处理响应
     */
    handleResponse(response) {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    },

    /**
     * 提交表单
     */
    submitForm(form, url = null) {
        const formData = new FormData(form);
        const submitUrl = url || form.action;

        return fetch(submitUrl, {
            method: form.method || 'POST',
            headers: {
                'X-CSRFToken': Utils.getCsrfToken()
            },
            credentials: 'same-origin',
            body: formData
        }).then(this.handleResponse);
    }
};

/**
 * UI工具
 */
const UI = {
    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info', duration = 5000) {
        const messageId = Utils.generateId('message');
        const messageEl = document.createElement('div');
        messageEl.id = messageId;
        messageEl.className = `alert alert-${type} fixed top-4 right-4 z-50 max-w-sm shadow-lg`;
        messageEl.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-lg leading-none">&times;</button>
            </div>
        `;

        document.body.appendChild(messageEl);

        // 自动移除
        setTimeout(() => {
            const el = document.getElementById(messageId);
            if (el) {
                el.remove();
            }
        }, duration);
    },

    /**
     * 显示确认对话框
     */
    confirm(message = CONFIG.MESSAGES.CONFIRM_DELETE) {
        return new Promise((resolve) => {
            const result = window.confirm(message);
            resolve(result);
        });
    },

    /**
     * 显示加载状态
     */
    showLoading(element, text = CONFIG.MESSAGES.LOADING) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.disabled = true;
            element.dataset.originalText = element.innerHTML;
            element.innerHTML = `<span class="spinner mr-2"></span>${text}`;
        }
    },

    /**
     * 隐藏加载状态
     */
    hideLoading(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element && element.dataset.originalText) {
            element.disabled = false;
            element.innerHTML = element.dataset.originalText;
            delete element.dataset.originalText;
        }
    },

    /**
     * 显示全屏加载
     */
    showFullLoading(text = CONFIG.MESSAGES.LOADING) {
        const loadingEl = document.createElement('div');
        loadingEl.id = 'full-loading';
        loadingEl.className = 'loading-overlay';
        loadingEl.innerHTML = `
            <div class="text-center">
                <div class="loading-spinner"></div>
                <p class="mt-4 text-gray-600">${text}</p>
            </div>
        `;
        document.body.appendChild(loadingEl);
    },

    /**
     * 隐藏全屏加载
     */
    hideFullLoading() {
        const loadingEl = document.getElementById('full-loading');
        if (loadingEl) {
            loadingEl.remove();
        }
    },

    /**
     * 显示模态框
     */
    showModal(content, options = {}) {
        const modalId = Utils.generateId('modal');
        const modal = document.createElement('div');
        modal.id = modalId;
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal">
                ${options.title ? `
                    <div class="modal-header">
                        <h3 class="text-lg font-medium">${options.title}</h3>
                    </div>
                ` : ''}
                <div class="modal-body">
                    ${content}
                </div>
                ${options.showFooter !== false ? `
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="UI.closeModal('${modalId}')">取消</button>
                        ${options.confirmButton ? `<button class="btn btn-primary" onclick="${options.confirmAction || ''}">${options.confirmButton}</button>` : ''}
                    </div>
                ` : ''}
            </div>
        `;

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal(modalId);
            }
        });

        document.body.appendChild(modal);
        return modalId;
    },

    /**
     * 关闭模态框
     */
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.remove();
        }
    }
};

/**
 * 表格工具
 */
const Table = {
    /**
     * 排序表格
     */
    sort(table, columnIndex, direction = 'asc') {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        rows.sort((a, b) => {
            const aVal = a.children[columnIndex].textContent.trim();
            const bVal = b.children[columnIndex].textContent.trim();
            
            if (direction === 'asc') {
                return aVal.localeCompare(bVal, 'zh-CN', { numeric: true });
            } else {
                return bVal.localeCompare(aVal, 'zh-CN', { numeric: true });
            }
        });
        
        rows.forEach(row => tbody.appendChild(row));
    },

    /**
     * 筛选表格
     */
    filter(table, searchTerm) {
        const tbody = table.querySelector('tbody');
        const rows = tbody.querySelectorAll('tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const match = text.includes(searchTerm.toLowerCase());
            row.style.display = match ? '' : 'none';
        });
    }
};

/**
 * 本地存储工具
 */
const Storage = {
    /**
     * 设置本地存储
     */
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (e) {
            console.error('存储失败:', e);
        }
    },

    /**
     * 获取本地存储
     */
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (e) {
            console.error('读取存储失败:', e);
            return defaultValue;
        }
    },

    /**
     * 删除本地存储
     */
    remove(key) {
        try {
            localStorage.removeItem(key);
        } catch (e) {
            console.error('删除存储失败:', e);
        }
    },

    /**
     * 清空本地存储
     */
    clear() {
        try {
            localStorage.clear();
        } catch (e) {
            console.error('清空存储失败:', e);
        }
    }
};

/**
 * 页面初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有确认删除按钮
    document.querySelectorAll('.confirm-delete').forEach(btn => {
        btn.addEventListener('click', async function(e) {
            e.preventDefault();
            const confirmed = await UI.confirm();
            if (confirmed) {
                const form = this.closest('form');
                if (form) {
                    form.submit();
                } else {
                    window.location.href = this.href;
                }
            }
        });
    });

    // 初始化表单验证
    document.querySelectorAll('form[data-validate]').forEach(form => {
        form.addEventListener('submit', function(e) {
            const errors = Utils.validateForm(this);
            if (errors.length > 0) {
                e.preventDefault();
                UI.showMessage(errors.join('<br>'), 'danger');
            }
        });
    });

    // 初始化可排序表格
    document.querySelectorAll('table[data-sortable]').forEach(table => {
        const headers = table.querySelectorAll('th[data-sort]');
        headers.forEach((header, index) => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                const currentDirection = this.dataset.direction || 'asc';
                const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
                this.dataset.direction = newDirection;
                Table.sort(table, index, newDirection);
            });
        });
    });

    // 自动隐藏消息提示
    setTimeout(() => {
        document.querySelectorAll('.alert').forEach(alert => {
            if (!alert.classList.contains('alert-persistent')) {
                alert.remove();
            }
        });
    }, 5000);
});

// 导出到全局作用域
window.Utils = Utils;
window.Ajax = Ajax;
window.UI = UI;
window.Table = Table;
window.Storage = Storage;
window.CONFIG = CONFIG;