/**
 * 统一删除管理器
 * 提供标准化的删除确认对话框和API调用
 */
class DeleteManager {
    constructor() {
        this.createModal();
        this.bindEvents();
    }

    /**
     * 创建删除确认模态框
     */
    createModal() {
        const modalHtml = `
            <div id="deleteConfirmModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                        <div class="px-6 py-4">
                            <div class="flex items-center mb-4">
                                <i data-lucide="alert-triangle" class="w-8 h-8 text-red-500 mr-3"></i>
                                <h3 class="text-lg font-medium text-gray-900">确认删除</h3>
                            </div>
                            <p class="text-gray-600 mb-6" id="deleteMessage">您确定要删除此项吗？</p>
                            <div class="flex justify-end space-x-3">
                                <button type="button" class="btn btn-secondary btn-md" id="cancelDelete">取消</button>
                                <button type="button" class="btn btn-danger btn-md" id="confirmDelete">
                                    <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                                    <span>删除</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 将模态框添加到页面
        if (!document.getElementById('deleteConfirmModal')) {
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 取消删除
        document.addEventListener('click', (e) => {
            if (e.target.id === 'cancelDelete' || e.target.closest('#cancelDelete')) {
                this.hideModal();
            }
        });

        // 点击遮罩层关闭
        document.addEventListener('click', (e) => {
            if (e.target.id === 'deleteConfirmModal') {
                this.hideModal();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !document.getElementById('deleteConfirmModal').classList.contains('hidden')) {
                this.hideModal();
            }
        });
    }

    /**
     * 显示删除确认对话框
     * @param {Object} options 删除选项
     * @param {string} options.title 删除标题
     * @param {string} options.message 删除消息
     * @param {string} options.url 删除API地址
     * @param {string} options.method 请求方法，默认DELETE
     * @param {Function} options.onSuccess 成功回调
     * @param {Function} options.onError 错误回调
     */
    confirmDelete(options) {
        const {
            title = '确认删除',
            message = '您确定要删除此项吗？此操作不可撤销。',
            url,
            method = 'DELETE',
            onSuccess,
            onError,
            data = {}
        } = options;

        // 设置消息
        document.getElementById('deleteMessage').textContent = message;
        
        // 显示模态框
        this.showModal();

        // 绑定确认删除事件
        const confirmBtn = document.getElementById('confirmDelete');
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        newConfirmBtn.addEventListener('click', async () => {
            await this.executeDelete({
                url,
                method,
                data,
                onSuccess,
                onError
            });
        });
    }

    /**
     * 执行删除操作
     */
    async executeDelete({ url, method, data, onSuccess, onError }) {
        const confirmBtn = document.getElementById('confirmDelete');
        const confirmSpan = confirmBtn.querySelector('span');
        const originalText = confirmSpan.textContent;

        try {
            // 显示加载状态
            confirmBtn.disabled = true;
            confirmSpan.textContent = '删除中...';

            // 准备请求参数
            const requestOptions = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                }
            };

            // 添加认证头
            const token = localStorage.getItem('access_token');
            if (token) {
                requestOptions.headers['Authorization'] = `Bearer ${token}`;
            }

            // 添加请求体
            if (method !== 'GET' && Object.keys(data).length > 0) {
                requestOptions.body = JSON.stringify(data);
            }

            // 发送请求
            const response = await fetch(url, requestOptions);
            const result = await response.json();

            if (response.ok && result.success !== false) {
                // 删除成功
                this.hideModal();
                this.showToast('删除成功', 'success');
                
                if (onSuccess) {
                    onSuccess(result);
                }
            } else {
                throw new Error(result.error || result.message || '删除失败');
            }

        } catch (error) {
            console.error('删除失败:', error);
            this.showToast('删除失败: ' + error.message, 'error');
            
            if (onError) {
                onError(error);
            }
        } finally {
            // 恢复按钮状态
            confirmBtn.disabled = false;
            confirmSpan.textContent = originalText;
        }
    }

    /**
     * 批量删除
     * @param {Object} options 批量删除选项
     */
    confirmBatchDelete(options) {
        const {
            title = '确认批量删除',
            message,
            items = [],
            url,
            onSuccess,
            onError
        } = options;

        const itemCount = items.length;
        const finalMessage = message || `您确定要删除选中的 ${itemCount} 项吗？此操作不可撤销。`;

        this.confirmDelete({
            title,
            message: finalMessage,
            url,
            method: 'POST',
            data: { items },
            onSuccess,
            onError
        });
    }

    /**
     * 显示模态框
     */
    showModal() {
        const modal = document.getElementById('deleteConfirmModal');
        modal.classList.remove('hidden');
        
        // 重新初始化图标
        if (window.lucide) {
            lucide.createIcons({ nameAttr: 'data-lucide' });
        }
    }

    /**
     * 隐藏模态框
     */
    hideModal() {
        document.getElementById('deleteConfirmModal').classList.add('hidden');
    }

    /**
     * 获取CSRF令牌
     */
    getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return '';
    }

    /**
     * 显示提示消息
     */
    showToast(message, type = 'info') {
        // 创建提示元素
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 
            'bg-blue-500'
        } text-white`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// 创建全局删除管理器实例
window.deleteManager = new DeleteManager();

// 导出便捷方法
window.confirmDelete = (options) => window.deleteManager.confirmDelete(options);
window.confirmBatchDelete = (options) => window.deleteManager.confirmBatchDelete(options);