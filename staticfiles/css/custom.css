/* 企业考评评分系统 - 自定义样式 */

/* 基础变量 */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --light-bg: #f8fafc;
    --dark-bg: #1e293b;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --transition: all 0.15s ease-in-out;
    --transition-fast: all 0.1s ease-in-out;
    --sidebar-width: 16rem;
    --sidebar-collapsed-width: 4rem;
}

/* 全局布局修复 */
html, body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

* {
    box-sizing: border-box;
}

/* 确保主内容区域正确布局 */
#mainContent {
    background-color: #f8fafc;
    padding: 0;
    margin: 0;
    position: relative;
    overflow: hidden;
}

#mainContent > div:first-child {
    margin: 0;
    padding: 0;
    width: 100%;
}

/* 确保头部区域完全填满 */
.main-header {
    margin: 0 !important;
    padding: 2rem 1.5rem !important;
    width: 100% !important;
    min-height: 100px !important;
    box-sizing: border-box !important;
    background-color: #ffffff !important;
    border-bottom: 1px solid #e5e7eb !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex !important;
    align-items: center !important;
}

.main-content {
    margin: 0 !important;
    padding: 2rem 1.5rem 1.5rem 1.5rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
    flex: 1;
    overflow-y: auto;
    background-color: #f8fafc !important;
}

/* 确保头部内部布局稳定 */
.main-header .flex {
    gap: 1rem;
}

.main-header h1 {
    margin: 0;
    line-height: 1.25;
    color: #1f2937 !important;
}

.main-header p {
    margin: 0.5rem 0 0 0;
    line-height: 1.25;
    color: #6b7280 !important;
}

/* 增强头部组件视觉效果 */
.main-header {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* 确保头部内容可见性 */
.main-header > * {
    position: relative;
    z-index: 1;
}

/* 侧边栏样式 */
.sidebar-expanded {
    width: var(--sidebar-width);
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 50;
}

.sidebar-collapsed {
    width: var(--sidebar-collapsed-width);
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 50;
}

.sidebar-transition {
    transition: width 0.2s ease-in-out;
}

.content-expanded {
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
    min-height: 100vh;
    box-sizing: border-box;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    padding: 0;
    background-color: #f8fafc;
    overflow-x: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.content-collapsed {
    margin-left: var(--sidebar-collapsed-width);
    width: calc(100% - var(--sidebar-collapsed-width));
    min-height: 100vh;
    box-sizing: border-box;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    padding: 0;
    background-color: #f8fafc;
    overflow-x: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.content-transition {
    transition: margin-left 0.2s ease-in-out, width 0.2s ease-in-out;
}

/* 快速导航链接样式 - 优化点击响应 */
.nav-link {
    transition: background-color 0.08s ease-out, color 0.08s ease-out, transform 0.08s ease-out;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent; /* 移除移动端点击高亮 */
    backface-visibility: hidden; /* 优化硬件加速 */
    will-change: transform; /* 提示浏览器优化 */
}

.nav-link:hover {
    transform: translateX(2px);
}

.nav-link:active {
    transform: translateX(1px);
    transition: transform 0.05s ease-out;
}

/* 防止过度动画 */
.nav-link:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 减少重绘 */
.nav-link * {
    pointer-events: none;
}

/* 通用工具类 */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-hover {
    transition: var(--transition);
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 0.875rem;
    line-height: 1.25rem;
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
    border: none;
    outline: none;
}

.btn:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px var(--primary-color);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #475569;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: #059669;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background-color: #d97706;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.btn-outline:hover {
    background-color: var(--light-bg);
    border-color: var(--primary-color);
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* 表单控件 */
.form-input {
    display: block;
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: white;
    transition: var(--transition);
    font-size: 0.875rem;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:invalid {
    border-color: var(--danger-color);
}

.form-input:invalid:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-label {
    display: block;
    font-weight: 500;
    font-size: 0.875rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-help {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

.form-error {
    font-size: 0.75rem;
    color: var(--danger-color);
    margin-top: 0.25rem;
}

/* 卡片组件 */
.card {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.card-header {
    padding: 1.5rem 1.5rem 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 0 1.5rem 1.5rem;
    border-top: 1px solid var(--border-color);
    margin-top: 1.5rem;
}

/* 状态徽章 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-primary {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.badge-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.badge-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.badge-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.badge-secondary {
    background-color: rgba(100, 116, 139, 0.1);
    color: var(--secondary-color);
}

/* 进度条 */
.progress {
    width: 100%;
    height: 0.5rem;
    background-color: var(--light-bg);
    border-radius: 9999px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease-in-out;
    border-radius: 9999px;
}

.progress-bar-success {
    background-color: var(--success-color);
}

.progress-bar-warning {
    background-color: var(--warning-color);
}

.progress-bar-danger {
    background-color: var(--danger-color);
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background-color: var(--light-bg);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.table tbody tr:hover {
    background-color: var(--light-bg);
}

.table-striped tbody tr:nth-child(even) {
    background-color: rgba(248, 250, 252, 0.5);
}

/* 导航样式 */
.nav-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-item.active {
    background-color: var(--primary-color);
    color: white;
}

.nav-item svg {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 32rem;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem 1.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 0 1.5rem 1.5rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* 提示信息 */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid transparent;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
    color: #065f46;
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
    color: #92400e;
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: #991b1b;
}

.alert-info {
    background-color: rgba(6, 182, 212, 0.1);
    border-color: rgba(6, 182, 212, 0.2);
    color: #0c4a6e;
}

/* 加载动画 */
.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: currentColor;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 4px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table {
        font-size: 0.875rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
    }
    
    .modal {
        width: 95%;
        margin: 1rem;
    }
}

/* 打印样式 */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .btn {
        display: none;
    }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000;
        --text-secondary: #000;
        --text-muted: #000;
    }
    
    .card {
        border: 2px solid #000;
    }
    
    .form-input {
        border: 2px solid #000;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --light-bg: #0f172a;
        --text-primary: #f1f5f9;
        --text-secondary: #cbd5e1;
        --text-muted: #64748b;
        --border-color: #334155;
    }
    
    .card {
        background-color: #1e293b;
        border-color: var(--border-color);
    }
    
    .form-input {
        background-color: #1e293b;
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .table th {
        background-color: #334155;
        color: var(--text-primary);
    }
}