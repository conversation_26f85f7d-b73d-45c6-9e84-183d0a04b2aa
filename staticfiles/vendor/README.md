# 本地第三方库资源

## 已下载的CSS和JS库

### CSS 文件
- `tailwind-3.4.17.min.css` - Tailwind CSS 最新版 (3.4.17)
- `animate.min.css` - Animate.css 动画库 (4.1.1)
- `nprogress.min.css` - NProgress 进度条样式 (0.2.0)

### JavaScript 文件
- `lucide.min.js` - Lucide Icons 图标库 (最新版)
- `echarts.min.js` - ECharts 图表库 (5.5.1)
- `sortable.min.js` - Sortable.js 拖拽排序 (1.15.0)
- `alpine.min.js` - Alpine.js 轻量级框架 (3.14.1)
- `axios.min.js` - Axios HTTP客户端 (1.7.7)
- `chartjs.min.js` - Chart.js 图表库 (4.4.7)
- `dayjs.min.js` - Day.js 日期处理 (1.11.13)
- `nprogress.min.js` - NProgress 进度条 (0.2.0)

## 使用方法

### 在HTML模板中引用

```html
<!-- CSS -->
<link rel="stylesheet" href="{% static 'vendor/css/tailwind-3.4.17.min.css' %}">
<link rel="stylesheet" href="{% static 'vendor/css/animate.min.css' %}">
<link rel="stylesheet" href="{% static 'vendor/css/nprogress.min.css' %}">

<!-- JavaScript -->
<script src="{% static 'vendor/js/lucide.min.js' %}"></script>
<script src="{% static 'vendor/js/echarts.min.js' %}"></script>
<script src="{% static 'vendor/js/sortable.min.js' %}"></script>
<script src="{% static 'vendor/js/alpine.min.js' %}"></script>
<script src="{% static 'vendor/js/axios.min.js' %}"></script>
<script src="{% static 'vendor/js/chartjs.min.js' %}"></script>
<script src="{% static 'vendor/js/dayjs.min.js' %}"></script>
<script src="{% static 'vendor/js/nprogress.min.js' %}"></script>
```

### 在Django settings.py中配置

```python
# 确保在settings.py中有静态文件配置
STATICFILES_DIRS = [
    BASE_DIR / "static",
]

# 生产环境收集静态文件
# python manage.py collectstatic
```

## 文件大小统计
- 总CSS大小: ~480KB
- 总JS大小: ~1.7MB
- 总计: ~2.2MB

## 许可证
所有库都遵循各自的MIT或Apache许可证，可在各自官网查看详细条款。