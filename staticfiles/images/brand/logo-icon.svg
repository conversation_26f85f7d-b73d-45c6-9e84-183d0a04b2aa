<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="16" cy="16" r="14" fill="url(#iconGradient)" stroke="white" stroke-width="2"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 简化的勾选图标 -->
  <g transform="translate(16, 16)">
    <path d="M-4 -0.5 L-1 2.5 L4 -3.5" 
          stroke="white" 
          stroke-width="2" 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          fill="none"/>
  </g>
</svg>