#!/usr/bin/env python
"""
测试通信模块修复是否成功
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from django.urls import reverse, NoReverseMatch
from django.test import Client
from django.contrib.auth import get_user_model
from organizations.models import Staff, Department, Position

def test_url_resolution():
    """测试URL解析是否正常"""
    print("🔍 测试URL解析...")
    
    try:
        # 测试消息中心URL
        message_center_url = reverse('communications:admin:message_center')
        print(f"✅ 消息中心URL解析成功: {message_center_url}")
        
        # 测试公告管理URL
        announcement_list_url = reverse('communications:admin:announcement_list')
        print(f"✅ 公告管理URL解析成功: {announcement_list_url}")
        
        # 测试首页URL
        dashboard_url = reverse('organizations:admin:dashboard')
        print(f"✅ 首页URL解析成功: {dashboard_url}")
        
        return True
        
    except NoReverseMatch as e:
        print(f"❌ URL解析失败: {e}")
        return False

def test_view_access():
    """测试视图访问是否正常"""
    print("\n🌐 测试视图访问...")
    
    client = Client()
    
    try:
        # 创建测试用户和数据（如果不存在）
        department, created = Department.objects.get_or_create(
            name='测试部门',
            defaults={
                'code': 'TEST',
                'description': '测试部门',
                'sort_order': 1,
                'is_active': True
            }
        )
        
        position, created = Position.objects.get_or_create(
            name='测试职位',
            defaults={
                'code': 'TEST',
                'level': 5,
                'description': '测试职位',
                'sort_order': 1
            }
        )
        
        # 创建测试员工
        staff, created = Staff.objects.get_or_create(
            employee_no='TEST001',
            defaults={
                'name': '测试管理员',
                'email': '<EMAIL>',
                'department': department,
                'position': position,
                'is_manager': True,
                'is_super_admin': True,
                'is_active': True
            }
        )
        
        if created:
            staff.set_password('test123')
            staff.save()
        
        # 模拟登录
        client.login(username='TEST001', password='test123')
        
        # 测试消息中心访问
        response = client.get('/communications/admin/messages/')
        print(f"📨 消息中心访问状态: {response.status_code}")
        if response.status_code == 200:
            print("✅ 消息中心页面加载成功")
        else:
            print(f"❌ 消息中心页面加载失败: {response.status_code}")
            if hasattr(response, 'content'):
                print(f"错误内容: {response.content.decode()[:200]}...")
        
        # 测试公告管理访问
        response = client.get('/communications/admin/announcements/')
        print(f"📢 公告管理访问状态: {response.status_code}")
        if response.status_code == 200:
            print("✅ 公告管理页面加载成功")
        else:
            print(f"❌ 公告管理页面加载失败: {response.status_code}")
            if hasattr(response, 'content'):
                print(f"错误内容: {response.content.decode()[:200]}...")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 视图访问测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_fields():
    """测试模型字段是否正确"""
    print("\n🏗️ 测试模型字段...")
    
    try:
        from communications.models import Message, Announcement
        
        # 测试Message模型的priority字段
        message_fields = [field.name for field in Message._meta.fields]
        if 'priority' in message_fields:
            print("✅ Message模型包含priority字段")
        else:
            print("❌ Message模型缺少priority字段")
        
        # 测试Announcement模型的字段
        announcement_fields = [field.name for field in Announcement._meta.fields]
        print(f"📋 Announcement模型字段: {announcement_fields}")
        
        if 'priority' in announcement_fields:
            print("⚠️ Announcement模型包含priority字段（不应该有）")
        else:
            print("✅ Announcement模型不包含priority字段（正确）")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型字段测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试通信模块修复结果...\n")
    
    # 测试URL解析
    url_test = test_url_resolution()
    
    # 测试模型字段
    model_test = test_model_fields()
    
    # 测试视图访问
    view_test = test_view_access()
    
    print("\n📊 测试结果总结:")
    print(f"URL解析: {'✅ 通过' if url_test else '❌ 失败'}")
    print(f"模型字段: {'✅ 通过' if model_test else '❌ 失败'}")
    print(f"视图访问: {'✅ 通过' if view_test else '❌ 失败'}")
    
    if url_test and model_test and view_test:
        print("\n🎉 所有测试通过！通信模块修复成功！")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试。")
        return False

if __name__ == '__main__':
    main()