#!/usr/bin/env python
"""
验证灵活化考评系统功能是否正常
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
sys.path.append('/mnt/d/code/newmachinecode/UniversalStaffEvaluation3')

django.setup()

def test_models():
    """测试模型是否正常工作"""
    print("🔍 测试模型导入和基本功能...")
    
    try:
        from evaluations.models import EvaluationBatch, EvaluationTemplate, BatchTemplate
        print("✅ 模型导入成功")
        
        # 测试 EvaluationBatch 的新字段
        batch_fields = [f.name for f in EvaluationBatch._meta.get_fields()]
        if 'is_active' in batch_fields:
            print("✅ EvaluationBatch.is_active 字段存在")
        else:
            print("❌ EvaluationBatch.is_active 字段不存在")
            
        if 'templates' in batch_fields:
            print("✅ EvaluationBatch.templates 关系存在")
        else:
            print("❌ EvaluationBatch.templates 关系不存在")
        
        # 测试 BatchTemplate 模型
        bt_fields = [f.name for f in BatchTemplate._meta.get_fields()]
        required_fields = ['batch', 'template', 'relation_type', 'is_preferred', 'sort_order']
        missing_fields = [f for f in required_fields if f not in bt_fields]
        if not missing_fields:
            print("✅ BatchTemplate 模型字段完整")
        else:
            print(f"❌ BatchTemplate 缺少字段: {missing_fields}")
        
        return True
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

def test_batch_methods():
    """测试批次模型的新方法"""
    print("\n🔍 测试批次模型方法...")
    
    try:
        from evaluations.models import EvaluationBatch
        
        # 检查方法是否存在
        batch = EvaluationBatch()
        methods = ['get_available_templates', 'get_preferred_template', 'is_batch_active']
        
        for method in methods:
            if hasattr(batch, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法不存在")
        
        return True
    except Exception as e:
        print(f"❌ 方法测试失败: {e}")
        return False

def test_database_structure():
    """测试数据库表结构"""
    print("\n🔍 测试数据库表结构...")
    
    try:
        from django.db import connection
        
        with connection.cursor() as cursor:
            # 检查 BatchTemplate 表是否存在（MySQL专用）
            try:
                cursor.execute("SHOW TABLES LIKE 'evaluations_batchtemplate'")
                result = cursor.fetchone()
                if result:
                    print("✅ evaluations_batchtemplate 表存在")
                else:
                    print("❌ evaluations_batchtemplate 表不存在")
                
                # 检查 EvaluationBatch 表的 is_active 字段
                cursor.execute("SHOW COLUMNS FROM evaluations_evaluationbatch LIKE 'is_active'")
                result = cursor.fetchone()
                if result:
                    print("✅ evaluations_evaluationbatch.is_active 字段存在")
                else:
                    print("❌ evaluations_evaluationbatch.is_active 字段不存在")
                    
            except Exception as db_error:
                print(f"⚠️  数据库查询失败，但这不影响功能: {db_error}")
                # 尝试通过Django ORM检查模型是否可用
                from evaluations.models import BatchTemplate
                BatchTemplate.objects.count()  # 如果表不存在，这里会报错
                print("✅ 通过Django ORM验证，数据库表结构正常")
        
        return True
    except Exception as e:
        print(f"❌ 数据库结构测试失败: {e}")
        return False

def test_views():
    """测试视图是否正常导入"""
    print("\n🔍 测试视图导入...")
    
    try:
        from evaluations.views_backup import BatchListView, TemplateListView
        print("✅ 视图类导入成功")
        
        # 检查视图方法
        batch_view = BatchListView()
        if hasattr(batch_view, 'get_context_data'):
            print("✅ BatchListView.get_context_data 方法存在")
        
        template_view = TemplateListView()
        if hasattr(template_view, 'get_context_data'):
            print("✅ TemplateListView.get_context_data 方法存在")
        
        return True
    except Exception as e:
        print(f"❌ 视图测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始验证灵活化考评系统功能...")
    print("=" * 50)
    
    tests = [
        test_models,
        test_batch_methods,
        test_database_structure,
        test_views
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有功能验证通过！灵活化考评系统已就绪！")
        return True
    else:
        print("⚠️  部分功能存在问题，需要进一步检查")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)