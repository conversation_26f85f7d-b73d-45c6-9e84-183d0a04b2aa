#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
员工单个删除功能修复测试
测试修复后的员工删除功能是否正常工作
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from organizations.models import Department, Position, Staff
from common.models import AuditLog
from common.security.permissions import Permission

class StaffDeleteFixTest:
    def __init__(self):
        self.client = Client()
        self.setup_test_data()
    
    def setup_test_data(self):
        """设置测试数据"""
        print("🔧 设置测试数据...")
        
        # 创建测试部门
        self.test_dept = Department.objects.get_or_create(
            name="测试部门",
            defaults={
                'dept_code': 'TEST',
                'description': '用于删除功能测试的部门'
            }
        )[0]
        
        # 创建测试职位
        self.test_position = Position.objects.get_or_create(
            name="测试职位",
            defaults={
                'level': 3,
                'department': self.test_dept,
                'description': '用于删除功能测试的职位'
            }
        )[0]
        
        # 创建测试员工
        self.test_staff = Staff.objects.get_or_create(
            employee_no="TEST001",
            defaults={
                'name': '测试员工',
                'department': self.test_dept,
                'position': self.test_position,
                'email': '<EMAIL>',
                'is_active': True
            }
        )[0]
        
        # 创建管理员用户
        self.admin_staff = Staff.objects.get_or_create(
            employee_no="ADMIN001", 
            defaults={
                'name': '管理员',
                'department': self.test_dept,
                'is_admin': True,
                'is_active': True
            }
        )[0]
        
        print(f"✅ 测试数据创建完成:")
        print(f"   - 测试部门: {self.test_dept.name}")
        print(f"   - 测试职位: {self.test_position.name}")
        print(f"   - 测试员工: {self.test_staff.name} (ID: {self.test_staff.id})")
        print(f"   - 管理员: {self.admin_staff.name}")
    
    def test_staff_delete_url_generation(self):
        """测试员工删除URL是否正确生成"""
        print("\n📋 测试1: 员工删除URL生成")
        
        try:
            # 测试URL反向解析
            delete_url = reverse('organizations:admin:staff_delete', kwargs={'pk': self.test_staff.id})
            expected_url = f'/organizations/admin/staff/{self.test_staff.id}/delete/'
            
            print(f"   生成的URL: {delete_url}")
            print(f"   期望的URL: {expected_url}")
            
            if delete_url == expected_url:
                print("   ✅ URL生成正确")
                return True
            else:
                print("   ❌ URL生成错误")
                return False
                
        except Exception as e:
            print(f"   ❌ URL解析失败: {e}")
            return False
    
    def test_staff_delete_view_exists(self):
        """测试员工删除视图是否存在"""
        print("\n📋 测试2: 员工删除视图存在性")
        
        try:
            from organizations.views import StaffDeleteView
            print("   ✅ StaffDeleteView类存在")
            
            # 检查视图方法
            if hasattr(StaffDeleteView, 'post'):
                print("   ✅ post方法存在")
                return True
            else:
                print("   ❌ post方法不存在")
                return False
                
        except ImportError as e:
            print(f"   ❌ 无法导入StaffDeleteView: {e}")
            return False
    
    def test_staff_delete_permission(self):
        """测试员工删除权限检查"""
        print("\n📋 测试3: 员工删除权限")
        
        try:
            # 检查权限常量是否存在
            if hasattr(Permission, 'ORG_DELETE_STAFF'):
                print(f"   ✅ 删除权限常量存在: {Permission.ORG_DELETE_STAFF}")
                return True
            else:
                print("   ❌ 删除权限常量不存在")
                return False
                
        except Exception as e:
            print(f"   ❌ 权限检查失败: {e}")
            return False
    
    def test_staff_delete_api_call(self):
        """测试员工删除API调用"""
        print("\n📋 测试4: 员工删除API功能")
        
        try:
            # 获取删除URL
            delete_url = reverse('organizations:admin:staff_delete', kwargs={'pk': self.test_staff.id})
            print(f"   删除URL: {delete_url}")
            
            # 模拟POST请求（注意：这里只是测试URL可达性，不实际执行删除）
            # 因为没有完整的用户认证设置，我们只测试URL是否可访问
            response = self.client.post(delete_url, content_type='application/json')
            
            # 检查响应（可能是权限错误或其他错误，但不应该是404）
            if response.status_code != 404:
                print(f"   ✅ URL可访问，状态码: {response.status_code}")
                if response.status_code == 403:
                    print("   ℹ️  返回403是预期的（权限验证）")
                elif response.status_code == 400:
                    print("   ℹ️  返回400可能是CSRF或认证问题")
                return True
            else:
                print(f"   ❌ URL不存在，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ API调用测试失败: {e}")
            return False
    
    def test_staff_list_template_fix(self):
        """测试员工列表模板修复"""
        print("\n📋 测试5: 员工列表模板修复")
        
        try:
            template_path = '/mnt/d/code/newmachinecode/UniversalStaffEvaluation3/templates/admin/staff/list.html'
            
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键修复点
            checks = [
                ('URL模板使用', "{% url \"organizations:admin:staff_delete\" 0 %}" in content),
                ('confirmDelete函数存在', "function confirmDelete(staffId, staffName)" in content),
                ('window.confirmDelete调用', "window.confirmDelete({" in content),
                ('showToast函数存在', "function showToast(" in content),
                ('成功回调处理', "showToast('员工删除成功', 'success')" in content)
            ]
            
            all_passed = True
            for check_name, result in checks:
                if result:
                    print(f"   ✅ {check_name}")
                else:
                    print(f"   ❌ {check_name}")
                    all_passed = False
            
            return all_passed
                
        except Exception as e:
            print(f"   ❌ 模板检查失败: {e}")
            return False
    
    def test_delete_manager_js_loaded(self):
        """测试删除管理器JS是否已加载"""
        print("\n📋 测试6: 删除管理器JS加载")
        
        try:
            base_template_path = '/mnt/d/code/newmachinecode/UniversalStaffEvaluation3/templates/admin/base_admin.html'
            delete_manager_path = '/mnt/d/code/newmachinecode/UniversalStaffEvaluation3/static/js/delete-manager.js'
            
            # 检查base模板是否包含delete-manager.js
            with open(base_template_path, 'r', encoding='utf-8') as f:
                base_content = f.read()
            
            if "delete-manager.js" in base_content:
                print("   ✅ base_admin.html包含delete-manager.js")
            else:
                print("   ❌ base_admin.html未包含delete-manager.js")
                return False
            
            # 检查delete-manager.js文件是否存在
            if os.path.exists(delete_manager_path):
                print("   ✅ delete-manager.js文件存在")
                
                # 检查关键函数
                with open(delete_manager_path, 'r', encoding='utf-8') as f:
                    js_content = f.read()
                
                if "window.confirmDelete" in js_content:
                    print("   ✅ window.confirmDelete函数定义存在")
                    return True
                else:
                    print("   ❌ window.confirmDelete函数定义不存在")
                    return False
            else:
                print("   ❌ delete-manager.js文件不存在")
                return False
                
        except Exception as e:
            print(f"   ❌ JS文件检查失败: {e}")
            return False
    
    def cleanup_test_data(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        try:
            # 删除测试员工（软删除）
            if hasattr(self.test_staff, 'id'):
                self.test_staff.soft_delete(deleted_by='test_cleanup')
                print(f"   ✅ 测试员工已软删除: {self.test_staff.name}")
        except Exception as e:
            print(f"   ⚠️  清理警告: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始员工删除功能修复测试\n")
        
        tests = [
            self.test_staff_delete_url_generation,
            self.test_staff_delete_view_exists,
            self.test_staff_delete_permission,
            self.test_staff_delete_api_call,
            self.test_staff_list_template_fix,
            self.test_delete_manager_js_loaded
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        print(f"\n📊 测试结果统计:")
        print(f"   ✅ 通过: {passed}/{total}")
        print(f"   ❌ 失败: {total - passed}/{total}")
        print(f"   📈 成功率: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 所有测试通过！员工删除功能修复成功！")
        else:
            print(f"\n⚠️  还有 {total - passed} 个问题需要解决")
        
        # 清理测试数据
        self.cleanup_test_data()
        
        return passed == total

if __name__ == '__main__':
    tester = StaffDeleteFixTest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)