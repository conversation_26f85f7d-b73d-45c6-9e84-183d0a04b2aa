#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试匿名编号批量删除功能
诊断为什么批量删除没有反应
"""

import os
import sys
import django
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from django.test import Client, RequestFactory
from django.contrib.auth.models import AnonymousUser
from django.urls import reverse
from organizations.models import Staff
from organizations.views_anonymous import AnonymousCodeBatchDeleteView

def test_staff_data():
    """测试员工数据"""
    print("=" * 60)
    print("测试员工数据")
    print("=" * 60)
    
    try:
        # 检查员工总数
        total_staff = Staff.objects.filter(deleted_at__isnull=True).count()
        print(f"✅ 活跃员工总数: {total_staff}")
        
        # 检查有新匿名编号的员工
        staff_with_new_code = Staff.objects.filter(
            deleted_at__isnull=True,
            new_anonymous_code__isnull=False
        )
        print(f"✅ 有新匿名编号的员工: {staff_with_new_code.count()}")
        
        # 显示前几个有新匿名编号的员工
        for staff in staff_with_new_code[:5]:
            print(f"   - {staff.name} ({staff.employee_no}): {staff.new_anonymous_code}")
        
        # 检查没有新匿名编号的员工
        staff_without_new_code = Staff.objects.filter(
            deleted_at__isnull=True,
            new_anonymous_code__isnull=True
        ).count()
        print(f"✅ 没有新匿名编号的员工: {staff_without_new_code}")
        
        return staff_with_new_code.count() > 0
        
    except Exception as e:
        print(f"❌ 员工数据检查失败: {e}")
        return False

def test_url_routing():
    """测试URL路由"""
    print("\n" + "=" * 60)
    print("测试URL路由")
    print("=" * 60)
    
    try:
        # 测试URL反向解析
        url = reverse('organizations:admin:anonymous_code_batch_delete')
        print(f"✅ 批量删除URL: {url}")
        
        # 测试客户端访问
        client = Client()
        
        # 测试GET请求（应该不被允许）
        response = client.get(url)
        print(f"✅ GET请求响应: {response.status_code} (应该是405或重定向)")
        
        # 测试POST请求（无认证，应该重定向或403）
        test_data = {
            'staff_ids': [1, 2],
            'reason': '测试',
            'force_delete': False
        }
        response = client.post(
            url,
            data=json.dumps(test_data),
            content_type='application/json'
        )
        print(f"✅ POST请求响应: {response.status_code} (无认证)")
        
        return True
        
    except Exception as e:
        print(f"❌ URL路由测试失败: {e}")
        return False

def diagnose_issue():
    """诊断问题"""
    print("\n" + "=" * 60)
    print("问题诊断")
    print("=" * 60)
    
    # 可能的问题原因
    issues = []
    
    # 1. 检查是否有可删除的数据
    staff_with_codes = Staff.objects.filter(
        deleted_at__isnull=True,
        new_anonymous_code__isnull=False
    ).count()
    
    if staff_with_codes == 0:
        issues.append("没有员工有新匿名编号可以删除")
    else:
        print(f"✅ 有 {staff_with_codes} 个员工有新匿名编号可以删除")
    
    # 2. 检查权限设置
    try:
        from organizations.models import Permission
        perm = Permission.objects.filter(code='SYS_MANAGE_SETTINGS').first()
        if not perm:
            issues.append("缺少SYS_MANAGE_SETTINGS权限定义")
        else:
            print(f"✅ 权限定义存在: {perm.name}")
    except Exception as e:
        issues.append(f"权限系统可能有问题: {e}")
    
    # 3. 检查URL配置
    try:
        url = reverse('organizations:admin:anonymous_code_batch_delete')
        print(f"✅ URL配置正确: {url}")
    except Exception as e:
        issues.append(f"URL路由配置有问题: {e}")
    
    if issues:
        print("\n发现以下问题:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
    else:
        print("\n✅ 没有发现明显的后端问题")
    
    # 给出建议
    print("\n🔍 建议检查:")
    print("1. 确保选择了有新匿名编号的员工（绿色背景的编号）")
    print("2. 确保当前用户有管理员权限")
    print("3. 检查浏览器控制台是否有JavaScript错误")
    print("4. 检查网络请求是否正常发送")
    print("5. 检查CSRF token是否正确")

if __name__ == "__main__":
    print("匿名编号批量删除功能诊断")
    print("=" * 60)
    
    test_staff_data()
    test_url_routing()
    diagnose_issue()
    
    print("\n" + "=" * 60)
    print("诊断完成")
    print("=" * 60)
