"""
Django settings for UniversalStaffEvaluation3 project.

Generated by 'django-admin startproject' using Django 5.2.4.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-w4yqqt^)e5ym@er+9_g%!ecn#+hz1qeo+9refr59y1vejlh_sd'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'testserver']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',         # 保留但不使用（避免依赖错误）
    'django.contrib.auth',          # 基础认证功能
    'django.contrib.contenttypes',  # 权限系统需要
    'django.contrib.sessions',      # 会话管理
    'django.contrib.messages',      # 消息框架
    'django.contrib.staticfiles',   # 静态文件
    
    # 本项目应用
    'common',          # 公共组件（基础模型）
    'organizations',   # 组织架构 + 认证管理
    'evaluations',     # 考评核心业务
    'reports',         # 报告和分析
    'communications',  # 站内通信
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',  # 需要保留，基础认证中间件
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    
    # JWT安全认证中间件（替换原有认证中间件）
    'common.security.middleware.JWTAuthenticationMiddleware',    # JWT认证
    'common.security.middleware.SecurityHeadersMiddleware',     # 安全头
    'common.security.middleware.AuthRequiredMiddleware',        # 认证要求检查
    'common.security.middleware.TokenRefreshMiddleware',        # Token自动刷新
    
    # 全局异常处理中间件
    'common.middleware.exceptions.SecurityEventMiddleware',     # 安全事件监控（优先级高）
    'common.middleware.exceptions.GlobalExceptionMiddleware',   # 全局异常处理
]

ROOT_URLCONF = 'UniversalStaffEvaluation3.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates']
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'UniversalStaffEvaluation3.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

# 数据库配置 - MySQL
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'universal_staff_evaluation3',
        'USER': 'root',
        'PASSWORD': 'root',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]
STATIC_ROOT = BASE_DIR / 'staticfiles'

# 媒体文件设置
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# ====================  JWT和缓存配置  ====================

# 缓存配置（用于JWT token管理）
# 注意：开发环境使用本地内存缓存，生产环境建议使用Redis
# 生产环境配置示例：
# 生产环境推荐使用Redis缓存
# CACHES = {
#     'default': {
#         'BACKEND': 'django_redis.cache.RedisCache',
#         'LOCATION': 'redis://127.0.0.1:6379/1',
#         'OPTIONS': {
#             'CLIENT_CLASS': 'django_redis.client.DefaultClient',
#         },
#         'KEY_PREFIX': 'staff_eval',
#         'TIMEOUT': 604800,  # 7天
#     }
# }

# 开发环境使用内存缓存（会在服务器重启时丢失）
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
        'TIMEOUT': 604800,  # 7天（与refresh token生命周期一致）
        'OPTIONS': {
            'MAX_ENTRIES': 10000,
            'CULL_FREQUENCY': 3,
        }
    }
}

# JWT配置
JWT_SETTINGS = {
    'ACCESS_TOKEN_LIFETIME': 8 * 3600,      # 8小时（秒）
    'REFRESH_TOKEN_LIFETIME': 7 * 24 * 3600,  # 7天（秒）
    'ALGORITHM': 'HS256',
    'ROTATE_REFRESH_TOKENS': True,          # 刷新时是否轮换refresh token
    'BLACKLIST_AFTER_ROTATION': True,      # 轮换后是否将旧token加入黑名单
}

# 账户安全配置
ACCOUNT_SECURITY = {
    'MAX_FAILED_ATTEMPTS': 50,              # 最大失败尝试次数
    'LOCKOUT_DURATION_MINUTES': 30,        # 锁定持续时间（分钟）
    'PASSWORD_EXPIRE_DAYS': 90,            # 密码过期天数
    'REQUIRE_PASSWORD_CHANGE': False,      # 是否强制修改初始密码
    'ENABLE_TWO_FACTOR': False,            # 是否启用双因子认证（预留）
}

# 会话配置（兼容性支持）
SESSION_COOKIE_AGE = 8 * 3600              # 8小时
SESSION_EXPIRE_AT_BROWSER_CLOSE = True     # 浏览器关闭时过期
SESSION_SAVE_EVERY_REQUEST = True          # 每次请求都保存session

# 安全配置增强
SECURE_BROWSER_XSS_FILTER = True          # XSS过滤
SECURE_CONTENT_TYPE_NOSNIFF = True        # 防止MIME类型嗅探
X_FRAME_OPTIONS = 'DENY'                  # 防止点击劫持
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# CSRF配置
CSRF_COOKIE_SECURE = False                # 生产环境中应设为True（HTTPS）
CSRF_COOKIE_HTTPONLY = False             # 允许JavaScript访问CSRF cookie（用于AJAX请求）
CSRF_COOKIE_SAMESITE = 'Lax'             # CSRF cookie SameSite设置
CSRF_COOKIE_NAME = 'csrftoken'           # CSRF cookie名称
CSRF_HEADER_NAME = 'HTTP_X_CSRFTOKEN'    # CSRF header名称

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'django.log',
            'formatter': 'verbose',
        },
        'security_file': {
            'level': 'WARNING',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'security.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'common.security': {
            'handlers': ['security_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'organizations': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# ====================  匿名编号安全配置  ====================

# 匿名编号加密盐（生产环境请修改为复杂的随机字符串）
ANONYMOUS_CODE_SALT = 'UniversalStaffEval2025_AnonymousCodeSalt_SecureKey_V2.0'

# 匿名编号安全配置
ANONYMOUS_CODE_SETTINGS = {
    'ALGORITHM': 'SHA256',                    # 哈希算法
    'HASH_ROUNDS': 3,                         # 哈希轮数
    'CODE_LENGTH': 12,                        # 编号长度
    'SEGMENT_LENGTH': 4,                      # 每段长度
    'SEGMENTS_COUNT': 3,                      # 段数
    'MAX_GENERATION_ATTEMPTS': 100,          # 最大生成尝试次数
    'ENABLE_OLD_CODE_COMPATIBILITY': True,   # 启用旧编号兼容性
    'OLD_CODE_DEPRECATION_DAYS': 30,        # 旧编号废弃天数
}

# 安全验证配置
SECURITY_VALIDATION = {
    'ENABLE_FORMAT_VALIDATION': True,        # 启用格式验证
    'ENABLE_STRENGTH_CHECK': True,          # 启用强度检查
    'ENABLE_UNIQUENESS_CHECK': True,        # 启用唯一性检查
    'ENABLE_PATTERN_DETECTION': True,       # 启用模式检测
    'MIN_STRENGTH_SCORE': 75,               # 最低强度分数
}

# 安全事件监控配置
SECURITY_MONITORING = {
    'LOG_FAILED_ATTEMPTS': True,             # 记录失败尝试
    'LOG_OLD_CODE_USAGE': True,              # 记录旧编号使用
    'LOG_SECURITY_EVENTS': True,             # 记录安全事件
    'ALERT_THRESHOLD': 10,                   # 告警阈值（每小时失败次数）
    'ENABLE_IP_TRACKING': True,              # 启用IP追踪
}

# 认证相关URL配置
LOGIN_URL = '/admin/login/'                  # 登录页面URL
LOGIN_REDIRECT_URL = '/admin/'               # 登录后重定向URL
LOGOUT_REDIRECT_URL = '/admin/login/'        # 退出后重定向URL
