#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查模板文件中的static标签使用情况
查找使用了{% static %}但没有加载{% load static %}的文件
"""

import os
import re

def check_static_tags():
    """检查所有模板文件的static标签使用情况"""
    print("=== 检查模板文件中的static标签使用情况 ===\n")
    
    template_dirs = ['templates']
    issues_found = []
    files_checked = 0
    
    for template_dir in template_dirs:
        dir_path = os.path.join(os.path.dirname(__file__), template_dir)
        if os.path.exists(dir_path):
            for root, dirs, files in os.walk(dir_path):
                for file in files:
                    if file.endswith('.html'):
                        file_path = os.path.join(root, file)
                        files_checked += 1
                        
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                
                                # 检查是否使用了{% static %}标签
                                static_usage = re.findall(r'{%\s*static\s+[^%]+%}', content)
                                
                                if static_usage:
                                    # 检查是否有{% load static %}
                                    has_load_static = re.search(r'{%\s*load\s+static\s*%}', content)
                                    
                                    if not has_load_static:
                                        rel_path = os.path.relpath(file_path, os.path.dirname(__file__))
                                        issues_found.append({
                                            'file': rel_path,
                                            'static_usages': len(static_usage),
                                            'examples': static_usage[:3]  # 只显示前3个例子
                                        })
                                        
                        except Exception as e:
                            print(f"⚠️  无法读取文件 {file_path}: {str(e)}")
    
    print(f"📊 检查了 {files_checked} 个模板文件")
    
    if issues_found:
        print(f"\n❌ 发现 {len(issues_found)} 个文件使用了static标签但没有加载static标签库:")
        for issue in issues_found:
            print(f"\n📁 {issue['file']}")
            print(f"   使用次数: {issue['static_usages']}")
            print(f"   示例: {issue['examples']}")
            print(f"   修复方法: 在文件开头添加 {{% load static %}}")
        return False
    else:
        print("\n✅ 所有使用static标签的模板文件都正确加载了static标签库")
        return True

def fix_static_tags():
    """自动修复缺少{% load static %}的文件"""
    print("\n=== 自动修复static标签问题 ===\n")
    
    template_dirs = ['templates']
    fixed_files = []
    
    for template_dir in template_dirs:
        dir_path = os.path.join(os.path.dirname(__file__), template_dir)
        if os.path.exists(dir_path):
            for root, dirs, files in os.walk(dir_path):
                for file in files:
                    if file.endswith('.html'):
                        file_path = os.path.join(root, file)
                        
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                
                            # 检查是否使用了{% static %}标签
                            static_usage = re.findall(r'{%\s*static\s+[^%]+%}', content)
                            
                            if static_usage:
                                # 检查是否有{% load static %}
                                has_load_static = re.search(r'{%\s*load\s+static\s*%}', content)
                                
                                if not has_load_static:
                                    # 自动添加{% load static %}
                                    lines = content.split('\n')
                                    
                                    # 找到合适的位置插入{% load static %}
                                    insert_position = 0
                                    
                                    # 如果有{% extends %}，在其后插入
                                    for i, line in enumerate(lines):
                                        if re.search(r'{%\s*extends\s+', line):
                                            insert_position = i + 1
                                            break
                                    
                                    # 插入{% load static %}
                                    lines.insert(insert_position, '{% load static %}')
                                    
                                    # 写回文件
                                    new_content = '\n'.join(lines)
                                    with open(file_path, 'w', encoding='utf-8') as f:
                                        f.write(new_content)
                                    
                                    rel_path = os.path.relpath(file_path, os.path.dirname(__file__))
                                    fixed_files.append(rel_path)
                                    print(f"✅ 修复: {rel_path}")
                                        
                        except Exception as e:
                            print(f"⚠️  无法处理文件 {file_path}: {str(e)}")
    
    if fixed_files:
        print(f"\n🎉 成功修复了 {len(fixed_files)} 个文件")
    else:
        print("\n✅ 没有需要修复的文件")
    
    return fixed_files

if __name__ == '__main__':
    # 先检查问题
    has_issues = not check_static_tags()
    
    if has_issues:
        print("\n" + "="*50)
        response = input("是否自动修复这些问题？(y/n): ")
        if response.lower() in ['y', 'yes']:
            fix_static_tags()
            print("\n" + "="*50)
            print("重新检查修复结果:")
            check_static_tags()
    
    print("\n检查完成！")
